#ifndef _TX_DMS_WARNING_H_
#define _TX_DMS_WARNING_H_

//此处只定义内部报警信息

typedef struct tired_ {
    float close_eye_ratio;                   //闭眼占比(60s时间占比)
    long close_eye_continue_time;            //闭眼持续时长(ms)
    int close_eye_60s_2count;                //60s闭眼2次数,单次闭眼时长0.6s
    long close_eye_60s_first_continue_time;  //60s第一次持续闭眼时长,单次闭眼时长0.6s
    long close_eye_60s_second_continue_time;  //60s第二次持续闭眼时长,单次闭眼时长0.6s
    int close_eye_60s_1200_2count;            //60s闭眼2次数,单次闭眼时长1.2s
    long close_eye_60s_1200_first_continue_time;  //60s第一次持续闭眼时长,单次闭眼时长1.2s
    long close_eye_60s_1200_second_continue_time;  //60s第二次持续闭眼时长,单次闭眼时长1.2s

    int yawn_count;                        //打哈欠次数
    long first_open_mouth_continue_time;   //第一次张嘴持续时长(ms)
    long second_open_mouth_continue_time;  //第二次张嘴持续时长(ms)
    long three_open_mouth_continue_time;   //第三次张嘴持续时长(ms)
} tx_tired;

typedef struct internal_analysis_distraction_info_ {
    long distraction_continue_time;        //长时间分心持续时长(ms)
    float distraction_continue_percent;    //长时间分心百分比
    long distraction_sum_time;             //短时分心累计时长(ms)
    long distraction_front_continue_time;  //回正持续时长(ms)
    long time_gap;                         //累计的帧数据的时间跨度(ms)

} internal_analysis_distraction_info;

#endif