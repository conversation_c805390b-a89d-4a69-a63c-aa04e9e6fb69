#ifndef _DMS_CALCULATE_WARNING_H_
#define _DMS_CALCULATE_WARNING_H_
#include <algorithm>
#include <chrono>
#include <deque>
#include <iostream>
#include "tx_dms_sdk.h"
namespace tongxing {
// DMS报警类型
typedef enum WarningType_ {
    NORMAL = 0,                   //正常
    DROWSINESS_LEVEL_LIGHT = 2,   //轻度疲劳
    DROWSINESS_LEVEL_MEDIUM = 3,  //中度疲劳
    DROWSINESS_LEVEL_HEAVEY = 4,  //重度疲劳

} WarningType;

// 报警升降级类型
typedef enum RelegationType_ {
    //降级
    DOWN_LEVEL = 0,
    //平级
    SAME_LEVEL = 1,
    //升级
    UP_LEVEL = 2
} RelegationType;

// 报警类型结构体
typedef struct Warn_Info_ {
    // 闭眼状态
    bool eye_close_status;
    // 张嘴状态
    bool mouth_open_status;
    // ldw状态
    bool ldw_status;
    //输入时间点
    long time_input;

} Warn_Info;

class DrowsinessWarn {
  public:
    DrowsinessWarn();
    ~DrowsinessWarn();
     DrowsinessWarn(int eye_3stime_window,
                                   int eye_20stime_window,
                                   int eye_60stime_window,
                                   int mouth_stime_window,
                                   int fps_num);

    //更新报警信息
    void Update(const Warn_Info& info);

    //获取报警类型
    WarningType GetWarnStatus(TXWarnInfo &warn_info);

    //重置所有报警
    void Reset();

    // 获取时间点间隔-毫秒
    long getMsec(const long begin, const long end);

    bool GetEyeCloseThreshold(double thres);
    bool GetYawnCount(const std::deque<bool>& mouth_result, const int count);
    bool GetCloseEyeContinue(const std::deque<bool>& eye_result, const double intrval_time);
    bool GetCloseEyeCoutinueCounts(const int count, const double intrval_time);
    bool GetYawnCountAndLdw(const int count, const int LDW_count);
    bool GetCloseEyeCoutinueCountsAndLdw(const int count,
                                         const double intrval_time,
                                         const int LDW_count);
    void correctEyeState(std::deque<bool> &eye_statem,int digit);

  private:
    //fps设置
    int fps = 30;

    //60s闭眼时间窗占比
    long eye_60time_window = 60*1000;
    double eye_close_60_threshold = 0.1;    //等级2
    double eye_close_60_threshold2 = 12.5;  //等级3

    long mouth_time_window = 60*1000;  //嘴巴时间窗
    long eye_20time_window = 20*1000;   //20s时间窗用来保存连续性计算
    long eye_3time_window = 3*1000;     //单次持续闭眼窗--1.5 2.4

    //存放数据记录
    std::deque<bool> eye_1min_result;  //1min内所有眼睛状态数据

    std::deque<bool> mouth_2min_result;  //2min内所有嘴巴状态数据

    std::deque<bool> eye_20s_result;  //20s内所有眼睛状态数据
    std::deque<bool> eye_3s_result;   //3s内所有眼睛状态数据,主要用于持续闭眼

    std::deque<bool> ldw_20s_result;   //20s内所有ldw状态数据+eye
    std::deque<bool> ldw_120s_result;  //20s内所有ldw状态数据+mouth

    std::deque<bool> mouth_1min_result;  //1min内所有嘴巴状态数据--用于降级判断
    std::deque<bool> eye_1min_result2;   //1min内所有眼睛状态数据--用于降级判断

    //重置时间窗时间点
    long time_5min_start;
    long time_5min_end;
    bool time_5min_flag = false;
    // 时间窗时间点
    long time_eye_1min_start;
    long time_eye_1min_end;
    bool time_eye_1min_flag = false;
    bool time_eye_1min_arrived = false;

    long time_eye_20s_start;
    long time_eye_20s_end;
    bool time_eye_20s_flag = false;
    bool time_eye_20s_arrived = false;

    long time_eye_3s_start;
    long time_eye_3s_end;
    bool time_eye_3s_flag = false;
    bool time_eye_3s_arrived = false;

    long time_mouth_2min_start;
    long time_mouth_2min_end;
    bool time_mouth_2min_flag = false;
    bool time_mouth_2min_arrived = false;

    //历史报警类型，持续8s输出使用
    WarningType history_warn_type = NORMAL;
    WarningType history_8s_warn_type = NORMAL;

    //缓存时间点
    long time;

    //时间计时关键数据
    long warn_time_start;
    long warn_time_end;
    long warn_1min_time_start;
    long warn_1min_time_end;

    //标志位报警
    // bool warn_flag = false;
    bool first_warn_flag = false;

    bool level_4_flag = false;  //降级等级4标志


    long sustain_warn_time = 8*1000;
    long sustain_1min_time = 60*1000;
    long sustain_5min_time = 60*1000*5;


    //降级时间点
    long jiangji_warn_1min_time_start;
    long jiangji_warn_1min_time_end;

    long jiangji_warn_8s_time_start;
    long jiangji_warn_8s_time_end;
};
}
#endif