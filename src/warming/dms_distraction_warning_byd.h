#ifndef _DMS_DISTRACTION_WARNING_BYD_H_
#define _DMS_DISTRACTION_WARNING_BYD_H_
#include <algorithm>
#include <chrono>
#include <cmath>
#include <deque>
#include <iostream>
#include <vector>
#include "HeadPoseCalibrator.h"
#include "tx_dms_sdk.h"
#include "tx_dms_warning.h"
namespace tongxing {
// 此类addw报警逻辑

// addw报警类型
typedef enum DistractionType_ {
    DISTRACTION_NORMAL = 0,       //正常
    DISTRACTION_FATIGUE1 = 1,     //分心1（长时分心、addw）
    DISTRACTION_FATIGUE2 = 2,     //分心2(短时分心)
    DISTRACTION_NO_RESPONSE = 3,  //无响应状态
} DistractionType;

// 报警类型信息结构体
typedef struct Distraction_Info_ {
    // 分神状态
    bool distraction_status;
    //输入时间点
    long time_input;

} Distraction_Info;

typedef struct distraction_warning_param_ {
    float head_yaw_min;
    float head_yaw_max;
    float head_pitch_min;
    float head_pitch_max;
    float right_gaze_vf_yaw_min;
    float right_gaze_vf_yaw_max;
    float right_gaze_vf_pitch_min;
    float right_gaze_vf_pitch_max;
    float left_gaze_vf_yaw_min;
    float left_gaze_vf_yaw_max;
    float left_gaze_vf_pitch_min;
    float left_gaze_vf_pitch_max;

    float current_right_gaze_vf_yaw;
    float current_right_gaze_vf_pitch;
    float current_left_gaze_vf_yaw;
    float current_left_gaze_vf_pitch;

    float head_yaw_bias;
} distraction_warning_param;

class DistractionWarn final {
  public:
    DistractionWarn();
    ~DistractionWarn();

    //更新报警信息
    void Update(const Distraction_Info& info);

    //获取报警类型
    DistractionType GetWarnStatus(int speed);

    //重置所有报警
    void Reset();

    //收到确认OK信号
    void SetOk();

    //自动标定
    int auto_calibration_distraction(TXDmsFaceInfo face_info,
                                     const TXCarInfo* car_info,
                                     long now_ts);

    void ClearCalibration();      //清除标定
    bool GetCalibrationStatus();  //获取标定状态
    bool IsDistracted(TXDmsFaceInfo face_info,
                      const TXCarInfo* car_info,
                      float right_up_down_proportion,
                      float left_up_down_proportion,
                      float face_angle_score,
                      bool camera_occlusion,
                      long now_ts);                             //判断是否分神状态
    void GetHeadPosePitch(float& min_value, float& max_value);  //获取头部姿态标定pitch角度
    void GetHeadPoseRoll(float& min_value, float& max_value);  //获取头部姿态标定后roll角度
    void GetHeadPoseYaw(float& min_value, float& max_value);
    std::string GetDistractReason() { return distraction_reason; }
    bool GetHeadPoseGazeShake(TXDmsFaceInfo face_info) { return false; }
    std::string GetDistractParamers();

    void GetDistractionInfo(internal_analysis_distraction_info& info);

  private:
    int car_speed_ = 70;  //报警生效车速 //占时未使用上

    std::shared_ptr<HeadPoseCalibrator> calibrator;

    // std::deque<std::pair<long, bool>> distraction_30s_result;  //30s状态数据队列

    // bool time_6s_flag = false;  //6s时间窗标准位
    // long time_6s_start = 0;
    // long time_6s_end = 0;

    // bool time_6s_arrived = false;

    bool ok_flag = false;
    DistractionType histor_warn_type = DISTRACTION_NORMAL;  //历史结果

    //报警计时时间
    long alarm_start_time = 0;
    long alarm_end_time = 0;

    long alarm_ok_start_time = 0;
    long alarm_ok_end_time = 0;

    //缓存时间点
    long cache_time;

    std::deque<std::pair<long, bool>> distraction_short_result;  //短时状态数据队列
    std::deque<std::pair<long, bool>> distraction_long_result;   //长时状态数据队列
    std::deque<std::pair<long, bool>> distraction_3s_result;     //3s状态数据队列

    std::deque<std::pair<long, float>> head_yaw_3s_vec;  //存放头部yaw缓存数据
    std::deque<bool> glasses_vec;                        // 存放戴眼镜状态
    std::deque<float> temp_vec;

    // bool time_3s_arrived = false;
    // bool time_3s_flag = false;  //6s时间窗标准位
    // long time_3s_start = 0;
    // long time_3s_end = 0;

    internal_analysis_distraction_info distraction_info;  //分神分析信息（及用于json分析）
    long GetContinueDistractionTime(
        const std::deque<std::pair<long, bool>> distraction_deque);  //获取持续分神时间
    float GetContinueDistractionPercent(const std::deque<std::pair<long, bool>> distraction_deque,
                                        long time_gap);  //获取长时分神时间百分比
    long GetSumDistractionTime(
        const std::deque<std::pair<long, bool>> distraction_deque);  //获取累计分神时间
    long GetContinueFrontTime(
        const std::deque<std::pair<long, bool>> distraction_deque);  //获取持续正视前方时间

    // 获取时间毫秒
    long getMsec(const long begin, const long end) { return end - begin; }
    // 获取时间秒
    long getSec(const long begin, const long end) { return (end - begin) * 1.0 / 1000; }

    bool GetResult(std::deque<std::pair<long, bool>>& distraction_deque, long time_gap);
    bool GetShortTimeResult(std::deque<std::pair<long, bool>>& distraction_deque,
                            long interval_times);
    bool GetDegradeResult(std::deque<std::pair<long, bool>>& distraction_deque);

    bool GetDegradeResult2(std::deque<std::pair<long, bool>>& distraction_deque, int cnt);

    void GetDataQueue(std::deque<std::pair<long, bool>>& input_distraction_deque,
                      std::deque<std::pair<long, bool>>& out_distraction_deque,
                      long time_gap);

    //分神标定
    bool read_json = false;   // 读取json文件，本地调试模式
    bool log_switch = false;  //日志开关

    float headpose_yaw_left = 0.0f;
    float headpose_yaw_right = 0.0f;
    float headpose_pitch_up = 0.0f;
    float headpose_pitch_down = 0.0f;

    float headpose_spe_glasses_yaw_left = 0.0f;
    float headpose_spe_glasses_yaw_right = 0.0f;
    float headpose_spe_glasses_pitch_up = 0.0f;
    float headpose_spe_glasses_pitch_down = 0.0f;

    float right_gaze_vision_fusion_yaw_left = 0.0f;
    float right_gaze_vision_fusion_yaw_right = 0.0f;
    float right_gaze_vision_fusion_pitch_up = 0.0f;
    float right_gaze_vision_fusion_pitch_down = 0.0f;
    float left_gaze_vision_fusion_yaw_left = 0.0f;
    float left_gaze_vision_fusion_yaw_right = 0.0f;
    float left_gaze_vision_fusion_pitch_up = 0.0f;
    float left_gaze_vision_fusion_pitch_down = 0.0f;

    //视线往下绝对值
    float righteye_up_down_proportion = 0.8;
    float lefteye_up_down_proportion = 0.8;

    float head_yaw_bias;
    float head_yaw_3s_min;
    float head_yaw_3s_max;
    int head_yaw_bias_window_time = 3000;

    //标定角度参数
    float calibrate_headpose_yaw_min;
    float calibrate_headpose_yaw_max;
    float calibrate_headpose_pitch_min;
    float calibrate_headpose_pitch_max;
    float calibrate_headpose_roll_min;
    float calibrate_headpose_roll_max;

    //融合视线最值
    float right_gaze_vision_fusion_yaw_min;
    float right_gaze_vision_fusion_yaw_max;
    float right_gaze_vision_fusion_pitch_min;
    float right_gaze_vision_fusion_pitch_max;
    float left_gaze_vision_fusion_yaw_min;
    float left_gaze_vision_fusion_yaw_max;
    float left_gaze_vision_fusion_pitch_min;
    float left_gaze_vision_fusion_pitch_max;
    /////////

    //根据raw角范围过滤roll误报分神
    float head_yaw_filter_min;
    float head_yaw_filter_max;
    float head_roll_filter_min;
    float head_roll_filter_max;

    float head_yaw_filter_left_offset;
    float head_yaw_filter_right_offset;
    float head_roll_filter_left_offset;
    float head_roll_filter_right_offset;
    ///

    int window_time = 3000;       //分心时间窗
    float distraction_thr = 0.8;  //分心阈值

    float pitch_down = 0.0f;  //临时过滤低头误报闭眼pitch
    float pitch_up = 0.0f;    //临时过滤低头误报闭眼pitch
    float yaw_left = 0.0f;    //临时过滤偏头误报闭眼yaw 偏差值
    float yaw_right = 0.0f;   //临时过滤偏头误报闭眼yaw 偏差值
    float roll_left = 0.0f;   //临时过滤偏头误报分神roll
    float roll_right = 0.0f;  //临时过滤偏头误报分神roll

    float headpose_pitch_threshold;  //头部姿态pitch角度阈值
    float gaze_pitch_threshold;      //gaze pitch角度阈值

    float headpose_yaw_threshold;  //头部姿态yaw角度阈值
    float gaze_yaw_threshold;      //gaze yaw角度阈值

    float headgaze_yaw_l_offset;  //视线融合yaw 区间偏差值
    float headgaze_yaw_r_offset;  //视线融合yaw 区间偏差值

    float headpose_yaw_normal_min = 0.0f;    //headpose正常yaw角度
    float headpose_yaw_normal_max = 0.0f;    //headpose正常yaw角度
    float headpose_pitch_normal_min = 0.0f;  //headpose正常pitch角度
    float headpose_pitch_normal_max = 0.0f;  //headpose正常pitch角度
    float headpose_roll_normal_min = 0.0f;   //headpose正常roll角度
    float headpose_roll_normal_max = 0.0f;   //headpose正常roll角度
    float headpose_yaw_variance;             //headpose yaw角方差阈值
    float headpose_pitch_variance;           //headpose pitch角方差阈值
    float headpose_roll_variance;            //headpose roll角方差阈值
    float gaze_yaw_variance;                 //gaze yaw方差
    float gaze_pitch_variance;               //gaze pitch方差
    float gaze_yaw_normal_min = 0.0f;        //gaze 正常yaw角度
    float gaze_yaw_normal_max = 0.0f;        //gaze 正常yaw角度
    float gaze_pitch_normal_min = 0.0f;      //gaze 正常pitch角度
    float gaze_pitch_normal_max = 0.0f;      //gaze 正常pitch角度

    int fusion_use_eye = 1;                              // 0:left eye 1:right eye 2:both eye
    int region_mapping_width = 800;                      //区域映射宽度
    int region_mapping_height = 800;                     //区域映射高度
    float tolerate_percentage = 0.005f;                  //容忍度百分比
    std::vector<std::vector<cv::Point2f>> region_hulls;  //区域轮廓点集
    int predict_result = 0;
    float mapping_x = 0.0f;
    float mapping_y = 0.0f;
    float gaze_pitch_mean = 0.0f;
    float gaze_yaw_mean = 0.0f;

    float car_speed_variance;  //车速方差（用于判断标定过程中急加速减速）
    float steering_wheel_angle_min = 0.0f;  //方向盘转角最小值
    float steering_wheel_angle_max = 0.0f;  //方向盘转角最大值

    int calibrate_time = 5000;

    bool auto_calibration = false;  //分神自动标定状态

    bool left_eye_pupil_visible = true;  //标定时左眼瞳孔是否可见
    std::vector<float> left_eye_pupil_yaw_vec;
    std::vector<float> left_eye_pupil_pitch_vec;
    bool right_eye_pupil_visible = true;  //标定时右眼瞳孔是否可见
    std::vector<float> right_eye_pupil_yaw_vec;
    std::vector<float> right_eye_pupil_pitch_vec;

    float temp_headpose_pitch_mean;
    float temp_headpose_yaw_mean;
    float temp_headpose_roll_mean;

    float temp_lefteye_gaze_pitch_mean;
    float temp_lefteye_gaze_yaw_mean;
    float temp_righteye_gaze_pitch_mean;
    float temp_righteye_gaze_yaw_mean;
    bool has_head_cali = false;
    bool has_leye_cali = false;
    bool has_reye_cali = false;
    calistatus status;
    bool cali_status_clear = false;

    void StartCalibration(float head_pose_yaw,
                          float head_pose_pitch,
                          float head_pose_roll,
                          float gaze_left_yaw,
                          float gaze_left_pitch,
                          float gaze_right_yaw,
                          float gaze_right_pitch,
                          float left_eye_conf,
                          float right_eye_conf,
                          int car_speed);  //开始标定

    long star_ts = 0;                     //开始时间戳
    long end_ts = 0;                      //结束时间戳
    float headpose_yaw_ = 0.0f;           //head pose yaw
    float headpose_pitch_ = 0.0f;         //head pose pitch
    float headpose_roll_ = 0.0f;          //head pose roll
    float gaze_left_eye_yaw_ = 1000.0f;   //gaze left eye yaw
    float gaze_left_eye_pitch_ = 0.0f;    //gaze left eye pitch
    float gaze_right_eye_yaw_ = 1000.0f;  //gaze right eye yaw
    float gaze_right_eye_pitch_ = 0.0f;   //gaze right eye pitch
    std::vector<float> headpose_yaw_vec;
    std::vector<float> headpose_pitch_vec;
    std::vector<float> headpose_roll_vec;
    std::vector<float> gaze_left_eye_yaw_vec;
    std::vector<float> gaze_right_eye_yaw_vec;
    std::vector<float> gaze_left_eye_pitch_vec;
    std::vector<float> gaze_right_eye_pitch_vec;
    std::vector<float> car_speed_vec;

    bool current_distraction_status = false;  //分神状态

    std::deque<float> right_gaze_yaw_vec;    //右眼gaze yaw 结果窗
    std::deque<float> right_gaze_pitch_vec;  //右眼gaze pitch 结果窗
    std::deque<float> left_gaze_yaw_vec;     //左眼gaze yaw 结果窗
    std::deque<float> left_gaze_pitch_vec;   //左眼gaze pitch 结果窗

    //历史视线区域结果
    float last_gaze_yaw = 1000.0f;
    float last_gaze_pitch = 1000.0f;

    std::deque<float> gaze_mapping_point;  //存储注视区域坐标 结果窗
    cv::Point2f mapping_point_old{0, 0};
    bool gaze_mapping_point_flag = true;
    // int gaze_mapping_point_diff = 0;

    //正常开车方向盘转角范围
    float wheel_rad_normal_min;  //方向盘转角最小值
    float wheel_rad_normal_max;  //方向盘转角最大值
    bool region_init_flag = false;
    bool is_loop_calibration = true;
    //定义一个说明分神由headpose还是gaze触发，用于内部json保存使用
    std::string distraction_reason;
    //存放每一帧分神合理区间参数
    distraction_warning_param distract_param;
};

}  // namespace tongxing
#endif