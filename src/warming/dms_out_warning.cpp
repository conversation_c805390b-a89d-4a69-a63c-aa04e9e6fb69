#include "dms_out_warning.h"
#include "CalmCarLog.h"

namespace tongxing {
dms_out_warning::dms_out_warning(/* args */) {
    Dwarn = new DrowsinessWarn(70, 60, 60, 60);
    Distrac = new DistractionWarn();
}

dms_out_warning::~dms_out_warning() {
    ;
}

int dms_out_warning::dms_init_warning() {
    ;
    return 0;
}

int dms_out_warning::output_model_bool(const TXModelOut* model_out,
                                       Warn_Info& warn_info_,
                                       TXDmsFaceInfo face_info) {
    //当两只眼瞳孔都不可见时，再计算闭眼状态
    // if (face_info.left_eye_landmark.pupil_score == 0 &&
    //     face_info.right_eye_landmark.pupil_score == 0) {
    if ((model_out->l_eye_close >= l_eye_cloes_threshold && 
            model_out->r_eye_close >= r_eye_cloes_threshold) ||
        (model_out->l_eye_close >= l_eye_cloes_threshold && 
            (face_info.right_eye_landmark.eye_score == 0 && 
            face_info.right_eye_landmark.pupil_score == 0)) || 
        (model_out->r_eye_close >= r_eye_cloes_threshold && 
            (face_info.left_eye_landmark.eye_score == 0 && 
            face_info.left_eye_landmark.pupil_score == 0))) { //单只眼睛可见时，只计算单只眼睛的闭合度
            warn_info_.eye_close_status = true;
        } else {
            warn_info_.eye_close_status = false;
        }
    // } else {
    //     warn_info_.eye_close_status = false;
    // }

    if (model_out->mouth_open >= yawn_opne_threshold) {
        warn_info_.mouth_open_status = true;
    } else {
        warn_info_.mouth_open_status = false;
    }

    if (model_out->mouth_open >= 0.2) {
        //打哈欠时不能算闭眼（打哈欠时不统计闭眼）//笑时眯眼误报闭眼
        warn_info_.eye_close_status = false;
    }
    
    return 0;
}

TXDistractionType dms_out_warning::distrac_run_warning(const long time,
                                                       const TXCarInfo* car_info,
                                                       bool status) {
    Distraction_Info warn_info;
    warn_info.time_input = time;
    if (car_info->speed < 20 || car_info->gear != TXGearPition::FORWARD ||
        car_info->turn_light != TXTurnSignal::TURN_OFF) {
        warn_info.distraction_status = false;  //车速未达到，当前不可为分心状态
    } else {
        warn_info.distraction_status = status;
    }

    Distrac->Update(warn_info);
    DistractionType warn_type = Distrac->GetWarnStatus(car_info->speed);
    // std::cout<<"warn_info.distraction_status="<<warn_info.distraction_status<<" warn_type="<<warn_type<<" car_info->speed="<<car_info->speed<<std::endl;
    TXDistractionType warning_type;

    switch (warn_type) {
        case DISTRACTION_NORMAL:
            warning_type = Distraction_Normal;
            break;
        case DISTRACTION_FATIGUE1:
            if (car_info->speed >= 20 && (car_info->turn_light == TXTurnSignal::TURN_OFF)) {
                warning_type = Distraction_Fatigue;
            } else {
                warning_type = Distraction_Normal;
            }
            break;
        case DISTRACTION_FATIGUE2:
            if (car_info->speed >= 20 && (car_info->turn_light == TXTurnSignal::TURN_OFF)) {
                warning_type = Distraction_Fatigue_Short;
            } else {
                warning_type = Distraction_Normal;
            }
            break;

        default:
            warning_type = Distraction_Invalid;
            break;
    }

    return warning_type;
}

TXDrowsinessType dms_out_warning::dms_run_warning(const long time,
                                                  TXDmsFaceInfo face_info,
                                                  const TXCarInfo* car_info,
                                                  const TXModelOut* input,
                                                  TXWarnInfo& warn_info_out) {
    Warn_Info warn_info;
    //当车速不满足时，将状态都置为不成立
    if (car_info->speed < 10 || car_info->gear != TXGearPition::FORWARD) {
        warn_info.eye_close_status = false;
        warn_info.mouth_open_status = false;
        warn_info.ldw_status = false;
        warn_info.time_input = time;
    } else {
        output_model_bool(input, warn_info, face_info);

        warn_info.time_input = time;
        warn_info.ldw_status = car_info->ldw;
    }

    // 1.先更新数据
    Dwarn->Update(warn_info);
    // 2.取更新后结果
    // 先计算轻度
    //获取结果

    WarningType warn_type = Dwarn->GetWarnStatus(warn_info_out);

    // CC_LOG_INFO("dms_run_warn", "warn_type:%d ",warn_type);
    TXDrowsinessType warning_type;

    switch (warn_type) {
        case NORMAL:
            warning_type = Drowsiness_Normal;
            break;
        case DROWSINESS_LEVEL_LIGHT:
            warning_type = Drowsiness_Fatigue2;
            break;
        case DROWSINESS_LEVEL_MEDIUM:
            warning_type = Drowsiness_Fatigue3;
            break;
        case DROWSINESS_LEVEL_HEAVEY:
            warning_type = Drowsiness_Fatigue4;
            break;
        case NO_RESPONSE:
            warning_type = Drowsiness_NoResponse;
            break;

        default:
            warning_type = Drowsiness_Invalid;
            break;
    }

    // 计算中度

    return warning_type;
}

int dms_out_warning::DmsRestAlarm() {
    // CC_LOG_INFO("dms_out_warning","----------DmsRestAlarm---------");
    Dwarn->Reset();
    return 0;
}

int dms_out_warning::DmsRestEyeAlarm() {
    // CC_LOG_INFO("dms_out_warning","----------DmsRestAlarm---------");
    Dwarn->Cleareye();
    return 0;
}

int dms_out_warning::DistracRestAlarm() {
    // CC_LOG_INFO("dms_out_warning","----------DmsRestAlarm---------");
    Distrac->Reset();
    return 0;
}

int dms_out_warning::DmsAlarmSetOk() {
    // CC_LOG_INFO("dms_out_warning","----------DmsRestAlarm---------");
    Dwarn->SetOk();
    return 0;
}

void dms_out_warning::ResetCalibration() {
    Distrac->ClearCalibration();
    return;
}

bool dms_out_warning::GetCalibrationStatus() {
    return Distrac->GetCalibrationStatus();
}

int dms_out_warning::auto_calibration_distraction(TXDmsFaceInfo face_info,
                                                  const TXCarInfo* car_info,
                                                  long now_ts) {
    return Distrac->auto_calibration_distraction(face_info, car_info, now_ts);
}

bool dms_out_warning::GetCurrentDistractStatus(TXDmsFaceInfo face_info,
                                               const TXCarInfo* car_info,
                                               float right_up_down_proportion,
                                               float left_up_down_proportion,
                                               float face_angle_score,
                                               bool camera_occlusion,
                                               long now_ts) {
    return Distrac->IsDistracted(face_info, car_info, right_up_down_proportion,
                                 left_up_down_proportion, face_angle_score, camera_occlusion,
                                 now_ts);
}

void dms_out_warning::GetHeadPosePitch(float& min_value, float& max_value) {
    return Distrac->GetHeadPosePitch(min_value, max_value);
}

void dms_out_warning::GetHeadPoseRoll(float& min_value, float& max_value) {
    return Distrac->GetHeadPoseRoll(min_value, max_value);
}

void dms_out_warning::GetHeadPoseYaw(float& min_value, float& max_value) {
    return Distrac->GetHeadPoseYaw(min_value, max_value);
}

std::string dms_out_warning::GetDistractReason() {
    return Distrac->GetDistractReason();
}

std::string dms_out_warning::GetDistractParamers() {
    return Distrac->GetDistractParamers();
}

bool dms_out_warning::GetHeadPoseGazeShake(TXDmsFaceInfo face_info) {
    return Distrac->GetHeadPoseGazeShake(face_info);
}

void dms_out_warning::GetTiredInfo(tx_tired& tired_info) {
    return Dwarn->GetTired(tired_info);
}

void dms_out_warning::GetDistractionInfo(internal_analysis_distraction_info& info) {
    return Distrac->GetDistractionInfo(info);
}

}  // namespace tongxing