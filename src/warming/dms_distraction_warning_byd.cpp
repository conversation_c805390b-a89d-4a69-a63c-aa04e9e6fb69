#include "dms_distraction_warning_byd.h"
#include <math.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <algorithm>
#include <fstream>
#include "CalmCarLog.h"
#include "cc_resource_register.h"
#include "json.h"
#include "svm_model.h"

int WINDOW_TIME;                //分神时间窗
float DISTRACTION_THR;          //分神阈值
int HEAD_YAW_BIAS_WINDOW_TIME;  //head_yaw_bias时间窗

float HEADPOSE_PITCH_THR;  //头部姿态pitch 阈值
float GAZE_PITCH_THR;      //视线 pitch阈值

float HEADPOSE_YAW_THR;  //头部姿态yaw 阈值
float GAZE_YAW_THR;      //视线 yaw阈值

float HEAD_YAW_FILTER_LEFT_OFFSET;    //通过头部YAW角过滤歪头引发分神左偏差值
float HEAD_YAW_FILTER_RIGHT_OFFSET;   //通过头部YAW角过滤歪头引发分神右偏差值
float HEAD_ROLL_FILTER_LEFT_OFFSET;   //通过头部roll角过滤歪头引发分神左偏差值
float HEAD_ROLL_FILTER_RIGHT_OFFSET;  //通过头部roll角过滤歪头引发分神右偏差值

float PITCH_UP;    //临时过滤低头误报闭眼pitch
float PITCH_DOWN;  //临时过滤低头误报闭眼pitch
float YAW_LEFT;    //临时过滤偏头误报闭眼yaw偏差值
float YAW_RIGHT;   //临时过滤偏头误报闭眼yaw偏差值
float ROLL_LIFT;   //临时过滤偏头误报分神roll
float ROLL_RIGHT;  //临时过滤偏头误报分神roll

float CAR_SPEED_VARIANCE;        //标定使用车速方差
float STEERING_WHEEL_ANGLE_MIN;  //标定方向盘转角最小值
float STEERING_WHEEL_ANGLE_MAX;  //标定方向盘转角最大值

float CALIBRATE_HEADPOSE_YAW_NORMAL_MIN;    //标定人脸角度yaw最小值
float CALIBRATE_HEADPOSE_YAW_NORMAL_MAX;    //标定人脸角度yaw最大值
float CALIBRATE_HEADPOSE_PITCH_NORMAL_MIN;  //标定人脸角度pitch最小值
float CALIBRATE_HEADPOSE_PITCH_NORMAL_MAX;  //标定人脸角度pitch最大值
float CALIBRATE_HEADPOSE_ROLL_NORMAL_MIN;   //标定人脸角度roll最小值
float CALIBRATE_HEADPOSE_ROLL_NORMAL_MAX;   //标定人脸角度roll最大值

float HEADPOSE_YAW_NORMAL_MIN;    //正常人脸角度yaw最小值
float HEADPOSE_YAW_NORMAL_MAX;    //正常人脸角度yaw最大值
float HEADPOSE_PITCH_NORMAL_MIN;  //正常人脸角度pitch最小值
float HEADPOSE_PITCH_NORMAL_MAX;  //正常人脸角度pitch最大值
float HEADPOSE_ROLL_NORMAL_MIN;   //正常人脸角度roll最小值
float HEADPOSE_ROLL_NORMAL_MAX;   //正常人脸角度roll最大值
float GAZE_YAW_NORMAL_MIN;        //正常视线yaw最小值
float GAZE_YAW_NORMAL_MAX;        //正常视线yaw最大值
float GAZE_PITCH_NORMAL_MIN;      //正常视线pitch最小值
float GAZE_PITCH_NORMAL_MAX;      //正常视线pitch最大值

float HEAD_POSE_YAW_L;    //head pose yaw角 左方向绝对偏差值
float HEAD_POSE_YAW_R;    //head pose yaw角 右方向绝对偏差值
float HEAD_POSE_PITCH_U;  //head pose pitch 上方向绝对偏差值
float HEAD_POSE_PITCH_D;  //head pose pitch 下方向绝对偏差值

float HEAD_POSE_SPE_GLASSES_YAW_L;  //戴眼镜且眼睛不可见时设置的更多偏差
float HEAD_POSE_SPE_GLASSES_YAW_R;
float HEAD_POSE_SPE_GLASSES_PITCH_U;
float HEAD_POSE_SPE_GLASSES_PITCH_D;

float RIGHT_GAZE_VISION_FUSION_YAW_L;    //右眼融合yaw偏差L
float RIGHT_GAZE_VISION_FUSION_YAW_R;    //右眼融合yaw偏差R
float RIGHT_GAZE_VISION_FUSION_PITCH_U;  //右眼融合pitch偏差U
float RIGHT_GAZE_VISION_FUSION_PITCH_D;  //右眼融合pitch偏差D

float LEFT_GAZE_VISION_FUSION_YAW_L;    //左眼融合yaw偏差L
float LEFT_GAZE_VISION_FUSION_YAW_R;    //左眼融合yaw偏差R
float LEFT_GAZE_VISION_FUSION_PITCH_U;  //左眼融合pitch偏差U
float LEFT_GAZE_VISION_FUSION_PITCH_D;  //左眼融合pitch偏差D

float HEAD_YAW_L_OFFSET;  //视线融合头部姿态偏差值(用于判断是否优先使用视线融合做分心判断)
float HEAD_YAW_R_OFFSET;  //视线融合头部姿态偏差值(用于判断是否优先使用视线融合做分心判断)

float RIGHTEYE_UP_DOWN_PROPORTION;  //视线往下看,右眼绝对值
float LEFTEYE_UP_DOWN_PROPORTION;   //视线往下看,左眼绝对值

float HEAD_YAW_BIAS;  //头部yaw最大最小值的绝对值，用于辅助判断是否注释一个点位，延迟报警

int FUSION_USE_EYE;                                  //融合角度计算所使用的眼睛
int REGION_MAPPING_WIDTH;                            //区域映射宽度
int REGION_MAPPING_HEIGHT;                           //区域映射高度
float TOLERATE_PERCENTAGE;                           //容忍度百分比
std::vector<std::vector<cv::Point2f>> REGION_HULLS;  //区域轮廓点集

static float calculateMean(const std::vector<float>& data) {
    float sum = 0.0;
    for (float value : data) {
        sum += value;
    }
    return sum / data.size();
}

static float calculateMean(const std::deque<float>& data) {
    float sum = 0.0;
    for (float value : data) {
        sum += value;
    }
    return sum / data.size();
}

static float calculateVariance(const std::vector<float>& data, double mean) {
    float variance = 0.0;
    for (float value : data) {
        variance += (value - mean) * (value - mean);
    }
    return variance / data.size();
}

static float calculateVariance(const std::deque<float>& data, double mean) {
    float variance = 0.0;
    for (float value : data) {
        variance += (value - mean) * (value - mean);
    }
    return variance / data.size();
}

namespace tongxing {

DistractionWarn::DistractionWarn() {
    distraction_short_result.clear();
    distraction_long_result.clear();
    distraction_3s_result.clear();
    head_yaw_3s_vec.clear();
    glasses_vec.clear();
    temp_vec.clear();

    //解析分神内置json
    Json::Reader config_json_reader;
    Json::Value config_root;
    auto config_json_data = CcResourcDataRegister::instance().get_function("sight.json");

    std::string config_doc = std::string((char*)config_json_data.second, config_json_data.first);
    if (!config_json_reader.parse(config_doc, config_root)) {
        TX_LOG_FATAL("TX DMS", "DistractionWarn Parse json config file failed!");
        // return -1;
    }
    //分神参数解析如下
    HEADPOSE_PITCH_THR = config_root["headpose_pitch_threshold"].asDouble();
    GAZE_PITCH_THR = config_root["gaze_pitch_threshold"].asDouble();
    HEADPOSE_YAW_THR = config_root["headpose_yaw_threshold"].asDouble();
    GAZE_YAW_THR = config_root["gaze_yaw_threshold"].asDouble();

    WINDOW_TIME = config_root["window_time"].asInt();
    DISTRACTION_THR = config_root["distraction_thr"].asDouble();
    HEAD_YAW_BIAS_WINDOW_TIME = config_root["head_yaw_bias_window_time"].asInt();

    HEAD_YAW_FILTER_LEFT_OFFSET = config_root["head_yaw_filter_left_offset"].asDouble();
    HEAD_YAW_FILTER_RIGHT_OFFSET = config_root["head_yaw_filter_right_offset"].asDouble();
    HEAD_ROLL_FILTER_LEFT_OFFSET = config_root["head_roll_filter_left_offset"].asDouble();
    HEAD_ROLL_FILTER_RIGHT_OFFSET = config_root["head_roll_filter_right_offset"].asDouble();

    CALIBRATE_HEADPOSE_YAW_NORMAL_MIN = config_root["calibrate_headpose_yaw_min"].asDouble();
    CALIBRATE_HEADPOSE_YAW_NORMAL_MAX = config_root["calibrate_headpose_yaw_max"].asDouble();
    CALIBRATE_HEADPOSE_PITCH_NORMAL_MIN = config_root["calibrate_headpose_pitch_min"].asDouble();
    CALIBRATE_HEADPOSE_PITCH_NORMAL_MAX = config_root["calibrate_headpose_pitch_max"].asDouble();
    CALIBRATE_HEADPOSE_ROLL_NORMAL_MIN = config_root["calibrate_headpose_roll_min"].asDouble();
    CALIBRATE_HEADPOSE_ROLL_NORMAL_MAX = config_root["calibrate_headpose_roll_max"].asDouble();

    PITCH_DOWN = config_root["pitch_down"].asDouble();
    PITCH_UP = config_root["pitch_up"].asDouble();
    YAW_LEFT = config_root["yaw_left"].asDouble();
    YAW_RIGHT = config_root["yaw_right"].asDouble();
    ROLL_LIFT = config_root["roll_left"].asDouble();
    ROLL_RIGHT = config_root["roll_right"].asDouble();

    CAR_SPEED_VARIANCE = config_root["car_speed_variance"].asDouble();
    STEERING_WHEEL_ANGLE_MIN = config_root["steering_wheel_angle_min"].asDouble();
    STEERING_WHEEL_ANGLE_MAX = config_root["steering_wheel_angle_max"].asDouble();
    HEADPOSE_YAW_NORMAL_MIN = config_root["headpose_yaw_normal_min"].asDouble();
    HEADPOSE_YAW_NORMAL_MAX = config_root["headpose_yaw_normal_max"].asDouble();
    HEADPOSE_PITCH_NORMAL_MIN = config_root["headpose_pitch_normal_min"].asDouble();
    HEADPOSE_PITCH_NORMAL_MAX = config_root["headpose_pitch_normal_max"].asDouble();
    HEADPOSE_ROLL_NORMAL_MIN = config_root["headpose_roll_normal_min"].asDouble();
    HEADPOSE_ROLL_NORMAL_MAX = config_root["headpose_roll_normal_max"].asDouble();
    GAZE_YAW_NORMAL_MIN = config_root["gaze_yaw_normal_min"].asDouble();
    GAZE_YAW_NORMAL_MAX = config_root["gaze_yaw_normal_max"].asDouble();
    GAZE_PITCH_NORMAL_MIN = config_root["gaze_pitch_normal_min"].asDouble();
    GAZE_PITCH_NORMAL_MAX = config_root["gaze_pitch_normal_max"].asDouble();

    HEAD_POSE_YAW_L = config_root["head_pose_yaw_left_offset"].asDouble();
    HEAD_POSE_YAW_R = config_root["head_pose_yaw_right_offset"].asDouble();
    HEAD_POSE_PITCH_U = config_root["head_pose_pitch_up_offset"].asDouble();
    HEAD_POSE_PITCH_D = config_root["head_pose_pitch_down_offset"].asDouble();
#if defined(BYD_HA6) || defined(BYD_SA2)
    HEAD_POSE_SPE_GLASSES_YAW_L = config_root["head_pose_spe_glasses_yaw_left_offset"].asDouble();
    HEAD_POSE_SPE_GLASSES_YAW_R = config_root["head_pose_spe_glasses_yaw_right_offset"].asDouble();
    HEAD_POSE_SPE_GLASSES_PITCH_U = config_root["head_pose_spe_glasses_pitch_up_offset"].asDouble();
    HEAD_POSE_SPE_GLASSES_PITCH_D =
        config_root["head_pose_spe_glasses_pitch_down_offset"].asDouble();
#else
    HEAD_POSE_SPE_GLASSES_YAW_L = 0.0f;
    HEAD_POSE_SPE_GLASSES_YAW_R = 0.0f;
    HEAD_POSE_SPE_GLASSES_PITCH_U = 0.0f;
    HEAD_POSE_SPE_GLASSES_PITCH_D = 0.0f;
#endif
    RIGHT_GAZE_VISION_FUSION_YAW_L =
        config_root["right_gaze_vision_fusion_yaw_left_offset"].asDouble();
    RIGHT_GAZE_VISION_FUSION_YAW_R =
        config_root["right_gaze_vision_fusion_yaw_right_offset"].asDouble();
    RIGHT_GAZE_VISION_FUSION_PITCH_U =
        config_root["right_gaze_vision_fusion_pitch_up_offset"].asDouble();
    RIGHT_GAZE_VISION_FUSION_PITCH_D =
        config_root["right_gaze_vision_fusion_pitch_down_offset"].asDouble();

    LEFT_GAZE_VISION_FUSION_YAW_L =
        config_root["left_gaze_vision_fusion_yaw_left_offset"].asDouble();
    LEFT_GAZE_VISION_FUSION_YAW_R =
        config_root["left_gaze_vision_fusion_yaw_right_offset"].asDouble();
    LEFT_GAZE_VISION_FUSION_PITCH_U =
        config_root["left_gaze_vision_fusion_pitch_up_offset"].asDouble();
    LEFT_GAZE_VISION_FUSION_PITCH_D =
        config_root["left_gaze_vision_fusion_pitch_down_offset"].asDouble();

    HEAD_YAW_L_OFFSET = config_root["headgaze_yaw_l_offset"].asDouble();
    HEAD_YAW_R_OFFSET = config_root["headgaze_yaw_r_offset"].asDouble();

    RIGHTEYE_UP_DOWN_PROPORTION = config_root["righteye_up_down_proportion"].asDouble();
    LEFTEYE_UP_DOWN_PROPORTION = config_root["lefteye_up_down_proportion"].asDouble();

    HEAD_YAW_BIAS = config_root["head_yaw_bias"].asDouble();

    if (config_root.isMember("fusion_use_eye")) {
        FUSION_USE_EYE = config_root["fusion_use_eye"].asInt();
    }
    if (config_root.isMember("region_mapping_width")) {
        REGION_MAPPING_WIDTH = config_root["region_mapping_width"].asInt();
    }
    if (config_root.isMember("region_mapping_height")) {
        REGION_MAPPING_HEIGHT = config_root["region_mapping_height"].asInt();
    }
    if (config_root.isMember("tolerate_percentage")) {
        TOLERATE_PERCENTAGE = config_root["tolerate_percentage"].asFloat();
    }
    if (config_root.isMember("region_hull")) {
        cc_assert(config_root["region_hull"].isArray());
        std::vector<cv::Point2f> hull;
        REGION_HULLS.clear();
        for (int k = 0; k < config_root["region_hull"].size(); k++) {
            hull.clear();
            for (int i = 0; i < config_root["region_hull"][k].size(); i++) {
                hull.clear();
                for (int j = 0; j < config_root["region_hull"][k][i].size(); j += 2) {
                    cc_assert(config_root["region_hull"][k][i].size() % 2 == 0);

                    cv::Point2f point;
                    point.x = config_root["region_hull"][k][i][j].asFloat();
                    point.y = config_root["region_hull"][k][i][j + 1].asFloat();
                    hull.push_back(point);
                }
                std::cout << "hull.size():" << hull.size() << std::endl;
                REGION_HULLS.push_back(hull);
            }
            std::cout << "use inside config json,REGION_HULLS.size():" << REGION_HULLS.size()
                      << std::endl;
        }
    }

    // printf("RIGHTEYE_UP_DOWN_PROPORTION:%f,LEFTEYE_UP_DOWN_PROPORTION:%f\n",
    //        RIGHTEYE_UP_DOWN_PROPORTION, LEFTEYE_UP_DOWN_PROPORTION);

    //标定参数如下

    int queue_length = 500;
    int k_num = 1;               //test
    float cluster_radius = 3.0;  //5.0;
    float threshold_100f = 0.8;
    float threshold_200f = 0.5;
    float threshold_longerf = 0.35;  //0.55;

    float eye_cluster_radius = 1.6;  //2.0;
    float eye_threshold_100f = 0.7;
    float eye_threshold_200f = 0.55;     //0.6;
    float eye_threshold_longerf = 0.45;  //0.5;

    calibrator.reset(new HeadPoseCalibrator(
        queue_length, 100, k_num, cluster_radius, threshold_100f, threshold_200f, threshold_longerf,
        eye_cluster_radius, eye_threshold_100f, eye_threshold_200f, eye_threshold_longerf));
    calibrator->init();
}

DistractionWarn::~DistractionWarn() {}

void DistractionWarn::Update(const Distraction_Info& info) {
    std::pair<long, bool> temp_pair;
    temp_pair.first = info.time_input;
    temp_pair.second = info.distraction_status;

    distraction_long_result.emplace_back(temp_pair);

    if (auto_calibration) {
        distraction_short_result.emplace_back(temp_pair);
        if (distraction_short_result.size() >= 2) {
            if ((distraction_short_result.back().first - distraction_short_result.front().first) >
                31000) {
                distraction_short_result.pop_front();  //移除队列最前面的数据
            }
        }
    }
    if (distraction_long_result.size() >= 2) {
        if ((distraction_long_result.back().first - distraction_long_result.front().first) > 6000) {
            distraction_long_result.pop_front();  //移除队列最前面的数据
        }
    }

    distraction_3s_result.clear();

    GetDataQueue(distraction_long_result, distraction_3s_result, window_time);  //获取3.5s的数据

    cache_time = info.time_input;

    return;
}

void DistractionWarn::GetDataQueue(std::deque<std::pair<long, bool>>& input_distraction_deque,
                                   std::deque<std::pair<long, bool>>& out_distraction_deque,
                                   long time_gap) {
    if (input_distraction_deque.size() == 0) {
        return;
    }
    auto last_ts = input_distraction_deque.back();
    for (const auto& v : input_distraction_deque) {
        if (last_ts.first - v.first > time_gap)
            continue;
        out_distraction_deque.emplace_back(v);
    }
    return;
}

//这里计算各个报警状态

//计算是否持续分神
bool DistractionWarn::GetResult(std::deque<std::pair<long, bool>>& distraction_deque,
                                long time_gap) {
    // std::cout << "diff:" << distraction_deque.back().first- distraction_deque.front().first << " " << distraction_deque.back().first <<
    // " " << distraction_deque.front().first << " time_gap:" << time_gap << std::endl;
    if ((distraction_deque.back().first - distraction_deque.front().first) < time_gap)
        return false;

    int count = 0;
    for (const auto& v : distraction_deque) {
        // if (v.second == false) {
        //     return false;
        // }

        if (v.second == true)
            count++;
    }
    // return true;
    float thf = count * 1.0f / distraction_deque.size();
    bool bRet = (thf >= distraction_thr) && (distraction_deque.back().second);
    // std::cout << "thf:" << thf << " distraction_thr:" << distraction_thr << " count:" << count << " distraction_deque.size():" << distraction_deque.size() << std::endl;
    return bRet;
}

//计算短时分心，累计分神
bool DistractionWarn::GetShortTimeResult(std::deque<std::pair<long, bool>>& distraction_deque,
                                         long interval_times) {
    if (distraction_deque.size() == 0)
        return false;
    // if ((distraction_deque.back().first - distraction_deque.front().first) < 30000)
    //     return false;

    long start_time = -1;  // -1 表示未开始计算
    long total_true_time = 0;
    for (size_t i = 0; i < distraction_deque.size(); ++i) {
        long timestamp = distraction_deque[i].first;
        bool is_true = distraction_deque[i].second;

        if (is_true) {
            if (start_time == -1) {
                start_time = timestamp;  // 记录一个新的连续 true 段落的起始时间
            }
        } else {
            if (start_time != -1) {
                // 遇到 false，结束当前连续 true 段落的计算
                total_true_time += distraction_deque[i - 1].first - start_time;
                start_time = -1;  // 重置为未开始
            }
        }

        if (total_true_time >= interval_times) {
            return true;
        }
    }

    // 如果最后一个段落是 true，且没有遇到 false
    if (start_time != -1) {
        total_true_time += distraction_deque.back().first - start_time;
    }

    if (total_true_time >= interval_times) {
        return true;
    }

    std::deque<std::pair<long, bool>> temp_deque2;
    temp_deque2.clear();
    long cost_time2 = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == true) {
            temp_deque2.clear();
            cost_time2 = 0;
        } else {
            //正常驾驶区域持续时长≥2s,清空短时分心数据
            temp_deque2.emplace_back(v);
            if (temp_deque2.size() >= 2) {
                cost_time2 = temp_deque2.back().first - temp_deque2.front().first;
            }
            if (cost_time2 >= 2000) {
                distraction_deque.clear();
                return false;
            }
        }
    }
    return false;
}

//获取降级结果
bool DistractionWarn::GetDegradeResult(std::deque<std::pair<long, bool>>& distraction_deque) {
    int count = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == false) {
            // return false;
            count++;
        }
    }
    // std::cout << "degrade score:" << (count * 1.0f / distraction_deque.size()) << std::endl;
    return (count * 1.0f / distraction_deque.size()) >= 0.5 ? true : false;
    // return true;
}

//降级根据连续帧数计算
bool DistractionWarn::GetDegradeResult2(std::deque<std::pair<long, bool>>& distraction_deque,
                                        int cnt) {
    int count = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == false) {
            // return false;
            count++;

        } else {
            count = 0;
        }

        if (count >= cnt)
            return true;
    }
    return false;
}

long DistractionWarn::GetContinueDistractionTime(
    const std::deque<std::pair<long, bool>> distraction_deque) {
    if (distraction_deque.size() == 0)
        return 0;

    long current_consecutive_time = 0;
    int cnt = 0;

    for (size_t i = 1; i < distraction_deque.size(); ++i) {
        long time_diff = distraction_deque[i].first - distraction_deque[i - 1].first;

        if (distraction_deque[i].second == true) {
            if (distraction_deque[i - 1].second == true) {
                current_consecutive_time += time_diff;
                // cnt = 0;  // Reset count as the streak continues.
            } else {
                // Previous frame was false, but current is true, reset the counter.
                cnt = 0;
                current_consecutive_time += time_diff;
            }
        } else {
            // Current frame is false, increment the non-continuous count.
            cnt++;
            // Only reset `current_consecutive_time` if the count of non-continuous frames exceeds the threshold.
            if (cnt >= 1) {
                current_consecutive_time = 0;
            }
        }

        // distraction_info.distraction_continue_time = current_consecutive_time;
    }
    return current_consecutive_time;
}

float DistractionWarn::GetContinueDistractionPercent(
    const std::deque<std::pair<long, bool>> distraction_deque, long time_gap) {
    if (distraction_deque.size() == 0 ||
        (distraction_deque.back().first - distraction_deque.front().first) < time_gap)
        return 0.0f;

    int count = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == true)
            count++;
    }
    // return true;
    float thf = count * 1.0f / distraction_deque.size();

    return thf;
}

long DistractionWarn::GetSumDistractionTime(
    const std::deque<std::pair<long, bool>> distraction_deque) {
    long distraction_sum_time = 0;

    if (distraction_deque.size() == 0)
        return 0;

    long start_time = -1;  // -1 表示未开始计算
    for (size_t i = 0; i < distraction_deque.size(); ++i) {
        long timestamp = distraction_deque[i].first;
        bool is_true = distraction_deque[i].second;

        if (is_true) {
            if (start_time == -1) {
                start_time = timestamp;  // 记录一个新的连续 true 段落的起始时间
            }
        } else {
            if (start_time != -1) {
                // 遇到 false，结束当前连续 true 段落的计算
                distraction_sum_time += distraction_deque[i - 1].first - start_time;
                start_time = -1;  // 重置为未开始
            }
        }
    }

    // 如果最后一个段落是 true，且没有遇到 false
    if (start_time != -1) {
        distraction_sum_time += distraction_deque.back().first - start_time;
    }

    return distraction_sum_time;
}

long DistractionWarn::GetContinueFrontTime(
    const std::deque<std::pair<long, bool>> distraction_deque) {
    if (distraction_deque.size() == 0)
        return 0;

    long distraction_front_continue_time = 0;
    // std::vector<long> time_vector;  //用于记录时间差数组

    std::deque<std::pair<long, bool>> temp_deque2;
    temp_deque2.clear();
    long cost_time2 = 0;
    for (const auto& v : distraction_deque) {
        if (v.second == false) {
            temp_deque2.emplace_back(v);
            if (temp_deque2.size() >= 2) {
                cost_time2 = temp_deque2.back().first - temp_deque2.front().first;
            }
            distraction_front_continue_time = cost_time2;
        } else {
            temp_deque2.clear();
            cost_time2 = 0;
        }
        // time_vector.emplace_back(cost_time2);
    }
    // distraction_front_continue_time = *(std::max_element(
    //     time_vector.begin(), time_vector.end()));  //找出时间窗中最大时间作为结果输出

    return distraction_front_continue_time;
}

void DistractionWarn::Reset() {
    // time_3s_flag = false;
    // time_3s_arrived = false;
    // time_6s_flag = false;
    // time_6s_arrived = false;
    ok_flag = false;

    distraction_short_result.clear();
    distraction_long_result.clear();

    head_yaw_3s_vec.clear();
    glasses_vec.clear();
    temp_vec.clear();

    // shake_right_gaze_yaw_vec.clear();
    // shake_right_gaze_pitch_vec.clear();
    // shake_left_gaze_yaw_vec.clear();
    // shake_left_gaze_pitch_vec.clear();

    right_gaze_yaw_vec.clear();
    right_gaze_pitch_vec.clear();
    left_gaze_yaw_vec.clear();
    left_gaze_pitch_vec.clear();

    // headpose_vision_fusion_yaw_vec.clear();
    // headpose_righteye_gaze_vision_fusion_yaw_vec.clear();
    // headpose_lefteye_gaze_vision_fusion_yaw_vec.clear();

    alarm_start_time = cache_time;

    histor_warn_type = DISTRACTION_NORMAL;
    return;
}

DistractionType DistractionWarn::GetWarnStatus(int speed) {
    DistractionType status = DISTRACTION_NORMAL;
    //可视化
    distraction_info = {0};
    distraction_info.distraction_continue_time = GetContinueDistractionTime(distraction_3s_result);
    distraction_info.distraction_continue_percent =
        GetContinueDistractionPercent(distraction_3s_result, window_time - 150);
    distraction_info.distraction_front_continue_time = GetContinueFrontTime(distraction_3s_result);
    distraction_info.time_gap = 0;
    if (distraction_3s_result.size() > 0)
        distraction_info.time_gap =
            (distraction_3s_result.back().first - distraction_3s_result.front().first);
    //可视化end

    //提前报分神优化，延迟报警(只对长时分心有效)
    if (head_yaw_3s_vec.size() > 0 &&
        (head_yaw_3s_vec.back().first - head_yaw_3s_vec.front().first) >=
            (head_yaw_bias_window_time - 150) &&
        fabs(head_yaw_3s_min - head_yaw_3s_max) >= head_yaw_bias &&
        histor_warn_type == DISTRACTION_NORMAL) {
        distraction_reason = distraction_reason + " no_distraction2";

        // printf("head_yaw_bias abs:%f, %f, %f\n", fabs(head_yaw_3s_min - head_yaw_3s_max), head_yaw_3s_min, head_yaw_3s_max);
        // printf("Suppress distraction and delay alarm!\n");
    } else {
        bool speed_50_3s = GetResult(distraction_3s_result, window_time - 150);
        // printf("speed_50_3s:%d\n", speed_50_3s);
        if (speed_50_3s && speed >= 20) {
            status = DISTRACTION_FATIGUE1;
        }
    }

#if 0  //defined(BYD_EQ)
    std::deque<std::pair<long, bool>> distraction_temp_result;
    distraction_temp_result.clear();
    GetDataQueue(distraction_short_result, distraction_temp_result, 30100);  //获取30s的数据

    distraction_info.distraction_sum_time = GetSumDistractionTime(distraction_temp_result);

    bool short_time_distraction = GetShortTimeResult(distraction_temp_result, 10000);
    if (short_time_distraction && speed >= 20) {
        status = DISTRACTION_FATIGUE2;
    }
#endif

    if (histor_warn_type < status) {
        histor_warn_type = status;      //最新的报警等级，存在历史结果
        alarm_start_time = cache_time;  //升级后,更新报警开始时间
        alarm_ok_start_time = cache_time;
    } else {
        status = histor_warn_type;  //持续报上次结果
        alarm_ok_end_time = cache_time;
    }

    //判断降级（注视正常驾驶区域，持续时长≥1s，则退出分心状态）视为触发降级，即为正常状态
    alarm_end_time = cache_time;
    // if (status != DISTRACTION_NORMAL && getSec(alarm_start_time, alarm_end_time) >= 1 && !ok_flag)
    // if (status != DISTRACTION_NORMAL && getSec(alarm_start_time, alarm_end_time) >= 1 && !ok_flag)
    {
        //只触发降级
        std::deque<std::pair<long, bool>> temp_result;
        GetDataQueue(distraction_3s_result, temp_result, 1000);
        if (GetDegradeResult2(temp_result, 3)) {
            // std::cout << "Degrade distract..." << std::endl;
            Reset();
            status = DISTRACTION_NORMAL;
        }

        // std::deque<std::pair<long, bool>> temp_result2;  //重置分神数据
        // GetDataQueue(distraction_3s_result, temp_result2, 2000);
        // //满足2s时间窗，累计1s非分神时重置分神数据
        // if (GetDegradeResult(temp_result2)) {
        //     // std::cout << "Degrade distract..." << std::endl;
        //     Reset();
        //     status = DISTRACTION_NORMAL;
        // }
    }

    if (ok_flag) {  //收到ok,清除报警
        // std::cout << "ok_flag, clear distract..." << std::endl;
        Reset();
        status = DISTRACTION_NORMAL;
    }
    // else if (status != DISTRACTION_NORMAL && !ok_flag &&
    //            (getSec(alarm_ok_start_time, alarm_ok_end_time) >= 6)) {
    //     alarm_ok_start_time = cache_time;  //重置时间
    //     status = DISTRACTION_NO_RESPONSE;  //发送一次无响应状态
    // }
    // printf("status:%d\n", status);

    return status;
}

void DistractionWarn::SetOk() {
    ok_flag = true;
}

//自动标定分神
int DistractionWarn::auto_calibration_distraction(TXDmsFaceInfo face_info,
                                                  const TXCarInfo* car_info,
                                                  long now_ts) {
    // 读取json文件，本地调试模式
    std::string config_file = "sight.json";
    // std::string config_file = "/home/<USER>/byd_ddaw_addw/config/sc3e/sight.json";
    //配置文件存在，则从配置文件中读取
    if (!read_json && access(config_file.c_str(), F_OK) == 0) {
        Json::Reader json_reader2;
        Json::Value root2;
        std::ifstream infile2(config_file, std::ios::binary);
        if (!infile2.is_open()) {
            std::cout << "Open  sight config file failed!" << std::endl;
        }

        if (!json_reader2.parse(infile2, root2)) {
            std::cout << "Parse  sight json config file failed!" << std::endl;
        } else {
            read_json = true;
            log_switch = root2["log_switch"].asBool();

            headpose_pitch_threshold = root2["headpose_pitch_threshold"].asDouble();
            gaze_pitch_threshold = root2["gaze_pitch_threshold"].asDouble();
            headpose_yaw_threshold = root2["headpose_yaw_threshold"].asDouble();
            gaze_yaw_threshold = root2["gaze_yaw_threshold"].asDouble();

            window_time = root2["window_time"].asInt();
            distraction_thr = root2["distraction_thr"].asDouble();

            head_yaw_bias_window_time = root2["head_yaw_bias_window_time"].asInt();

            // head_yaw_filter_min = root2["head_yaw_filter_min"].asDouble();
            // head_yaw_filter_max = root2["head_yaw_filter_max"].asDouble();
            // head_roll_filter_min = root2["head_roll_filter_min"].asDouble();
            // head_roll_filter_max = root2["head_roll_filter_max"].asDouble();

            head_yaw_filter_left_offset = root2["head_yaw_filter_left_offset"].asDouble();
            head_yaw_filter_right_offset = root2["head_yaw_filter_right_offset"].asDouble();
            head_roll_filter_left_offset = root2["head_roll_filter_left_offset"].asDouble();
            head_roll_filter_right_offset = root2["head_roll_filter_right_offset"].asDouble();

            calibrate_headpose_yaw_min = root2["calibrate_headpose_yaw_min"].asDouble();
            calibrate_headpose_yaw_max = root2["calibrate_headpose_yaw_max"].asDouble();
            calibrate_headpose_pitch_min = root2["calibrate_headpose_pitch_min"].asDouble();
            calibrate_headpose_pitch_max = root2["calibrate_headpose_pitch_max"].asDouble();
            calibrate_headpose_roll_min = root2["calibrate_headpose_roll_min"].asDouble();
            calibrate_headpose_roll_max = root2["calibrate_headpose_roll_max"].asDouble();

            pitch_down = root2["pitch_down"].asDouble();
            pitch_up = root2["pitch_up"].asDouble();
            yaw_left = root2["yaw_left"].asDouble();
            yaw_right = root2["yaw_right"].asDouble();
            roll_left = root2["roll_left"].asDouble();
            roll_right = root2["roll_right"].asDouble();

            calibrate_time = root2["calibrate_time"].asInt();
            car_speed_variance = root2["car_speed_variance"].asDouble();
            steering_wheel_angle_min = root2["steering_wheel_angle_min"].asDouble();
            steering_wheel_angle_max = root2["steering_wheel_angle_max"].asDouble();
            headpose_yaw_normal_min = root2["headpose_yaw_normal_min"].asDouble();
            headpose_yaw_normal_max = root2["headpose_yaw_normal_max"].asDouble();
            headpose_pitch_normal_min = root2["headpose_pitch_normal_min"].asDouble();
            headpose_pitch_normal_max = root2["headpose_pitch_normal_max"].asDouble();
            headpose_roll_normal_min = root2["headpose_roll_normal_min"].asDouble();
            headpose_roll_normal_max = root2["headpose_roll_normal_max"].asDouble();
            gaze_yaw_normal_min = root2["gaze_yaw_normal_min"].asDouble();
            gaze_yaw_normal_max = root2["gaze_yaw_normal_max"].asDouble();
            gaze_pitch_normal_min = root2["gaze_pitch_normal_min"].asDouble();
            gaze_pitch_normal_max = root2["gaze_pitch_normal_max"].asDouble();
            headpose_yaw_variance = root2["headpose_yaw_variance"].asDouble();
            headpose_pitch_variance = root2["headpose_pitch_variance"].asDouble();
            // headpose_roll_variance = root2["headpose_roll_variance"].asDouble();
            // gaze_yaw_variance = root2["gaze_yaw_variance"].asDouble();
            // gaze_pitch_variance = root2["gaze_pitch_variance"].asDouble();
            headpose_yaw_left = root2["head_pose_yaw_left_offset"].asDouble();
            headpose_yaw_right = root2["head_pose_yaw_right_offset"].asDouble();
            headpose_pitch_up = root2["head_pose_pitch_up_offset"].asDouble();
            headpose_pitch_down = root2["head_pose_pitch_down_offset"].asDouble();
#if defined(BYD_HA6) || defined(BYD_SA2)
            headpose_spe_glasses_yaw_left =
                root2["head_pose_spe_glasses_yaw_left_offset"].asDouble();
            headpose_spe_glasses_yaw_right =
                root2["head_pose_spe_glasses_yaw_right_offset"].asDouble();
            headpose_spe_glasses_pitch_up =
                root2["head_pose_spe_glasses_pitch_up_offset"].asDouble();
            headpose_spe_glasses_pitch_down =
                root2["head_pose_spe_glasses_pitch_down_offset"].asDouble();
#else
            headpose_spe_glasses_yaw_left = 0.0f;
            headpose_spe_glasses_yaw_right = 0.0f;
            headpose_spe_glasses_pitch_up = 0.0f;
            headpose_spe_glasses_pitch_down = 0.0f;
#endif
            // lefteye_gaze_yaw_left = root2["lefteye_gaze_yaw_left_offset"].asDouble();
            // lefteye_gaze_yaw_right = root2["lefteye_gaze_yaw_right_offset"].asDouble();
            // righteye_gaze_yaw_left = root2["righteye_gaze_yaw_left_offset"].asDouble();
            // righteye_gaze_yaw_right = root2["righteye_gaze_yaw_right_offset"].asDouble();
            // lefteye_gaze_pitch_up = root2["lefteye_gaze_pitch_up_offset"].asDouble();
            // lefteye_gaze_pitch_down = root2["lefteye_gaze_pitch_down_offset"].asDouble();
            // righteye_gaze_pitch_up = root2["righteye_gaze_pitch_up_offset"].asDouble();
            // righteye_gaze_pitch_down = root2["righteye_gaze_pitch_down_offset"].asDouble();
            right_gaze_vision_fusion_yaw_left =
                root2["right_gaze_vision_fusion_yaw_left_offset"].asDouble();
            right_gaze_vision_fusion_yaw_right =
                root2["right_gaze_vision_fusion_yaw_right_offset"].asDouble();
            right_gaze_vision_fusion_pitch_up =
                root2["right_gaze_vision_fusion_pitch_up_offset"].asDouble();
            right_gaze_vision_fusion_pitch_down =
                root2["right_gaze_vision_fusion_pitch_down_offset"].asDouble();

            left_gaze_vision_fusion_yaw_left =
                root2["left_gaze_vision_fusion_yaw_left_offset"].asDouble();
            left_gaze_vision_fusion_yaw_right =
                root2["left_gaze_vision_fusion_yaw_right_offset"].asDouble();
            left_gaze_vision_fusion_pitch_up =
                root2["left_gaze_vision_fusion_pitch_up_offset"].asDouble();
            left_gaze_vision_fusion_pitch_down =
                root2["left_gaze_vision_fusion_pitch_down_offset"].asDouble();

            headgaze_yaw_l_offset = root2["headgaze_yaw_l_offset"].asDouble();
            headgaze_yaw_r_offset = root2["headgaze_yaw_r_offset"].asDouble();

            righteye_up_down_proportion = root2["righteye_up_down_proportion"].asDouble();
            lefteye_up_down_proportion = root2["lefteye_up_down_proportion"].asDouble();

            head_yaw_bias = root2["head_yaw_bias"].asDouble();

            if (root2.isMember("fusion_use_eye")) {
                fusion_use_eye = root2["fusion_use_eye"].asInt();
            }

            if (root2.isMember("region_mapping_width")) {
                region_mapping_width = root2["region_mapping_width"].asInt();
            }
            if (root2.isMember("region_mapping_height")) {
                region_mapping_height = root2["region_mapping_height"].asInt();
            }
            if (root2.isMember("tolerate_percentage")) {
                tolerate_percentage = root2["tolerate_percentage"].asFloat();
            }
            if (root2.isMember("region_hull")) {
                cc_assert(root2["region_hull"].isArray());

                std::vector<cv::Point2f> hull;
                for (int k = 0; k < root2["region_hull"].size(); k++) {
                    hull.clear();
                    for (int i = 0; i < root2["region_hull"][k].size(); i++) {
                        hull.clear();
                        for (int j = 0; j < root2["region_hull"][k][i].size(); j += 2) {
                            cc_assert(root2["region_hull"][k][i].size() % 2 == 0);

                            cv::Point2f point;
                            point.x = root2["region_hull"][k][i][j].asFloat();
                            point.y = root2["region_hull"][k][i][j + 1].asFloat();
                            hull.push_back(point);
                        }
                        std::cout << "hull.size():" << hull.size() << std::endl;
                        region_hulls.push_back(hull);
                    }
                    std::cout << "use outside config json,region_hulls.size():"
                              << region_hulls.size() << std::endl;
                }
            }
            // wheel_rad_normal_min = root2["wheel_rad_normal_min"].asDouble();
            // wheel_rad_normal_max = root2["wheel_rad_normal_max"].asDouble();
            // headpose_vision_fusion_yaw_variance =
            //     root2["headpose_vision_fusion_yaw_variance"].asDouble();
            // headpose_gaze_vision_fusion_yaw_variance =
            //     root2["headpose_gaze_vision_fusion_yaw_variance"].asDouble();
        }
    }
    // //test
    // log_switch = true;
    if (log_switch) {
        printf("read_json:%d,headpose_yaw_left:%f,headpose_yaw_right:%f,headpose_pitch_up:%f,"
               "headpose_pitch_down:%f \n",
               read_json, headpose_yaw_left, headpose_yaw_right, headpose_pitch_up,
               headpose_pitch_down);
    }

    //自动标定分神逻辑
    if (car_info->speed >= 20 && car_info->gear == TXGearPition::FORWARD) {
        if (!auto_calibration) {
            //判断人脸角度是否合理，不合理则不进行标定
            if (read_json) {
                if (calibrate_headpose_yaw_min > face_info.head_yaw ||
                    calibrate_headpose_yaw_max < face_info.head_yaw ||
                    calibrate_headpose_pitch_min > face_info.head_pitch ||
                    calibrate_headpose_pitch_max < face_info.head_pitch ||
                    calibrate_headpose_roll_min > face_info.head_roll ||
                    calibrate_headpose_roll_max < face_info.head_roll) {
                    if (log_switch) {
                        printf("DistractionWarn  "
                               "不满足人脸角度正常范围，不可进行自动标定...........\n");
                    }

                    return 2;
                }

                if (car_info->steer_whl_snsr_rad < steering_wheel_angle_min ||
                    car_info->steer_whl_snsr_rad > steering_wheel_angle_max) {
                    if (log_switch) {
                        printf("DistractionWarn  "
                               "不满足方向盘转角正常范围，不可进行自动标定...........\n");
                    }

                    return 2;
                }
            } else {
                // headpose_yaw_variance = HEADPOSE_YAW_VARIANCE;
                // headpose_pitch_variance = HEADPOSE_PITCH_VARIANCE;
                // headpose_roll_variance = HEADPOSE_ROLL_VARIANCE;
                car_speed_variance = CAR_SPEED_VARIANCE;
                // calibrate_time = CALIBRATE_TIME;
                headpose_pitch_threshold = HEADPOSE_PITCH_THR;
                gaze_pitch_threshold = GAZE_PITCH_THR;
                headpose_yaw_threshold = HEADPOSE_YAW_THR;
                gaze_yaw_threshold = GAZE_YAW_THR;
                // wheel_rad_normal_min = WHEEL_RAD_NORMAK_MIN;
                // wheel_rad_normal_max = WHEEL_RAD_NORMAK_MAX;
                // headpose_vision_fusion_yaw_variance = HEADPOSE_VISION_FUSION_YAW_VARIANCE;
                // headpose_gaze_vision_fusion_yaw_variance = HEADPOSE_GAZE_VISION_FUSION_YAW_VARIANCE;

                right_gaze_vision_fusion_yaw_left = RIGHT_GAZE_VISION_FUSION_YAW_L;
                right_gaze_vision_fusion_yaw_right = RIGHT_GAZE_VISION_FUSION_YAW_R;
                right_gaze_vision_fusion_pitch_up = RIGHT_GAZE_VISION_FUSION_PITCH_U;
                right_gaze_vision_fusion_pitch_down = RIGHT_GAZE_VISION_FUSION_PITCH_D;
                left_gaze_vision_fusion_yaw_left = LEFT_GAZE_VISION_FUSION_YAW_L;
                left_gaze_vision_fusion_yaw_right = LEFT_GAZE_VISION_FUSION_YAW_R;
                left_gaze_vision_fusion_pitch_up = LEFT_GAZE_VISION_FUSION_PITCH_U;
                left_gaze_vision_fusion_pitch_down = LEFT_GAZE_VISION_FUSION_PITCH_D;

                headgaze_yaw_l_offset = HEAD_YAW_L_OFFSET;
                headgaze_yaw_r_offset = HEAD_YAW_R_OFFSET;

                // head_yaw_filter_min = HEAD_YAW_FILTER_MIN;
                // head_yaw_filter_max = HEAD_YAW_FILTER_MAX;
                // head_roll_filter_min = HEAD_ROLL_FILTER_MIN;
                // head_roll_filter_max = HEAD_ROLL_FILTER_MAX;
                head_yaw_filter_left_offset = HEAD_YAW_FILTER_LEFT_OFFSET;
                head_yaw_filter_right_offset = HEAD_YAW_FILTER_RIGHT_OFFSET;
                head_roll_filter_left_offset = HEAD_ROLL_FILTER_LEFT_OFFSET;
                head_roll_filter_right_offset = HEAD_ROLL_FILTER_RIGHT_OFFSET;

                righteye_up_down_proportion = RIGHTEYE_UP_DOWN_PROPORTION;
                lefteye_up_down_proportion = LEFTEYE_UP_DOWN_PROPORTION;

                head_yaw_bias = HEAD_YAW_BIAS;
                head_yaw_bias_window_time = HEAD_YAW_BIAS_WINDOW_TIME;

                window_time = WINDOW_TIME;
                distraction_thr = DISTRACTION_THR;

                fusion_use_eye = FUSION_USE_EYE;
                region_mapping_width = REGION_MAPPING_WIDTH;
                region_mapping_height = REGION_MAPPING_HEIGHT;
                tolerate_percentage = TOLERATE_PERCENTAGE;
                region_hulls = REGION_HULLS;

                if (CALIBRATE_HEADPOSE_YAW_NORMAL_MIN > face_info.head_yaw ||
                    CALIBRATE_HEADPOSE_YAW_NORMAL_MAX < face_info.head_yaw ||
                    CALIBRATE_HEADPOSE_PITCH_NORMAL_MIN > face_info.head_pitch ||
                    CALIBRATE_HEADPOSE_PITCH_NORMAL_MAX < face_info.head_pitch ||
                    CALIBRATE_HEADPOSE_ROLL_NORMAL_MIN > face_info.head_roll ||
                    CALIBRATE_HEADPOSE_ROLL_NORMAL_MAX < face_info.head_roll) {
                    if (log_switch) {
                        printf("DistractionWarn "
                               "不满足人脸角度正常范围，不可进行自动标定2...........\n");
                    }
                    return 2;
                }
                if (car_info->steer_whl_snsr_rad < STEERING_WHEEL_ANGLE_MIN ||
                    car_info->steer_whl_snsr_rad > STEERING_WHEEL_ANGLE_MAX) {
                    if (log_switch) {
                        printf("DistractionWarn  "
                               "不满足方向盘转角正常范围，不可进行自动标定2...........\n");
                    }

                    return 2;
                }
            }
            //开始标定
            // if (0 == star_ts)
            //     star_ts = now_ts;
            // end_ts = now_ts;
            // if (end_ts - star_ts < calibrate_time) {
        }
        StartCalibration(face_info.head_yaw, face_info.head_pitch, face_info.head_roll,
                         face_info.left_eye_landmark.yaw, face_info.left_eye_landmark.pitch,
                         face_info.right_eye_landmark.yaw, face_info.right_eye_landmark.pitch,
                         face_info.left_eye_landmark.pupil_score,
                         face_info.right_eye_landmark.pupil_score, car_info->speed);

        // float temp_car_speed_mean = calculateMean(car_speed_vec);
        // float temp_car_speed_var = calculateVariance(car_speed_vec, temp_car_speed_mean);

        headpose_yaw_ = temp_headpose_yaw_mean;
        headpose_pitch_ = temp_headpose_pitch_mean;
        headpose_roll_ = temp_headpose_roll_mean;

        gaze_left_eye_pitch_ = temp_lefteye_gaze_pitch_mean;
        gaze_left_eye_yaw_ = temp_lefteye_gaze_yaw_mean;
        gaze_right_eye_pitch_ = temp_righteye_gaze_pitch_mean;
        gaze_right_eye_yaw_ = temp_righteye_gaze_yaw_mean;

        if (log_switch) {
            printf("DistractionWarn finally "
                   "headpose_yaw_:%f,headpose_pitch_:%f,headpose_roll_:%f,gaze_right_eye_"
                   "pitch_:%f,gaze_right_"
                   "eye_yaw_:%f,gaze_left_eye_yaw_:%f, gaze_left_eye_pitch_:%f\n",
                   headpose_yaw_, headpose_pitch_, headpose_roll_, gaze_right_eye_pitch_,
                   gaze_right_eye_yaw_, gaze_left_eye_yaw_, gaze_left_eye_pitch_);
        }
        //在这里计算融合后的最值
        right_gaze_vision_fusion_yaw_min =
            ((gaze_right_eye_yaw_ * gaze_yaw_threshold) + (headpose_yaw_ * headpose_yaw_threshold) -
             right_gaze_vision_fusion_yaw_left);
        right_gaze_vision_fusion_yaw_max =
            ((gaze_right_eye_yaw_ * gaze_yaw_threshold) + (headpose_yaw_ * headpose_yaw_threshold) +
             right_gaze_vision_fusion_yaw_right);
        right_gaze_vision_fusion_pitch_min =
            ((gaze_right_eye_pitch_ * gaze_pitch_threshold) +
             (headpose_pitch_ * headpose_pitch_threshold) - right_gaze_vision_fusion_pitch_down);
        right_gaze_vision_fusion_pitch_max =
            ((gaze_right_eye_pitch_ * gaze_pitch_threshold) +
             (headpose_pitch_ * headpose_pitch_threshold) + right_gaze_vision_fusion_pitch_up);
        left_gaze_vision_fusion_yaw_min =
            ((gaze_left_eye_yaw_ * gaze_yaw_threshold) + (headpose_yaw_ * headpose_yaw_threshold) -
             left_gaze_vision_fusion_yaw_left);
        left_gaze_vision_fusion_yaw_max =
            ((gaze_left_eye_yaw_ * gaze_yaw_threshold) + (headpose_yaw_ * headpose_yaw_threshold) +
             left_gaze_vision_fusion_yaw_right);
        left_gaze_vision_fusion_pitch_min =
            ((gaze_left_eye_pitch_ * gaze_pitch_threshold) +
             (headpose_pitch_ * headpose_pitch_threshold) - left_gaze_vision_fusion_pitch_down);
        left_gaze_vision_fusion_pitch_max =
            ((gaze_left_eye_pitch_ * gaze_pitch_threshold) +
             (headpose_pitch_ * headpose_pitch_threshold) + left_gaze_vision_fusion_pitch_up);

        bool eye_cali_finish = false;
        if (fusion_use_eye == 1) {
            eye_cali_finish = status.reye_cali_finish;
        } else if (fusion_use_eye == 0) {
            eye_cali_finish = status.leye_cali_finish;
        } else if (fusion_use_eye == 2) {
            eye_cali_finish = status.reye_cali_finish || status.leye_cali_finish;
        }

        if (status.head_cali_finish && eye_cali_finish) {
            auto_calibration = true;
        }

        if (!auto_calibration) {
            if (log_switch) {
                printf("DistractionWarn  自动标定中...........\n");
            }
            return 1;
        }
        return 0;
    } else if (auto_calibration) {
        if (log_switch) {
            printf(
                "DistractionWarn  "
                "自动标定已完成...........headpose_yaw_:%f,headpose_pitch_:%f,gaze_right_eye_pitch_"
                ":%f,gaze_right_eye_yaw_:%f ,gaze_left_eye_yaw_:%f,gaze_left_eye_pitch_:%f\n",
                headpose_yaw_, headpose_pitch_, gaze_right_eye_pitch_, gaze_right_eye_yaw_,
                gaze_left_eye_yaw_, gaze_left_eye_pitch_);
        }
        return 0;
    } else {
        //防止中途标定一半(车速不满足，档位不满足等)，重新清理数据//累计时间窗逻辑则不需要重新清理
        // ClearCalibration();
        if (log_switch) {
            if (car_info->speed < 20)
                printf("DistractionWarn  车速不满足，不进行分神标定,使用默认参数...........\n");
            if (car_info->gear != TXGearPition::FORWARD)
                printf("DistractionWarn  档位不是前进挡，不进行分神标定,使用默认参数...........\n");
        }
        return 2;
    }
}

bool DistractionWarn::IsDistracted(TXDmsFaceInfo face_info,
                                   const TXCarInfo* car_info,
                                   float right_up_down_proportion,
                                   float left_up_down_proportion,
                                   float face_angle_score,
                                   bool camera_occlusion,
                                   long now_ts) {
    current_distraction_status = false;
    float face_angle_score_th = 0.35;  //0.5;
    distraction_reason = "";
    gaze_mapping_point_flag = true;
    // if (face_info.score <= 0.5)
    //     return false;

    //增加头部yaw数据缓存
    std::pair<long, float> temp_data;

    temp_data.first = now_ts;
    if (face_angle_score >= face_angle_score_th && face_info.score >= 0.5 &&
        abs(face_info.head_pitch) < 50 && abs(face_info.head_roll) < 50) {
        if (face_info.head_yaw > 50) {
            temp_data.second = 50.0f;

        } else if (face_info.head_yaw < -50) {
            temp_data.second = -50.0f;

        } else {
            temp_data.second = face_info.head_yaw;
        }
        head_yaw_3s_vec.emplace_back(temp_data);
        temp_vec.emplace_back(temp_data.second);
    }
    if (head_yaw_3s_vec.size() >= 2) {
        while ((head_yaw_3s_vec.back().first - head_yaw_3s_vec.front().first) >
               head_yaw_bias_window_time) {
            head_yaw_3s_vec.pop_front();
            if (!temp_vec.empty())
                temp_vec.pop_front();
        }
    }
    // printf("head_yaw_bias_window_time:%d,time_gap:%d,array_size:%d\n", head_yaw_bias_window_time,
    //        (head_yaw_3s_vec.back().first - head_yaw_3s_vec.front().first), temp_vec.size());

    // 平滑head yaw bias抑制逻辑的数据统计，减少特殊场景对报警的影响
    if (temp_vec.size() >= 6) {
        std::vector<float> sorted_vec(temp_vec.begin(), temp_vec.end());
        std::sort(sorted_vec.begin(), sorted_vec.end());

        float min1 = sorted_vec[0];
        float min2 = sorted_vec[1];
        float min3 = sorted_vec[2];

        float max1 = sorted_vec[sorted_vec.size() - 1];
        float max2 = sorted_vec[sorted_vec.size() - 2];
        float max3 = sorted_vec[sorted_vec.size() - 3];
        head_yaw_3s_min = (min2 + min3) / 2.0f;
        head_yaw_3s_max = (max2 + max3) / 2.0f;
        distract_param.head_yaw_bias = fabs(head_yaw_3s_min - head_yaw_3s_max);
    } else {
        distract_param.head_yaw_bias = 0;
    }

    //增加头部yaw数据缓存 end

    //  角度 = 弧度 × (180/π)
    float angle = car_info->steer_whl_snsr_rad * (180 / 3.14159);
    float max_value = std::max(std::fabs(30.0f - (0.375f * car_info->speed)), 10.0f);
    // printf("angle:%f,max_value:%f\n", angle, max_value);
    if (car_info->turn_light == TURN_ON_LEFT || car_info->turn_light == TURN_ON_RIGHT ||
        car_info->speed < 20 || fabs(angle) >= max_value) {
        // printf("Being distracted is suppressed! \n", angle, max_value);
        distraction_reason = "no1";
        return false;
    }

    //车辆小幅转弯逻辑
    float headpose_yaw_l_offset = 0.0f;
    float headpose_yaw_r_offset = 0.0f;
    float gaze_yaw_l_offset = 0.0f;
    float gaze_yaw_r_offset = 0.0f;
    if (angle >= -10 && angle <= 10 && auto_calibration) {
        float unit = 7.0f / 10;  //每一度0.3
        float adjust_value;
        if (angle < 0) {
            adjust_value = floor(angle);  //向下取整

        } else {
            adjust_value = ceil(angle);  //向上取整
        }
        adjust_value = adjust_value * unit;
        // printf("adjust_value:%f\n", adjust_value);

        if (adjust_value < 0) {
            headpose_yaw_l_offset = fabs(adjust_value);
            gaze_yaw_l_offset = fabs(adjust_value);

        } else {
            headpose_yaw_r_offset = adjust_value;
            gaze_yaw_r_offset = adjust_value;
        }
    }

    // //在这里添加通过设置一个头部姿态合理yaw的区间，用于抑制头部roll角引起的分神
    // if (has_head_cali) {
    //     if ((face_info.head_roll < (headpose_roll_ - head_roll_filter_left_offset) ||
    //          face_info.head_roll > (headpose_roll_ + head_roll_filter_right_offset)) &&
    //         ((face_info.right_eye_landmark.pupil_score == 0 &&
    //           face_info.left_eye_landmark.pupil_score == 0))) {
    //         distraction_reason = "no2";
    //         return false;
    //     }
    // }

    // 针对眼镜且眼睛不可见场景下做的抑制头部误报逻辑
    bool is_spe_glasses_solution = false;
    float head_yaw_min = 0.0f, head_yaw_max = 0.0f, head_pitch_min = 0.0f, head_pitch_max = 0.0f;
#if defined(BYD_HA6) || defined(BYD_SA2)
    bool is_glasses = false;
    bool left_gaze_visuable = false;
    bool right_gaze_visuable = false;
    if (face_info.right_eye_landmark.eye_score > 0) {
        right_gaze_visuable = true;
    } else if (face_info.left_eye_landmark.eye_score > 0) {
        left_gaze_visuable = true;
    }
    if (face_info.isGlass != 0)
        is_glasses = true;
    // 因为眼睛状态的检测不稳定，所以当一定时间内有检测到眼镜，则认为是戴眼镜状态
    glasses_vec.emplace_back(is_glasses);
    if (glasses_vec.size() >= 2) {
        for (auto glasses_status : glasses_vec) {
            if (glasses_status == true) {
                is_glasses = true;
                break;
            }
        }
        if (glasses_vec.size() >= 100) {
            glasses_vec.pop_front();
        }
    }

    if (is_glasses) {
        if (fusion_use_eye == 0) {
            is_spe_glasses_solution = !left_gaze_visuable;
        } else if (fusion_use_eye == 1) {
            is_spe_glasses_solution = !right_gaze_visuable;
        } else if (fusion_use_eye == 2) {
            is_spe_glasses_solution = (!left_gaze_visuable && !right_gaze_visuable);
        }
    }
#endif

    if (has_head_cali) {
        if (read_json) {
            head_yaw_min = headpose_yaw_ - headpose_yaw_left - headpose_yaw_l_offset;
            head_yaw_max = headpose_yaw_ + headpose_yaw_right + headpose_yaw_r_offset;
            head_pitch_min = headpose_pitch_ - headpose_pitch_down;
            head_pitch_max = headpose_pitch_ + headpose_pitch_up;
            if (is_spe_glasses_solution) {
                head_yaw_min = head_yaw_min - headpose_spe_glasses_yaw_left;
                head_yaw_max = head_yaw_max + headpose_spe_glasses_yaw_right;
                head_pitch_min = head_pitch_min - headpose_spe_glasses_pitch_down;
                head_pitch_max = head_pitch_max + headpose_spe_glasses_pitch_up;
            }
        } else {
            head_yaw_min = headpose_yaw_ - HEAD_POSE_YAW_L - headpose_yaw_l_offset;
            head_yaw_max = headpose_yaw_ + HEAD_POSE_YAW_R + headpose_yaw_r_offset;
            head_pitch_min = headpose_pitch_ - HEAD_POSE_PITCH_D;
            head_pitch_max = headpose_pitch_ + HEAD_POSE_PITCH_U;
            if (is_spe_glasses_solution) {
                head_yaw_min = head_yaw_min - HEAD_POSE_SPE_GLASSES_YAW_L;
                head_yaw_max = head_yaw_max + HEAD_POSE_SPE_GLASSES_YAW_R;
                head_pitch_min = head_pitch_min - HEAD_POSE_SPE_GLASSES_PITCH_D;
                head_pitch_max = head_pitch_max + HEAD_POSE_SPE_GLASSES_PITCH_U;
            }
        }

        distract_param.head_yaw_min = head_yaw_min;
        distract_param.head_yaw_max = head_yaw_max;
        distract_param.head_pitch_min = head_pitch_min;
        distract_param.head_pitch_max = head_pitch_max;
    }

    if (status.reye_cali_finish) {
        float temp_right_gaze_vision_fusion_yaw_min =
            right_gaze_vision_fusion_yaw_min - gaze_yaw_l_offset;
        float temp_right_gaze_vision_fusion_yaw_max =
            right_gaze_vision_fusion_yaw_max + gaze_yaw_r_offset;

        distract_param.right_gaze_vf_yaw_min = temp_right_gaze_vision_fusion_yaw_min;
        distract_param.right_gaze_vf_yaw_max = temp_right_gaze_vision_fusion_yaw_max;
        distract_param.right_gaze_vf_pitch_min = right_gaze_vision_fusion_pitch_min;
        distract_param.right_gaze_vf_pitch_max = right_gaze_vision_fusion_pitch_max;
    }

    if (status.leye_cali_finish) {
        float temp_left_gaze_vision_fusion_yaw_min =
            left_gaze_vision_fusion_yaw_min - gaze_yaw_l_offset;
        float temp_left_gaze_vision_fusion_yaw_max =
            left_gaze_vision_fusion_yaw_max + gaze_yaw_r_offset;

        distract_param.left_gaze_vf_yaw_min = temp_left_gaze_vision_fusion_yaw_min;
        distract_param.left_gaze_vf_yaw_max = temp_left_gaze_vision_fusion_yaw_max;
        distract_param.left_gaze_vf_pitch_min = left_gaze_vision_fusion_pitch_min;
        distract_param.left_gaze_vf_pitch_max = left_gaze_vision_fusion_pitch_max;
    }

    //过滤仰头+张嘴误报分神逻辑
    if (auto_calibration && (distract_param.head_pitch_max < face_info.head_pitch) &&
        (face_info.mouth_opening > 0.5)) {
        distraction_reason = "no3";
        return false;
    }

    //计算是否分神
    float pitch_angle = 0.0f;
    float yaw_angle = 0.0f;
    float gaze_yaw_angle = 0.0f;
    float gaze_pitch_angle = 0.0f;
    bool head_pose = false;
    bool leye_gaze_credible_score = false;
    bool reye_gaze_credible_score = false;

    pitch_angle = face_info.head_pitch;
    yaw_angle = face_info.head_yaw;
    if (face_angle_score >= face_angle_score_th && face_info.score >= 0.5) {
        if (head_yaw_min != 0 && head_yaw_max != 0 && head_pitch_min != 0 && head_pitch_max != 0) {
            if (yaw_angle < (head_yaw_min) || yaw_angle > (head_yaw_max) ||
                pitch_angle < (head_pitch_min) || pitch_angle > (head_pitch_max))
                head_pose = true;
        }
    }

    bool right_eye_gaze = false;
    bool left_eye_gaze = false;
    float leye_fusion_pitch = 0.0f;
    float leye_fusion_yaw = 0.0f;
    float reye_fusion_pitch = 0.0f;
    float reye_fusion_yaw = 0.0f;
    predict_result = 0;

    //右眼
    pitch_angle = 0.0f;
    yaw_angle = 0.0f;
    gaze_yaw_angle = 0.0f;
    gaze_pitch_angle = 0.0f;

    float temp_pitch_angle = 0.0f;
    float temp_yaw_angle = 0.0f;
    float finally_pitch = 0.0f;
    float finally_yaw = 0.0f;

    if (face_info.right_eye_landmark.pupil_score > 0 &&
        face_info.right_eye_landmark.eye_score > 0) {
        gaze_yaw_angle = face_info.right_eye_landmark.yaw;
        gaze_pitch_angle = face_info.right_eye_landmark.pitch;

        reye_gaze_credible_score = true;

        // right_gaze_yaw_vec.emplace_back(gaze_yaw_angle);
        // right_gaze_pitch_vec.emplace_back(gaze_pitch_angle);

        // if (right_gaze_yaw_vec.size() > 5) {
        //     right_gaze_yaw_vec.pop_front();
        //     right_gaze_pitch_vec.pop_front();
        // }

        // temp_yaw_angle = calculateMean(right_gaze_yaw_vec);
        // temp_pitch_angle = calculateMean(right_gaze_pitch_vec);
        temp_yaw_angle = gaze_yaw_angle;
        temp_pitch_angle = gaze_pitch_angle;

        finally_pitch = temp_pitch_angle * gaze_pitch_threshold +
            face_info.head_pitch * headpose_pitch_threshold;
        finally_yaw =
            temp_yaw_angle * gaze_yaw_threshold + face_info.head_yaw * headpose_yaw_threshold;

        reye_fusion_pitch = finally_pitch;
        reye_fusion_yaw = finally_yaw;
    }

    if (read_json) {
        distract_param.current_right_gaze_vf_yaw = finally_yaw;
        distract_param.current_right_gaze_vf_pitch = finally_pitch;
    } else {
        distract_param.current_right_gaze_vf_yaw = finally_yaw;
        distract_param.current_right_gaze_vf_pitch = finally_pitch;
    }

    //左眼
    pitch_angle = 0.0f;
    yaw_angle = 0.0f;
    gaze_yaw_angle = 0.0f;
    gaze_pitch_angle = 0.0f;

    temp_pitch_angle = 0.0f;
    temp_yaw_angle = 0.0f;
    finally_pitch = 0.0f;
    finally_yaw = 0.0f;

    if (face_info.left_eye_landmark.pupil_score > 0 && face_info.left_eye_landmark.eye_score > 0) {
        gaze_yaw_angle = face_info.left_eye_landmark.yaw;
        gaze_pitch_angle = face_info.left_eye_landmark.pitch;

        leye_gaze_credible_score = true;

        // left_gaze_yaw_vec.emplace_back(gaze_yaw_angle);
        // left_gaze_pitch_vec.emplace_back(gaze_pitch_angle);

        // if (left_gaze_yaw_vec.size() > 5) {
        //     left_gaze_yaw_vec.pop_front();
        //     left_gaze_pitch_vec.pop_front();
        // }

        // temp_yaw_angle = calculateMean(left_gaze_yaw_vec);
        // temp_pitch_angle = calculateMean(left_gaze_pitch_vec);

        temp_yaw_angle = gaze_yaw_angle;
        temp_pitch_angle = gaze_pitch_angle;

        finally_pitch = temp_pitch_angle * gaze_pitch_threshold +
            face_info.head_pitch * headpose_pitch_threshold;
        finally_yaw =
            temp_yaw_angle * gaze_yaw_threshold + face_info.head_yaw * headpose_yaw_threshold;

        leye_fusion_pitch = finally_pitch;
        leye_fusion_yaw = finally_yaw;
    }

    if (read_json) {
        distract_param.current_left_gaze_vf_yaw = finally_yaw;
        distract_param.current_left_gaze_vf_pitch = finally_pitch;
    } else {
        distract_param.current_left_gaze_vf_yaw = finally_yaw;
        distract_param.current_left_gaze_vf_pitch = finally_pitch;
    }
    // std::cout << "region_hulls[0].size():" << region_hulls[0].size() << std::endl;
    mapping_x = 0.0f;
    mapping_y = 0.0f;
    if (face_angle_score >= face_angle_score_th && face_info.score >= 0.5) {
        if (auto_calibration) {
            gaze_pitch_mean = 0.0f;
            gaze_yaw_mean = 0.0f;
            bool gaze_credible_score = false;
            float mapping_pitch = 0.0f;
            float mapping_yaw = 0.0f;
            cv::Point2f mapping_point(0.0f, 0.0f);
            float cali_pitch = 0.0;
            float cali_yaw = 0.0;

            if (fusion_use_eye == 1) {
                gaze_pitch_mean = temp_righteye_gaze_pitch_mean;
                gaze_yaw_mean = temp_righteye_gaze_yaw_mean;
                gaze_credible_score = reye_gaze_credible_score;
                mapping_pitch = reye_fusion_pitch;
                mapping_yaw = reye_fusion_yaw;
            } else if (fusion_use_eye == 0) {
                gaze_pitch_mean = temp_lefteye_gaze_pitch_mean;
                gaze_yaw_mean = temp_lefteye_gaze_yaw_mean;
                gaze_credible_score = leye_gaze_credible_score;
                mapping_pitch = leye_fusion_pitch;
                mapping_yaw = leye_fusion_yaw;
            } else if (fusion_use_eye == 2) {
                gaze_credible_score = leye_gaze_credible_score || reye_gaze_credible_score;
                gaze_pitch_mean = 0.0f;
                gaze_yaw_mean = 0.0f;
                mapping_pitch = 0.0f;
                mapping_yaw = 0.0f;

                if ((leye_gaze_credible_score && has_leye_cali) &&
                    (reye_gaze_credible_score && has_reye_cali)) {
                    gaze_pitch_mean =
                        (temp_righteye_gaze_pitch_mean + temp_lefteye_gaze_pitch_mean) / 2.0f;
                    gaze_yaw_mean =
                        (temp_righteye_gaze_yaw_mean + temp_lefteye_gaze_yaw_mean) / 2.0f;
                    mapping_pitch = (reye_fusion_pitch + leye_fusion_pitch) / 2.0f;
                    mapping_yaw = (reye_fusion_yaw + leye_fusion_yaw) / 2.0f;
                } else if (leye_gaze_credible_score && has_leye_cali) {
                    gaze_pitch_mean = temp_lefteye_gaze_pitch_mean;
                    gaze_yaw_mean = temp_lefteye_gaze_yaw_mean;
                    mapping_pitch = leye_fusion_pitch;
                    mapping_yaw = leye_fusion_yaw;
                } else if (reye_gaze_credible_score && has_reye_cali) {
                    gaze_pitch_mean = temp_righteye_gaze_pitch_mean;
                    gaze_yaw_mean = temp_righteye_gaze_yaw_mean;
                    mapping_pitch = reye_fusion_pitch;
                    mapping_yaw = reye_fusion_yaw;
                }
            } else {
                std::cout << "not set fusion use eye..." << std::endl;
            }

            cali_pitch =
                headpose_pitch_ * headpose_pitch_threshold + gaze_pitch_mean * gaze_pitch_threshold;
            cali_yaw = headpose_yaw_ * headpose_yaw_threshold + gaze_yaw_mean * gaze_yaw_threshold;
            // 投射点计算视线区域初始化
            if (region_init_flag == false) {
                if (CcSvmModel::getInstance()->init(region_mapping_width, region_mapping_height,
                                                    region_hulls, tolerate_percentage) == 0) {
                    CcSvmModel::getInstance()->set_cali_angle3d(cali_pitch, cali_yaw,
                                                                headpose_roll_);
                    region_init_flag = true;
                }
            } else {
                // 标定成功后一直进行标定修正
                if (is_loop_calibration) {
                    CcSvmModel::getInstance()->set_cali_angle3d(cali_pitch, cali_yaw,
                                                                headpose_roll_);
                }
                // 初始化已完成且所要求的眼睛可视
                if (gaze_credible_score) {
                    int result = CcSvmModel::getInstance()->predict(
                        mapping_pitch, mapping_yaw, headpose_roll_, 0, mapping_point);

                    predict_result = result;
                }
                // 根据返回的区域索引来决定是否分神
                if (predict_result != 0) {
                    if (fusion_use_eye == 1) {
                        right_eye_gaze = true;
                    } else if (fusion_use_eye == 0) {
                        left_eye_gaze = true;
                    } else if (fusion_use_eye == 2) {
                        left_eye_gaze = true;
                        right_eye_gaze = true;
                    }
                    // // 触发分心清空标定,标定完成后，标志位置为false
                    // if (!cali_status_clear) {
                    //     cali_status_clear = true;
                    //     calibrator->clear();
                    //     std::cout << "clear calibration..." << std::endl;
                    // }
                }

#if 0//defined(BYD_HKH_L) || defined(BYD_EQ)
                if (mapping_point_old.x == 0 || mapping_point_old.y == 0) {
                    mapping_point_old = mapping_point;
                }

                float distance = std::sqrt(std::pow(mapping_point_old.x - mapping_point.x, 2) +
                                           std::pow(mapping_point_old.y - mapping_point.y, 2));
                gaze_mapping_point.emplace_back(distance);
                if (gaze_mapping_point.size() > 12) {
                    gaze_mapping_point.pop_front();
                }
                mapping_point_old = mapping_point;
#endif  // 0

                mapping_x = mapping_point.x;
                mapping_y = mapping_point.y;
            }
        }
        // else{
        //     mapping_x = 0.0f;
        //     mapping_y = 0.0f;
        // }
        // std::cout << "[" << status.head_cali_finish << "," << status.reye_cali_finish << ","  << status.leye_cali_finish << "," <<
        //     region_init_flag << "," << gaze_credible_score << "]"<< std::endl;
        // std::cout << "predict_result:" << predict_result << std::endl;
        // std::cout << "mapping_point:[" << mapping_x << "," << mapping_y << "]" << std::endl;

        if (auto_calibration) {
            // 双眼都可视时，优先使用眼睛判断分心
            bool use_eye = reye_gaze_credible_score && leye_gaze_credible_score;
            if (use_eye) {
                if (right_eye_gaze) {
                    current_distraction_status = true;
                    distraction_reason = distraction_reason + "right_eye_gaze";
                } else if (left_eye_gaze) {
                    current_distraction_status = true;
                    distraction_reason = distraction_reason + "left_eye_gaze";
                }
            }
            if (!current_distraction_status) {
#if defined(BYD_EQ_R) || defined(BYD_EQ) || defined(BYD_HKH_L) || defined(BYD_HA6) || defined(BYD_SA2) || defined(BYD_SC2EDE) || defined(BYD_HKH_R)
                if (face_info.head_yaw > std::min(headgaze_yaw_l_offset, headgaze_yaw_r_offset) &&
                    face_info.head_yaw < std::max(headgaze_yaw_l_offset, headgaze_yaw_r_offset)) {
#else
                if (face_info.head_yaw > (headpose_yaw_ - headgaze_yaw_l_offset) &&
                    face_info.head_yaw < (headpose_yaw_ + headgaze_yaw_r_offset)) {
#endif
                    if (fusion_use_eye == 1) {
                        use_eye = reye_gaze_credible_score;
                    } else if (fusion_use_eye == 0) {
                        use_eye = leye_gaze_credible_score;
                    } else if (fusion_use_eye == 2) {
                        use_eye = leye_gaze_credible_score || reye_gaze_credible_score;
                    }
                    if (use_eye) {
                        if (right_eye_gaze) {
                            current_distraction_status = true;
                            distraction_reason = distraction_reason + "right_eye_gaze";
                        } else if (left_eye_gaze) {
                            current_distraction_status = true;
                            distraction_reason = distraction_reason + "left_eye_gaze";
                        }
                    } else if (head_pose) {
                        current_distraction_status = true;
                        distraction_reason = distraction_reason + "headpose";
                    }

                    //新增视线往下看不报分心逻辑
                    if (current_distraction_status == false) {
                        if (face_info.right_eye_landmark.eye_score > 0 &&
                            face_info.right_eye_landmark.opening >= 0.1) {
                            if (right_up_down_proportion < righteye_up_down_proportion) {
                                current_distraction_status = true;
                                distraction_reason = distraction_reason + "right_eye_gaze2";
                            }
                        }

                        if (face_info.left_eye_landmark.eye_score > 0 &&
                            face_info.left_eye_landmark.opening >= 0.1) {
                            if (left_up_down_proportion < lefteye_up_down_proportion) {
                                current_distraction_status = true;
                                distraction_reason = distraction_reason + "left_eye_gaze2";
                            }
                        }
                    }
                } else {
                    if (head_pose) {
                        current_distraction_status = true;
                        distraction_reason = distraction_reason + "headpose";
                    }
                }
            }

        } else {
            if ((face_info.right_eye_landmark.pupil_score > 0) &&
                (face_info.right_eye_landmark.eye_score > 0)) {
                if (right_eye_gaze) {
                    current_distraction_status = true;
                    distraction_reason = distraction_reason + "right_eye_gaze";
                }
            } else if ((face_info.left_eye_landmark.pupil_score > 0) &&
                       (face_info.left_eye_landmark.eye_score > 0)) {
                if (left_eye_gaze) {
                    current_distraction_status = true;
                    distraction_reason = distraction_reason + "left_eye_gaze";
                }
            }
            if (head_pose) {
                current_distraction_status = true;
                distraction_reason = distraction_reason + "headpose";
            }
        }
    } else {
        if (!camera_occlusion && face_info.score < 0.5) {
#if defined(BYD_EQ)
            distraction_reason = distraction_reason + "no_face_det";
            current_distraction_status = true;
#endif
        }
        else if(face_angle_score<face_angle_score_th){
            current_distraction_status = true;
            distraction_reason = distraction_reason + "face_big_angle";
        }
        // std::cout<<face_angle_score<<" "<<face_info.score<<std::endl;
    }

    if (!current_distraction_status) {
        distraction_reason = "no_distraction";
    }
    // std::cout<<distraction_reason<<std::endl;
    //提前报分神优化，延迟报警
    // if (current_distraction_status && head_yaw_3s_vec.size() >= array_size &&
    //     fabs(head_yaw_min - head_yaw_max) >= head_yaw_bias) {
    //     printf("head_yaw_bias abs:%f\n", fabs(head_yaw_min - head_yaw_max));
    //     distraction_reason = "no_distraction2";
    //     current_distraction_status = false;
    // }

#if 0//defined(BYD_HKH_L) || defined(BYD_EQ)
    //  if ( face_info.head_yaw < 40)
    if (current_distraction_status && predict_result != 0 &&
        std::abs(face_info.head_yaw - headpose_yaw_) < 8 &&
        std::abs(face_info.head_pitch - headpose_pitch_) < 8) {
        float average = 0;

        for (size_t i = 0; i < gaze_mapping_point.size(); i++) {
            average += gaze_mapping_point[i];
        }
        average = average / gaze_mapping_point.size();

        // printf("----gaze_mapping_point average:%f \n", average);

        distraction_reason = " average :" + std::to_string(average);
        if (average > 25) {
            current_distraction_status = false;
            distraction_reason = "no_distraction_gaze_false";
        }
    }
#endif
    //计算分神end
    return current_distraction_status;
}  // namespace tongxing

void DistractionWarn::StartCalibration(float head_pose_yaw,
                                       float head_pose_pitch,
                                       float head_pose_roll,
                                       float gaze_left_yaw,
                                       float gaze_left_pitch,
                                       float gaze_right_yaw,
                                       float gaze_right_pitch,
                                       float left_eye_conf,
                                       float right_eye_conf,
                                       int car_speed) {
    headpose_yaw_vec.emplace_back(head_pose_yaw);
    headpose_pitch_vec.emplace_back(head_pose_pitch);
    headpose_roll_vec.emplace_back(head_pose_roll);
    if (left_eye_conf != 0) {
        gaze_left_eye_yaw_vec.emplace_back(gaze_left_yaw);
        gaze_left_eye_pitch_vec.emplace_back(gaze_left_pitch);
        // std::stringstream ss;
        // std::ofstream outfile;
        // ss << gaze_left_pitch << " ";
        // outfile.open("lefteye_gaze_pitch.txt", std::ios::out | std::ios::app);
        // // 检查文件是否成功打开
        // if (outfile.is_open()) {
        //     outfile << ss.str() << std::endl;
        // }
        // // 关闭文件
        // outfile.close();
    }
    if (right_eye_conf != 0) {
        gaze_right_eye_yaw_vec.emplace_back(gaze_right_yaw);
        gaze_right_eye_pitch_vec.emplace_back(gaze_right_pitch);
        // std::stringstream ss;
        // std::ofstream outfile;
        // ss << gaze_right_pitch << " ";
        // outfile.open("righteye_gaze_pitch.txt", std::ios::out | std::ios::app);
        // // 检查文件是否成功打开
        // if (outfile.is_open()) {
        //     outfile << ss.str() << std::endl;
        // }
        // // 关闭文件
        // outfile.close();
    }
    car_speed_vec.emplace_back(car_speed);
    bool is_eye_credible = false;
    if ((fusion_use_eye == 1 && right_eye_conf != 0) ||
        (fusion_use_eye == 0 && left_eye_conf != 0) ||
        (fusion_use_eye == 2 && right_eye_conf != 0 && left_eye_conf != 0)) {
        is_eye_credible = true;
    }

    if (!is_eye_credible) {
        // std::cout << "eye not credible, don`t calibration..." << std::endl;
        return;
    }
    std::vector<float> head_angles = {head_pose_pitch, head_pose_yaw, head_pose_roll};
    std::vector<float> lefteye_angles = {gaze_left_pitch, gaze_left_yaw / 10};
    std::vector<float> righteye_angles = {gaze_right_pitch, gaze_right_yaw / 10};
    std::vector<float> centroid;
    std::vector<float> leyecentroid;
    std::vector<float> reyecentroid;

    // calibrator->update(head_pose_pitch, head_pose_yaw, head_pose_roll);
    // std::vector<float> centroid;
    if (calibrator->execute(head_angles, lefteye_angles, righteye_angles, centroid, leyecentroid,
                            reyecentroid, status)) {
        // std::cout << "cali success! centroid position:" << std::endl;
        // std::cout << "Pitch: " << centroid[0] << std::endl;
        // std::cout << "Yaw:   " << centroid[1] << std::endl;
        // std::cout << "Roll:  " << centroid[2] << std::endl;
        // if (cali_status_clear && (status.leye_cali_finish || status.reye_cali_finish)) {
        //     std::cout << "calibration ok!" << std::endl;
        //     cali_status_clear = false;
        // }

        if (status.head_cali_finish) {
            // std::cout << "Pitch: " << centroid[0] << std::endl;
            // std::cout << "Yaw:   " << centroid[1] << std::endl;
            // std::cout << "Roll:  " << centroid[2] << std::endl;
            has_head_cali = true;
            temp_headpose_yaw_mean = centroid[1];
            temp_headpose_pitch_mean = centroid[0];
            temp_headpose_roll_mean = centroid[2];
        }
        if (status.leye_cali_finish) {
            // std::cout << "leye pitch: " << leyecentroid[0] << std::endl;
            // std::cout << "leye yaw: " << leyecentroid[1] << std::endl;
            has_leye_cali = true;
            temp_lefteye_gaze_pitch_mean = leyecentroid[0];
            temp_lefteye_gaze_yaw_mean = leyecentroid[1] * 10;
        }
        if (status.reye_cali_finish) {
            // std::cout << "reye pitch: " << reyecentroid[0] << std::endl;
            // std::cout << "reye yaw: " << reyecentroid[1] << std::endl;
            has_reye_cali = true;
            temp_righteye_gaze_pitch_mean = reyecentroid[0];
            temp_righteye_gaze_yaw_mean = reyecentroid[1] * 10;
        }
    }

    // return false;
    return;
}

void DistractionWarn::ClearCalibration() {
    auto_calibration = false;
    has_head_cali = false;
    has_leye_cali = false;
    has_reye_cali = false;
    star_ts = 0;
    end_ts = 0;
    headpose_yaw_ = 0.0f;
    headpose_pitch_ = 0.0f;
    headpose_roll_ = 0.0f;
    gaze_left_eye_yaw_ = 0.0f;
    gaze_right_eye_yaw_ = 0.0f;
    headpose_yaw_vec.clear();
    headpose_pitch_vec.clear();
    headpose_roll_vec.clear();
    gaze_left_eye_yaw_vec.clear();
    gaze_left_eye_pitch_vec.clear();
    gaze_right_eye_yaw_vec.clear();
    gaze_right_eye_pitch_vec.clear();

    right_gaze_yaw_vec.clear();
    right_gaze_pitch_vec.clear();
    left_gaze_yaw_vec.clear();
    left_gaze_pitch_vec.clear();

    head_yaw_3s_vec.clear();
    temp_vec.clear();

    calibrator->clear();
    CcSvmModel::getInstance()->clear();
    left_eye_pupil_yaw_vec.clear();
    left_eye_pupil_pitch_vec.clear();
    right_eye_pupil_yaw_vec.clear();
    right_eye_pupil_pitch_vec.clear();
    left_eye_pupil_visible = false;
    right_eye_pupil_visible = false;
    region_init_flag = false;
    // shake_right_gaze_yaw_vec.clear();
    // shake_right_gaze_pitch_vec.clear();
    // shake_left_gaze_yaw_vec.clear();
    // shake_left_gaze_pitch_vec.clear();

    // headpose_vision_fusion_yaw_vec.clear();
    // headpose_righteye_gaze_vision_fusion_yaw_vec.clear();
    // headpose_lefteye_gaze_vision_fusion_yaw_vec.clear();
}

bool DistractionWarn::GetCalibrationStatus() {
    return auto_calibration;
}

void DistractionWarn::GetHeadPosePitch(float& min_value, float& max_value) {
    if (read_json) {
        min_value = headpose_pitch_ - pitch_down;
        max_value = headpose_pitch_ + pitch_up;
    } else {
        min_value = headpose_pitch_ - PITCH_DOWN;
        max_value = headpose_pitch_ + PITCH_UP;
    }
    return;
}

void DistractionWarn::GetHeadPoseYaw(float& min_value, float& max_value) {
    if (read_json) {
        min_value = headpose_yaw_ - yaw_left;
        max_value = headpose_yaw_ + yaw_right;
    } else {
        min_value = headpose_yaw_ - YAW_LEFT;
        max_value = headpose_yaw_ + YAW_RIGHT;
    }
    return;
}

void DistractionWarn::GetHeadPoseRoll(float& min_value, float& max_value) {
    if (read_json) {
        min_value = headpose_roll_ - roll_left;
        max_value = headpose_roll_ + roll_right;
    } else {
        min_value = headpose_roll_ - ROLL_LIFT;
        max_value = headpose_roll_ + ROLL_RIGHT;
    }
    return;
}

std::string DistractionWarn::GetDistractParamers() {
    std::stringstream ss;
    ss << "{";
    ss << "\"head_yaw_min\":" << distract_param.head_yaw_min << ",";
    ss << "\"head_yaw_max\":" << distract_param.head_yaw_max << ",";
    ss << "\"head_pitch_min\":" << distract_param.head_pitch_min << ",";
    ss << "\"head_pitch_max\":" << distract_param.head_pitch_max << ",";
    ss << "\"right_gaze_vf_yaw_min\":" << distract_param.right_gaze_vf_yaw_min << ",";
    ss << "\"right_gaze_vf_yaw_max\":" << distract_param.right_gaze_vf_yaw_max << ",";
    ss << "\"right_gaze_vf_pitch_min\":" << distract_param.right_gaze_vf_pitch_min << ",";
    ss << "\"right_gaze_vf_pitch_max\":" << distract_param.right_gaze_vf_pitch_max << ",";

    ss << "\"left_gaze_vf_yaw_min\":" << distract_param.left_gaze_vf_yaw_min << ",";
    ss << "\"left_gaze_vf_yaw_max\":" << distract_param.left_gaze_vf_yaw_max << ",";
    ss << "\"left_gaze_vf_pitch_min\":" << distract_param.left_gaze_vf_pitch_min << ",";
    ss << "\"left_gaze_vf_pitch_max\":" << distract_param.left_gaze_vf_pitch_max << ",";
    ss << "\"headpose_pitch_\":" << headpose_pitch_ << ",";
    ss << "\"headpose_yaw_\":" << headpose_yaw_ << ",";
    ss << "\"headpose_roll_\":" << headpose_roll_ << ",";
    ss << "\"gaze_pitch_mean\":" << gaze_pitch_mean << ",";
    ss << "\"gaze_yaw_mean\":" << gaze_yaw_mean << ",";

    ss << "\"predict_result\":" << predict_result << ",";
    ss << "\"mapping_x\":" << mapping_x << ",";
    ss << "\"mapping_y\":" << mapping_y << ",";

    ss << "\"current_head_yaw_bias\":" << distract_param.head_yaw_bias << ",";

    ss << "\"current_right_gaze_vf_yaw\":" << distract_param.current_right_gaze_vf_yaw << ",";
    ss << "\"current_right_gaze_vf_pitch\":" << distract_param.current_right_gaze_vf_pitch << ",";
    ss << "\"current_left_gaze_vf_yaw\":" << distract_param.current_left_gaze_vf_yaw << ",";
    ss << "\"current_left_gaze_vf_pitch\":" << distract_param.current_left_gaze_vf_pitch << ",";

    // 添加 region_hulls
    ss << "\"region_hulls\":[";
    for (size_t i = 0; i < region_hulls.size(); ++i) {
        ss << "[";
        for (size_t j = 0; j < region_hulls[i].size(); ++j) {
            ss << "{\"x\":" << region_hulls[i][j].x << ",\"y\":" << region_hulls[i][j].y << "}";
            if (j < region_hulls[i].size() - 1) {
                ss << ",";
            }
        }
        ss << "]";
        if (i < region_hulls.size() - 1) {
            ss << ",";
        }
    }
    ss << "]";

    ss << "}";
    return ss.str();
}

void DistractionWarn::GetDistractionInfo(internal_analysis_distraction_info& info) {
    info = distraction_info;
}

}  // namespace tongxing
