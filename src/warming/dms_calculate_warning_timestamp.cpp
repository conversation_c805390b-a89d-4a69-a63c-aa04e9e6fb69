#include "dms_calculate_warning_timestamp.h"
#include "CalmCarLog.h"

namespace tongxing {
DrowsinessWarn::DrowsinessWarn() {}
DrowsinessWarn::~DrowsinessWarn() {}

DrowsinessWarn::DrowsinessWarn(int eye_3stime_window,
                               int eye_20stime_window,
                               int eye_60stime_window,
                               int mouth_stime_window,
                               int fps_num) {
    eye_3time_window = eye_3stime_window * 1000;
    eye_20time_window = eye_20stime_window * 1000;
    eye_60time_window = eye_60stime_window * 1000;
    mouth_time_window = mouth_stime_window * 1000;
    fps = fps_num;
}

// 更新报警信息
int update_num = 0;
void DrowsinessWarn::Update(const Warn_Info& info) {
    std::pair<long, bool> eye_pair;
    eye_pair.first = info.time_input;
    eye_pair.second = info.eye_close_status;
    // 将所有数据插入到对应数据结果中
    eye_1min_result.push_back(eye_pair);
    eye_20s_result.push_back(eye_pair);
    eye_3s_result.push_back(eye_pair);
    eye_1min_result2.push_back(eye_pair);

    std::pair<long, bool> mouth_pair;
    mouth_pair.first = info.time_input;
    mouth_pair.second = info.mouth_open_status;

    mouth_2min_result.push_back(mouth_pair);
    mouth_1min_result.push_back(mouth_pair);

    std::pair<long, bool> ldw_pair;
    ldw_pair.first = info.time_input;
    ldw_pair.second = info.ldw_status;

    ldw_20s_result.push_back(ldw_pair);
    ldw_120s_result.push_back(ldw_pair);

    time = info.time_input;

    // 2.根据时间点判断是否需要进行删除结果集数据
    if (!time_eye_20s_flag) {
        time_eye_20s_start = info.time_input;
        time_eye_20s_end = info.time_input;
        time_eye_20s_flag = true;
    } else {
        time_eye_20s_end = info.time_input;
        if (getMsec(time_eye_20s_start, time_eye_20s_end) > eye_20time_window) {
            time_eye_20s_arrived = true;
            eye_20s_result.pop_front();
            ldw_20s_result.pop_front();
        }
    }

    if (!time_eye_3s_flag) {
        time_eye_3s_start = info.time_input;
        time_eye_3s_end = info.time_input;
        time_eye_3s_flag = true;
    } else {
        time_eye_3s_end = info.time_input;
        if (getMsec(time_eye_3s_start, time_eye_3s_end) > eye_3time_window) {
            time_eye_3s_arrived = true;
            eye_3s_result.pop_front();
        }
    }

    if (!time_eye_1min_flag) {
        time_eye_1min_start = info.time_input;
        time_eye_1min_end = info.time_input;
        time_eye_1min_flag = true;
    } else {
        time_eye_1min_end = info.time_input;
        if (getMsec(time_eye_1min_start, time_eye_1min_end) > eye_60time_window) {
            time_eye_1min_arrived = true;
            eye_1min_result.pop_front();
            mouth_1min_result.pop_front();
            eye_1min_result2.pop_front();
        }
    }

    if (!time_mouth_2min_flag) {
        time_mouth_2min_start = info.time_input;
        time_mouth_2min_end = info.time_input;
        time_mouth_2min_flag = true;
    } else {
        time_mouth_2min_end = info.time_input;
        if (getMsec(time_mouth_2min_start, time_mouth_2min_end) > mouth_time_window) {
            time_mouth_2min_arrived = true;
            mouth_2min_result.pop_front();
            ldw_120s_result.pop_front();
        }
    }
    correctEyeState(eye_20s_result, 3);
    correctEyeState(eye_1min_result, 3);
    correctEyeState(eye_3s_result, 3);
    update_num++;
}

// 获取眼睛时间窗占比
bool DrowsinessWarn::GetEyeCloseThreshold(double thres) {
    if (!time_eye_1min_arrived)
        return false;
    int total = eye_1min_result.size();
    int count = 0;

    for (const auto& v : eye_1min_result) {
        if (v.second == true) {
            count++;
        }
    }
    return (count * 1.0f / total) >= thres ? true : false;
}

// 获取张嘴结果
bool DrowsinessWarn::GetYawnCount(const std::deque<std::pair<long, bool>>& mouth_result,
                                  const int count) {
    // 针对打哈欠次数，只要报了对应次数就应该立马报出来，不需要考虑时间窗是否到达
    // 有效哈欠2s为一个有效哈欠，根据帧率设置判断是否达到2s ,例如fps是30 2s==60帧
    int real_count = 0;
    long first_time = 0;
    long last_time = 0;

    first_time = mouth_result.front().first;
    for (const auto& v : mouth_result) {
        if (v.second == true) {
            last_time = v.first;
        } else {
            if (getMsec(first_time, last_time) >= 2000) {
                real_count++;
            }
            first_time = v.first;
        }

        if ((getMsec(first_time, last_time) >= 2000) && (real_count == (count - 1))) {
            real_count++;
        }
    }
    // CC_LOG_INFO("YAWN_111","YAWN sum:%d,real_count:%d,fps:%d",sum,real_count,fps);
    return real_count >= count ? true : false;
}

// 持续闭眼-1.5 2.4
bool DrowsinessWarn::GetCloseEyeContinue(const std::deque<std::pair<long, bool>>& eye_result,
                                         const double intrval_time) {
    long first_time = 0;
    long last_time = 0;

    first_time = eye_result.front().first;
    for (const auto& v : eye_result) {
        if (v.second == true) {
            last_time = v.first;
        } else {
            first_time = v.first;
        }
        if (getMsec(first_time, last_time) >= intrval_time * 1000) {
            return true;
        }
    }
    return false;
}

// 持续闭眼，单次闭眼达到时长
bool DrowsinessWarn::GetCloseEyeCoutinueCounts(const int count, const double intrval_time) {
    long first_time = 0;
    long last_time = 0;
    int real_count = 0;

    first_time = eye_20s_result.front().first;
    for (const auto& v : eye_20s_result) {
        if (v.second == true) {
            last_time = v.first;
        } else {
            if (getMsec(first_time, last_time) >= intrval_time * 1000) {
                real_count++;
            }
            first_time = v.first;
        }
        if ((getMsec(first_time, last_time) >= intrval_time * 1000) &&
            (real_count == (count - 1))) {
            real_count++;
        }
    }

    return real_count >= count ? true : false;
}

// 获取张嘴结果+ LDW
bool DrowsinessWarn::GetYawnCountAndLdw(const int count, const int LDW_count) {
    // 针对打哈欠次数，只要报了对应次数就应该立马报出来，不需要考虑时间窗是否到达
    // 有效哈欠2s为一个有效哈欠，根据帧率设置判断是否达到2s ,例如fps是30 2s==60帧
    long first_time = 0;
    long last_time = 0;
    int real_count = 0;

    first_time = mouth_2min_result.front().first;
    for (const auto& v : mouth_2min_result) {
        if (v.second == true) {
            last_time = v.first;
        } else {
            if (getMsec(first_time, last_time) >= 1000 * 2) {
                real_count++;
            }
            first_time = v.first;
        }

        if ((getMsec(first_time, last_time) >= 1000 * 2) && (real_count == (count - 1))) {
            real_count++;
        }
    }
    // 计算出ldw的次数
    // int ldw_count = std::count(ldw_120s_result.begin(), ldw_120s_result.end(), true);
    int ldw_count = 0;
    for (const auto& v : ldw_120s_result) {
        if (v.second == true) {
            ldw_count++;
        }
    }

    return (real_count >= count && ldw_count >= LDW_count) ? true : false;
}

// 持续闭眼，单次闭眼达到时长+LDW
bool DrowsinessWarn::GetCloseEyeCoutinueCountsAndLdw(const int count,
                                                     const double intrval_time,
                                                     const int LDW_count) {
    long first_time = 0;
    long last_time = 0;
    int real_count = 0;

    first_time = eye_20s_result.front().first;
    for (const auto& v : eye_20s_result) {
        if (v.second == true) {
            last_time = v.first;
        } else {
            if (getMsec(first_time, last_time) >= intrval_time * 1000) {
                real_count++;
            }
            first_time = v.first;
        }

        if ((getMsec(first_time, last_time) >= intrval_time * 1000) &&
            (real_count == (count - 1))) {
            real_count++;
        }
    }

    // 计算出ldw的次数
    // int ldw_count = std::count(ldw_20s_result.begin(), ldw_20s_result.end(), true);
    int ldw_count = 0;
    for (const auto& v : ldw_20s_result) {
        if (v.second == true) {
            ldw_count++;
        }
    }

    return (real_count >= count && ldw_count >= LDW_count) ? true : false;
}

// 获取报警类型
WarningType DrowsinessWarn::GetWarnStatus(TXWarnInfo& warn_info) {
    WarningType status = NORMAL;
    bool repetition = false;

    do {
        if (!level_4_flag) {
            WarningType current_warn_type = NORMAL;
            RelegationType relegation_type = SAME_LEVEL;

            // 先计算轻度
            bool light_60_10_eye = warn_info.eye1_60s_10p = GetEyeCloseThreshold(0.1);  //60s  10%
            bool light_120_3_mouth = warn_info.mouth1_120s_2 =
                GetYawnCount(mouth_2min_result, 2);                                     //120 2
            bool light_ldw_2_mouth = warn_info.mouth1_1l_1 = GetYawnCountAndLdw(1, 1);  //ldw 1  y 1

            // 在计算中度
            bool medium_1point5_eye = warn_info.eye2_20s_1f5 =
                GetCloseEyeContinue(eye_20s_result, 1.5);  // 20s 1.5s
            bool medium_20_2_0point75_eye = warn_info.eye2_2_0f75 =
                GetCloseEyeCoutinueCounts(2, 0.75);  //2 次 0.75s
            bool medium_20_1_0point75_eye_ldw = warn_info.eye2_1l_0f75 =
                GetCloseEyeCoutinueCountsAndLdw(1, 0.75, 1);
            bool medium_ldw_3_mouth = warn_info.mouth2_1l_2 = GetYawnCountAndLdw(2, 1);
            bool medium_60_12point5_eye = warn_info.eye2_60s_12p = GetEyeCloseThreshold(0.125);
            bool medium_120_4_mouth = warn_info.mouth2_120s_3 = GetYawnCount(mouth_2min_result, 3);

            // 计算重度
            bool heavy_2point4_eye = warn_info.eye3_20s_2f4 =
                GetCloseEyeContinue(eye_20s_result, 2.4);
            bool heavy_20_2_1point2_eye = warn_info.eye3_2_1f2 = GetCloseEyeCoutinueCounts(2, 1.2);
            bool heavy_20_1_1point2_eye_ldw = warn_info.eye3_1l_1f2 =
                GetCloseEyeCoutinueCountsAndLdw(1, 1.2, 1);

            // CC_LOG_INFO("LIGHT","light_60_10_eye:%d,light_120_3_mouth:%d,light_ldw_2_mouth:%d",light_60_10_eye,light_120_3_mouth,light_ldw_2_mouth);
            if (light_60_10_eye || light_120_3_mouth || light_ldw_2_mouth) {
                // printf("light_60_10_eye:%d,light_120_3_mouth:%d,light_ldw_2_mouth:%d\n",light_60_10_eye,light_120_3_mouth,light_ldw_2_mouth);
                current_warn_type = DROWSINESS_LEVEL_LIGHT;
            }

            if (medium_1point5_eye || medium_20_2_0point75_eye || medium_20_1_0point75_eye_ldw ||
                medium_ldw_3_mouth || medium_60_12point5_eye || medium_120_4_mouth) {
                current_warn_type = DROWSINESS_LEVEL_MEDIUM;

                // printf("medium_1point5_eye[%d],medium_20_2_0point75_eye[%d],medium_ldw_3_mouth[%d],ldw[%d]\n",
                //       medium_1point5_eye, medium_20_2_0point75_eye, medium_ldw_3_mouth,
                //        medium_60_12point5_eye, medium_120_4_mouth, medium_20_1_0point75_eye_ldw);
            }

            if (heavy_2point4_eye || heavy_20_2_1point2_eye || heavy_20_1_1point2_eye_ldw) {
                current_warn_type = DROWSINESS_LEVEL_HEAVEY;
                // printf("heavy_2point4_eye:%d,heavy_20_2_1point2_eye:%d,heavy_20_1_1point2_eye_ldw:%d\n",
                //          heavy_2point4_eye, heavy_20_2_1point2_eye, heavy_20_1_1point2_eye_ldw);
            }
            // 判断报警是否升降级
            if (current_warn_type == history_8s_warn_type) {
                // 平级
                relegation_type = SAME_LEVEL;
            } else if (current_warn_type > history_8s_warn_type) {
                // 升级
                relegation_type = UP_LEVEL;
            }

            // 如果升级立即报出最新的状态
            if (relegation_type == UP_LEVEL) {
                std::cout << "current warn_type: " << current_warn_type << std::endl;
                status = current_warn_type;
                history_8s_warn_type = current_warn_type;

                // 重置时间窗
                warn_time_start = time;
                warn_1min_time_start = time;
                time_5min_start = time;

                // warn_flag = true;
                jiangji_warn_1min_time_start = time;
            } else if (relegation_type == SAME_LEVEL) {
                warn_time_end = time;
                warn_1min_time_end = time;
                if (getMsec(warn_time_start, warn_time_end) <= sustain_warn_time) {
                    status = history_8s_warn_type;
                    std::cout << "持续8s的报警类型: " << status << std::endl;
                } else if (getMsec(warn_1min_time_start, warn_1min_time_end) <= sustain_1min_time) {
                    status = NORMAL;
                    std::cout << "1分钟内报警类型: " << status << std::endl;
                } else {
                    warn_time_start = time;
                    warn_1min_time_start = time;
                    // warn_flag = false;
                    status = current_warn_type;
                    std::cout << "新1分钟开始报警类型: " << status << std::endl;
                }
            }
            if (!first_warn_flag) {
                jiangji_warn_1min_time_start = time;
                jiangji_warn_1min_time_end = time;
                first_warn_flag = true;
            } else {
                jiangji_warn_1min_time_end = time;
            }
            // 降级在满足报警一分钟后再进行判断
            if (getMsec(jiangji_warn_1min_time_start, jiangji_warn_1min_time_end) >
                sustain_1min_time) {
                printf("current_warn_type[%d],history_8s_warn_type[%d]\n", current_warn_type,
                       history_8s_warn_type);
                if (current_warn_type < history_8s_warn_type) {
                    // 获取一分钟内是否存在张嘴一次且闭眼0.75s一次
                    if (GetYawnCount(mouth_1min_result, 1) == false &&
                        GetCloseEyeContinue(eye_1min_result2, 0.75) == false) {
                        // 降级
                        relegation_type = DOWN_LEVEL;
                    } else {
                        repetition = true;
                    }
                    printf("一分钟内嘴巴是否张开[%d],是否闭眼一次[%d]\n",
                           GetYawnCount(mouth_1min_result, 1),
                           GetCloseEyeContinue(eye_1min_result2, 0.75));
                }
                status = NORMAL;
                jiangji_warn_1min_time_start = time;  // 重置降级报警时间点
            }

            if (relegation_type == DOWN_LEVEL) {
                // std::cout << "降级状态" << std::endl;
                printf("降级状态,current_warn_type=%d\n", current_warn_type);
                if (history_8s_warn_type == DROWSINESS_LEVEL_HEAVEY) {
                    // 降级从4降低的话就只输出一次
                    std::cout << "等级4降级报警结果:" << current_warn_type << std::endl;
                    status = DROWSINESS_LEVEL_HEAVEY;
                    history_8s_warn_type = current_warn_type;
                    level_4_flag = true;
                    jiangji_warn_8s_time_start = time;
                } else {
                    std::cout << "等级2||3降级报警结果不进行输出" << std::endl;
                    history_8s_warn_type = NORMAL;
                    status = NORMAL;
                }
            }

            history_warn_type = current_warn_type;
        } else {
            jiangji_warn_8s_time_end = time;
            if (getMsec(jiangji_warn_8s_time_start, jiangji_warn_8s_time_end) < sustain_warn_time) {
                status = DROWSINESS_LEVEL_HEAVEY;
                std::cout << "降级持续8s的报警类型: " << status << std::endl;
            } else {
                level_4_flag = false;
                status = NORMAL;
                std::cout << "降级输出完毕" << std::endl;
            }
        }
        printf("dms  status:%d\n", status);
        // 5min重置报警结果
        if (!time_5min_flag) {
            time_5min_start = time;
            time_5min_end = time;
            time_5min_flag = true;
        } else {
            time_5min_end = time;
        }
        // std::cout << "1111:" << getSecond(time_5min_start, time_5min_end) << std::endl;
        if (getMsec(time_5min_start, time_5min_end) >= sustain_5min_time) {
            std::cout << "5min清除所有报警结果" << std::endl;
            // 5min达到后重置所有报警结果
            // 清除所有报警结果
            Reset();
        }
        warn_info.repetition = repetition;
    } while (false);

    return status;
}

// 重置
void DrowsinessWarn::Reset() {
    // 重置所有报警数据
    eye_1min_result.clear();
    mouth_2min_result.clear();
    eye_20s_result.clear();
    ldw_20s_result.clear();
    ldw_120s_result.clear();
    mouth_1min_result.clear();
    eye_1min_result2.clear();
    time_5min_flag = false;
    time_eye_1min_flag = false;
    time_eye_1min_arrived = false;
    time_eye_20s_flag = false;
    time_eye_20s_arrived = false;

    time_mouth_2min_flag = false;
    time_mouth_2min_arrived = false;

    eye_3s_result.clear();
    time_eye_3s_flag = false;
    time_eye_3s_arrived = false;

    history_warn_type = NORMAL;
    history_8s_warn_type = NORMAL;
    // warn_flag = false;

    first_warn_flag = false;
    level_4_flag = false;
}

// 获取时间秒
long DrowsinessWarn::getMsec(const long begin, const long end) {
    return end - begin;
}

void DrowsinessWarn::correctEyeState(std::deque<std::pair<long, bool>>& eye_state, int digit) {
    // 遍历所有的数据
    int p = 0;
    int eyeClose_1 = 0;
    int eyeOpen = 0;
    int eyeClose_2 = 0;
    while (p < eye_state.size()) {
        // 找到第一个闭眼的帧
        while (p < eye_state.size() && eye_state[p].second) {
            p++;
            eyeClose_1++;
        }
        if (p == eye_state.size()) {
            break;
        }
        int start = p;
        int end = p;

        // 找到闭眼帧结束位置
        while (end < eye_state.size() && !eye_state[end].second) {
            end++;
            eyeOpen++;
            p++;
        }

        // end++;
        while (end < eye_state.size() && eye_state[end].second) {
            end++;
            eyeClose_2++;
            p++;
        }

        if (eyeClose_1 > 5 && eyeClose_2 > 3 && eyeOpen < digit) {
            for (int i = start; i < start + eyeOpen; i++) {
                // std::cout << "-----i:"<<i << " size:"<<eye_state.size() << std::endl;
                eye_state[i].second = true;
            }
        }
        p = p - eyeClose_2;

        eyeClose_1 = 0;
        eyeOpen = 0;
        eyeClose_2 = 0;
    }
}
}  // namespace tongxing
