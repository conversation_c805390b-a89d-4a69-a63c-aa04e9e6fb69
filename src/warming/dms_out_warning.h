#ifndef _DMS_OUT_WARNING_H_
#define _DMS_OUT_WARNING_H_
#include <chrono>
#include <map>
#include <vector>
#include "dms_calculate_warning_byd.h"
#include "dms_distraction_warning_byd.h"
#include "tx_dms_sdk.h"

namespace tongxing {

typedef struct TXModelOut_ {
    float mouth_open;
    float l_eye_close;
    float r_eye_close;
    float face_angle_x;
    float face_angle_y;
    float face_angle_z;
} TXModelOut;

class dms_out_warning {
  private:
    /* data */
    // dms_calculate_warning *eye_datect_time_1;
    // dms_calculate_warning *eye_datect_time_2;
    // dms_calculate_warning *mouth_datect;

    DrowsinessWarn* Dwarn;
    DistractionWarn* Distrac;

    float l_eye_cloes_threshold = 0.7;
    float r_eye_cloes_threshold = 0.7;
    float yawn_opne_threshold = 0.5;

    int output_model_bool(const TXModelOut* model_out,
                          Warn_Info& warn_info_,
                          TXDmsFaceInfo face_info);

  public:
    dms_out_warning(/* args */);
    ~dms_out_warning();
    int dms_init_warning();
    int DmsRestAlarm();
    int DmsRestEyeAlarm();
    int DmsAlarmSetOk();
    TXDrowsinessType dms_run_warning(const long time,
                                     TXDmsFaceInfo face_info,
                                     const TXCarInfo* car_info,
                                     const TXModelOut* input,
                                     TXWarnInfo& warn_info_out);

    TXDistractionType distrac_run_warning(const long time, const TXCarInfo* car_info, bool status);
    int DistracRestAlarm();
    void ResetCalibration();      //重置分神标定
    bool GetCalibrationStatus();  //获取标定状态
    int auto_calibration_distraction(TXDmsFaceInfo face_info,
                                     const TXCarInfo* car_info,
                                     long now_ts);  //自动标定
    bool GetCurrentDistractStatus(TXDmsFaceInfo face_info,
                                  const TXCarInfo* car_info,
                                  float right_up_down_proportion,
                                  float left_up_down_proportion,
                                  float face_angle_score,
                                  bool camera_occlusion,
                                  long now_ts);  //获取当前是否分神
    void GetHeadPosePitch(float& min_value,
                          float& max_value);  //获取标定后headpose pitch最小角度值
    void GetHeadPoseRoll(float& min_value, float& max_value);  //获取标定后headpose roll角度值
    void GetHeadPoseYaw(float& min_value, float& max_value);

    std::string GetDistractReason();
    std::string GetDistractParamers();
    bool GetHeadPoseGazeShake(TXDmsFaceInfo face_info);

    void GetTiredInfo(tx_tired& tired_info);
    void GetDistractionInfo(internal_analysis_distraction_info& info);
};

}  // namespace tongxing
#endif