#ifndef __CALMCAR_N50_PROCESS_H__
#define __CALMCAR_N50_PROCESS_H__
#include <memory>
#include <string>
#include "cc_math_tool.h"
#include "cc_module_grop.h"
#include "dms_process.h"
#include "json.h"
#include "tx_dms_sdk.h"

namespace tongxing {
class CcDmsProcess {
  public:
    int Init(const char* config_file = NULL, const char* cache_path = NULL);
    int SetInput(const TXImageInfo* image, TXDmsResult* result);
    int SetInput(const TXImageInfo* image, TXDmsResult* result, long ts);  //内部测试使用
    int SetInputByEaTensor(void* image, int device, long long frame_id, int speed);
    int GetVersion(std::string& version);
    int SetDmsState(bool state);
    int updateCarInfo(const TXCarInfo* carInfo);
    bool GetDmsState();
    int RestAlarm();
    int RestDistractAlarm();
    int AlarmSetOk();
    int SetDriverRoi(TXPoint2i* left_top_point, TXPoint2i* right_bottom_point);
    int SetPhoneAndSmokingDetectEnableStatus(unsigned char flag);

    std::string GetDistractReason(TXCameraStatus camera_status);  //供外层测试调用
    std::string GetDistractParamers();                            //供外层测试调用
    void GetTiredInfo(tx_tired& tired_info);
    int GetRightLeftEyeThr(float& left_eye_thr, float& right_eye_thr);
    void GetDistractionInfo(internal_analysis_distraction_info& info);

  public:
    int once_process(std::shared_ptr<NumArray> image, TXDmsResult* result);
    int once_process(std::shared_ptr<NumArray> image, TXDmsResult* result, long ts);  //内部测试使用
    std::shared_ptr<CcObjBBox> get_driving_face_bbox();
    std::shared_ptr<std::vector<cv::Point>> get_driving_face_keypoint();
    std::shared_ptr<std::vector<float>> get_driving_face_angle();
    std::shared_ptr<std::vector<float>> get_driving_right_eye_close_score();
    std::shared_ptr<std::vector<float>> get_driving_left_eye_close_score();
    std::shared_ptr<std::vector<float>> get_driving_face_attr();
    int get_occlusion_status();

    typedef struct EyeInfo_{
      float true_eye_score;
      TXPoint2i eye_coutours[8];  // 眼睛轮廓点
      float true_iris_score;
      TXPoint2i iris_coutours[8];  // 虹膜轮廓点
      float true_pupil_score;
      TXPoint2i pupil;  // 瞳孔点      
    } EyeInfo;

    typedef struct EyeInfos_{
      EyeInfo left_eye;
      EyeInfo right_eye;
    } EyeInfos;
    void get_eye_info_(EyeInfos& eyeinfos);
    std::shared_ptr<std::vector<float>> get_driving_right_eye_landmarks();
    std::shared_ptr<std::vector<float>> get_driving_left_eye_landmarks();

    std::shared_ptr<std::vector<CcObjBBox>> get_phone_cig_bbox();

  private:
    std::shared_ptr<std::vector<float>> get_driving_eye_landmarks(
        const std::shared_ptr<NumArray>& feat,
        const std::shared_ptr<NumArray>& offset_scale,
        const std::shared_ptr<std::vector<float>>& angle,
        const cv::Point& left_point,
        const cv::Point& right_point,
        const cv::Point& center_point, EyeInfo &eye_info);

  private:
    TXPoint2i driver_roi_left_top_point;
    TXPoint2i driver_roi_right_bottom_point;
    DmsProcess dms_process_;
    std::shared_ptr<tongxing::CcModule> grop;
    bool flag_enable_dms_ = false;
    std::string cache_path_;
    Json::Value config_param_;
    std::string cache_param_filename_;
    int status_process_ = 0;  // 0:DMS_PROCESS 1:FR_Regist_process 2,FR_Comparison
    bool enable_fatigue_ = true;
    bool enable_distraction_ = true;
    long long frame_id = 0;
    TXCarInfo car_info_;

    EyeInfo l_eyeinfo;
    EyeInfo r_eyeinfo;

  private:
    std::string case_save_path;  //用于保存结果路径
    bool first_boot_up = true;   //首次启动标识
    void SaveResultToJson(TXDmsResult& dms_result, const TXImageInfo* image);
    void SaveResultToWeb(TXDmsResult& dms_result, const TXImageInfo* image);
};
}  // namespace tongxing

#endif