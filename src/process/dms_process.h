#ifndef __N50_DMS_PROCESS_H__
#define __N50_DMS_PROCESS_H__
#include <functional>
#include <memory>
#include <opencv2/imgproc.hpp>
#include "cc_math_tool.h"
#include "cc_status_hold.h"
#include "dms_out_warning.h"
#include "dms_warming.h"
#include "occlusion_detector.h"
#include "tx_dms_sdk.h"

namespace tongxing {

typedef enum InternalCameraType_ {
    Camera_Norm = 0,     //正常
    Camera_Occ = 1,      //相机遮挡
    No_face = 2,         //无人脸
    No_face_mask = 3,    //无人脸-口罩
    No_face_irblock = 4  //无人脸-ir阻断
} InternalCameraType;

class DmsProcess {
    typedef std::function<std::shared_ptr<CcObjBBox>(void)> GetBboxFun;
    typedef std::function<std::shared_ptr<std::vector<CcObjBBox>>(void)> GetBboxArrayFun;
    typedef std::function<std::shared_ptr<std::vector<cv::Point>>(void)> GetPointArrayFun;
    typedef std::function<std::shared_ptr<std::vector<std::vector<cv::Point>>>(void)>
        GetPointArrayArrayFun;
    typedef std::function<std::shared_ptr<std::vector<float>>(void)> GetFloatArrayFun;
    typedef std::function<bool(void)> GetBoolFun;
    typedef std::function<int(void)> GetIntFun;

  public:
    int Init(const GetBboxFun& get_driving_face_bbox,
             const GetPointArrayFun& get_driving_face_keypoint,
             const GetFloatArrayFun& get_driving_face_angle,
             const GetFloatArrayFun& get_driving_right_eye_close_score,
             const GetFloatArrayFun& get_driving_left_eye_close_score,
             const GetIntFun& get_occlusion,
             const GetFloatArrayFun& get_driving_face_attr,
             const GetFloatArrayFun& get_driving_right_eye_landmarks,
             const GetFloatArrayFun& get_driving_left_eye_landmarks,
             const GetBboxArrayFun& get_phone_cig_bbox,
             const char* config_file);
    // int execute(const TXCarInfo* car_info, long long frame_id, TXDmsResult& result_out);
    int execute(const TXCarInfo* car_info,
                long long frame_id,
                TXDmsResult& result_out,
                long ts = 0);
    int SetFatigue(bool type);
    bool GetFatigue();
    int DmsRestAlarm();
    int DmsAlarmSetOk();
    int DmsDistractRestAlarm();
    std::string GetDistractReason(TXCameraStatus camera_status);
    std::string GetDistractParamers();
    int SetPhoneAndSmokingDetectEnableStatus(unsigned char flag);

    void GetTiredInfo(tx_tired& tired_info);
    int GetRightLeftEyeThr(float& left_eye_thr, float& right_eye_thr);
    void GetDistractionInfo(internal_analysis_distraction_info& info);

  private:
    cc_dms_warming noface_warning;
    cc_dms_warming occlusion_warning;
    CcBoolTimeWindowsStatistics phone_det_win;
    CcBoolTimeWindowsStatistics smoking_det_win;
    // cc_dms_warming Mask_warning;     //戴口罩
    // cc_dms_warming Glasses_warning;  //戴眼镜
    // CcStatusHold yawn_status_hold;
    bool enable_fatigue_ = true;

    bool is_first_face_occ = false;  //人脸遮挡标注位（上电过程中，只报警一次，其余报正常）

    //获取左右眼面积占比
    float leye_thr;
    float reye_thr;

    //标记报警是否已经触发
    bool noface_warning_flag = false;
    bool noface_stop_first_flag = false;
    long noface_warning_stop;
    bool occlusion_warning_flag = false;
    bool occlusion_stop_first_flag = false;
    long occlusion_warning_stop;
    // bool irblock_warning_flag = false;
    // bool irblock_stop_first_flag = false;
    // long irblock_warning_stop;
    // bool mask_warning_flag = false;
    // bool mask_stop_first_flag = false;
    // long mask_warning_stop;
    TXEyeLandmark last_right_eye_landmark;
    TXEyeLandmark last_left_eye_landmark;
    int right_eye_lost_count = 0;
    int left_eye_lost_count = 0;

    InternalCameraType last_camera_history_warn_type = Camera_Norm;

    long last_ts = 0;                //保存上一帧时间戳
    long last_frame_control_ts = 0;  //帧率控制，上一帧时间戳
    bool is_clear_eyeframes = false;
    // unsigned int activateNum = 36000 * 2;  //未激活试用帧数

    long algorithm_start_time;  //算法开始时间
  #if defined(BYD_SOP)
      long algorithm_experience_time = 0;
  #else
      long algorithm_experience_time = 1000 * 60 * 120;  //算法体验时间
  #endif

    bool is_algorithm_experience = false;  //算法体验期是否已经完毕

  private:
    unsigned char phone_smoking_detect_enable_status_;
    GetBboxFun get_driving_face_bbox_;
    GetPointArrayFun get_driving_face_keypoint_;
    GetFloatArrayFun get_driving_face_angle_;
    GetFloatArrayFun get_driving_right_eye_close_score_;
    GetFloatArrayFun get_driving_left_eye_close_score_;
    GetFloatArrayFun get_driving_right_eye_landmarks_;
    GetFloatArrayFun get_driving_left_eye_landmarks_;
    GetIntFun get_occlusion_;
    GetFloatArrayFun get_driving_face_attr_;
    GetBboxArrayFun get_phone_cig_bbox_;
    std::shared_ptr<dms_out_warning> tx_dms_out_warning;
    bool isAlgoActivate(TXDmsResult& result, const long& now_ts);
};
}  // namespace tongxing

#endif
