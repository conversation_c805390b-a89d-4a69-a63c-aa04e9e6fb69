#include "dms_process.h"
#include <sys/stat.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include "CalmCarLog.h"
#include "activate.h"
#include "cc_mouth_opening.h"
#include "json.h"

static std::string getCurrentTimes() {
    struct timeval tv;
    gettimeofday(&tv, NULL);

    static const int MAX_BUFFER_SIZE = 128;
    char timestamp_str[MAX_BUFFER_SIZE];
    time_t sec = static_cast<time_t>(tv.tv_sec);
    int ms = static_cast<int>(tv.tv_usec) / 1000;

    struct tm tm_time;
    localtime_r(&sec, &tm_time);
    static const char* formater = "%4d-%02d-%02d %02d:%02d:%02d.%03d";
    int wsize = snprintf(timestamp_str, MAX_BUFFER_SIZE, formater, tm_time.tm_year + 1900,
                         tm_time.tm_mon + 1, tm_time.tm_mday, tm_time.tm_hour, tm_time.tm_min,
                         tm_time.tm_sec, ms);

    timestamp_str[std::min(wsize, MAX_BUFFER_SIZE - 1)] = '\0';
    return std::string(timestamp_str);
}

#if defined(BYD_HKH_L) && !defined(X86_64)
extern "C" {
#include "isp.h"
}

// extern int ovt_AIAssisted_isp_set_sunglasses(int isp_id, int is_sunglasses);
// extern int ovt_AIAssisted_isp_set_aec(int vs_id, ovt_isp_aec_roi_boundary_t *val);
int ovt_isp_set_sunglasses(int is_sunglasses) {
    return ovt_AIAssisted_isp_set_sunglasses(0, is_sunglasses);
}

int ovt_isp_set_aec(int min_x, int max_x, int max_y, int min_y) {
    // TX_LOG_DEBUG("dms_process.cpp","debug");
    ovt_isp_aec_roi_boundary_t boundary;
    boundary.left = min_x;
    boundary.right = max_x;
    boundary.top = min_y;
    boundary.bottom = max_y;
    return ovt_AIAssisted_isp_set_aec_roi(0, &boundary);
    return 0;
}
#endif

namespace tongxing {
int DmsProcess::Init(const GetBboxFun& get_driving_face_bbox,
                     const GetPointArrayFun& get_driving_face_keypoint,
                     const GetFloatArrayFun& get_driving_face_angle,
                     const GetFloatArrayFun& get_driving_right_eye_close_score,
                     const GetFloatArrayFun& get_driving_left_eye_close_score,
                     const GetIntFun& get_occlusion,
                     const GetFloatArrayFun& get_driving_face_attr,
                     const GetFloatArrayFun& get_driving_right_eye_landmarks,
                     const GetFloatArrayFun& get_driving_left_eye_landmarks,
                     const GetBboxArrayFun& get_phone_cig_bbox,
                     const char* config_file) {
    get_driving_face_bbox_ = get_driving_face_bbox;
    get_driving_face_keypoint_ = get_driving_face_keypoint;
    get_driving_face_angle_ = get_driving_face_angle;
    get_driving_right_eye_close_score_ = get_driving_right_eye_close_score;
    get_driving_left_eye_close_score_ = get_driving_left_eye_close_score;
    get_occlusion_ = get_occlusion;
    get_driving_face_attr_ = get_driving_face_attr;
    get_driving_right_eye_landmarks_ = get_driving_right_eye_landmarks;
    get_driving_left_eye_landmarks_ = get_driving_left_eye_landmarks;
    get_phone_cig_bbox_ = get_phone_cig_bbox;
    tx_dms_out_warning.reset(new dms_out_warning());
    tx_dms_out_warning->dms_init_warning();

    noface_warning.init(6000, 0.8, 0, 10);  //无人脸
    // irblock_warning.init(3000, 1.0, 0, 10);   //红外阻断眼睛
    // mask_warning.init(3000, 1.0, 0, 10);      //戴口罩
    occlusion_warning.init(5000, 0.8, 0, 0);  //遮挡

    phone_det_win.init(1000, 0.7);
    smoking_det_win.init(1000, 0.7);
    phone_smoking_detect_enable_status_ = 0;
    //算法状态初始化时间
    struct timeval tv;
    gettimeofday(&tv, NULL);
    algorithm_start_time = tv.tv_sec * 1000 + tv.tv_usec / 1000;

    return 0;
}

int DmsProcess::SetFatigue(bool type) {
    enable_fatigue_ = type;
    return 0;
}

bool DmsProcess::GetFatigue() {
    return enable_fatigue_;
}

int DmsProcess::DmsRestAlarm() {
    //enable_fatigue_ = State;
    return tx_dms_out_warning->DmsRestAlarm();
}

int DmsProcess::DmsDistractRestAlarm() {
    return tx_dms_out_warning->DistracRestAlarm();
}

int DmsProcess::DmsAlarmSetOk() {
    return tx_dms_out_warning->DmsAlarmSetOk();
}

void DmsProcess::GetTiredInfo(tx_tired& tired_info) {
    return tx_dms_out_warning->GetTiredInfo(tired_info);
}

bool DmsProcess::isAlgoActivate(TXDmsResult& result, const long& now_ts) {
    bool is_activate = true;
#ifdef __USE_ACTIVATE__
    if (ActivateService::instance().GetActivateStatus() == true) {
        result.algorithm_status = ALGORITHM_NORMAL;
    } else {
        if (is_algorithm_experience ||
            ((now_ts - algorithm_start_time) > algorithm_experience_time)) {
            is_algorithm_experience = true;
            result.algorithm_status = ALGORITHM_UNACTIVATED_NO_WORK;
            TX_LOG_INFO("DmsProcess", "DMS IS NOT Activate!!!!!!!!!!");
            is_activate = false;
        } else {
            result.algorithm_status = ALGORITHM_UNACTIVATED_EXPERIENCE_TIME;
        }
    }

#endif
    return is_activate;
}

void DmsProcess::GetDistractionInfo(internal_analysis_distraction_info& info) {
    return tx_dms_out_warning->GetDistractionInfo(info);
}

int DmsProcess::execute(const TXCarInfo* car_info,
                        long long frame_id,
                        TXDmsResult& result_out,
                        long ts) {
    long now_ts;
    float face_angle_score = 1;
    float mouthpoint_score = 1;
    if (ts == 0) {
        struct timeval tv;
        gettimeofday(&tv, NULL);
        now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    } else {
        now_ts = ts;
    }
    // printf("car_speed:%d,time_gap:%ld\n", car_info->speed, now_ts - last_ts);
    // printf("car_info:[mask:%d,speed:%d,steer_whl_snsr_rad:%f,steer_whl_snsr_rad_spd:%f,gear:%d,"
    //        "turn_light:%d,ldw:%d,can_info:%d,camera_info:%d]\n",
    //        car_info->mask, car_info->speed, car_info->steer_whl_snsr_rad,
    //        car_info->steer_whl_snsr_rad_spd, car_info->gear, car_info->turn_light, car_info->ldw,
    //        car_info->can_fault, car_info->camera_fault);
    //帧率控制
    long time_gap = now_ts - last_frame_control_ts;
    if (time_gap < 100) {
        usleep((100 - time_gap) * 1000);
        // printf("time_wait_time:%ld\n", 100 - time_gap);
    }
    last_frame_control_ts = now_ts;


    TXDmsResult result = {0};
    result.result_frame_id = frame_id;
    result.camera_status = Camera_Normal;
    result.drowsiness_status = Drowsiness_Normal;
    result.distraction_status = Distraction_Normal;
    result.system_status = SYSTEM_NORMAL;
    result.algorithm_status = ALGORITHM_NORMAL;

    // 加入激活限制
    if (!isAlgoActivate(result, now_ts)) {
        result_out = result;  // 需要赋值初始状态的result
        return 0;
    }
    
    InternalCameraType temp_camera_status = Camera_Norm;     //临时摄像头状态结果
    InternalCameraType current_camera_type = Camera_Norm;    //存放当前摄像头状态结果
    TXDrowsinessType current_warn_type = Drowsiness_Normal;  //当前疲劳报警类型
    TXDistractionType current_distract_type = Distraction_Normal;      //当前分心报警类型
    TXDistracCaliStatus current_calibrate_status = CALIBRATE_INVALID;  //当前分神标定状态
    TXSystemStatus current_system_status = SYSTEM_NORMAL;              //当前系统状态

    //存放当前帧结果信息
    bool current_no_face = false;
    bool current_camera_occlusion = false;
    bool current_wear_mask = false;
    bool current_wear_ir_block = false;
    bool current_distraction_status = false;
    TXModelOut current_model_out{0};

    float right_up_down_proportion = 1;
    float left_up_down_proportion = 1;

    if (enable_fatigue_ == false) {
        return 0;
    }

    //解析系统故障码
    if (car_info->can_fault != 0 || car_info->camera_fault != 0) {
        if (car_info->can_fault != 0 && car_info->camera_fault != 0) {
            //todo
        } else if (car_info->can_fault != 0) {
            //十六进制数，转换成二进制后，第0位表示开关信号状态，第1位表示车速信号状态，第2位表示方向盘信号状态，第3位表示档位信号状态，第4位表示转向灯信号状态。(0:正常，1:丢失)
            std::vector<int> binaryArray;
            int num = car_info->can_fault;
            // 将整数转换为二进制并存入数组
            while (num > 0) {
                int bit = num % 2;
                binaryArray.push_back(bit);
                num /= 2;
            }

            if (binaryArray.size() <= 4) {
                if (binaryArray.size() == 2 && binaryArray[1])
                    current_system_status = SYSTEM_CAN_FAULT;
                else if (binaryArray.size() == 3 && (binaryArray[1] || binaryArray[2]))
                    current_system_status = SYSTEM_CAN_FAULT;
                else if (binaryArray.size() == 4 &&
                         (binaryArray[1] || binaryArray[2] || binaryArray[3]))
                    current_system_status = SYSTEM_CAN_FAULT;
            } else {
                if (binaryArray[1] || binaryArray[2] || binaryArray[3]) {
                    current_system_status = SYSTEM_CAN_FAULT;
                }
            }

        } else {
            //todo
            current_system_status = SYSTEM_CAMERA_FAULT;
        }
    }

    result.system_status = current_system_status;  //系统故障结果
    if (result.system_status != SYSTEM_NORMAL) {   //如果是硬件故障则立即返回
        if (result.system_status == SYSTEM_CAMERA_FAULT ||
            result.system_status == SYSTEM_CAN_CAMERA_FAULT)
            result.camera_status = Camera_Invalid;
        result.drowsiness_status = Drowsiness_Invalid;
        result.distraction_status = Distraction_Invalid;
        if (tx_dms_out_warning->GetCalibrationStatus()) {
            result.calibrate_status = CALIBRATE_DONE;
        } else {
            result.calibrate_status = current_calibrate_status;
        }
        result_out = result;

        tx_dms_out_warning->DmsRestAlarm();      //重置疲劳时间窗
        tx_dms_out_warning->DistracRestAlarm();  //重置分神时间窗

        return 0;
    }

#if defined(BYD_SC3E) || defined(BYD_SC2EDE)
    //二次标定逻辑
    if (car_info->driver_door_status == TXDriverDoorStatus::DOOR_OPEN ||
        car_info->driver_seat_status != TXDriverSeatStatus::SEAT_STATIC) {
        tx_dms_out_warning->ResetCalibration();  //重置标定状态
        tx_dms_out_warning->DistracRestAlarm();  //重置分神时间窗
    }
#endif

#if defined(BYD_EQ) || defined(BYD_SA2) || defined(BYD_HA6) || defined(BYD_EQ_R) ||                \
    defined(BYD_HKH_L) || defined(BYD_HKH_R)
    //二次标定逻辑
    if (car_info->driver_door_status == TXDriverDoorStatus::DOOR_OPEN) {
        tx_dms_out_warning->ResetCalibration();  //重置标定状态
        tx_dms_out_warning->DistracRestAlarm();  //重置分神时间窗
    }
#endif

    auto bbox_ptr = get_driving_face_bbox_();
    // bool noface_status = noface_warming.update((bbox_ptr->score < 0.5), now_ts, speed);
#ifdef WITH_PHONE_SMOKING_DET
    if (phone_smoking_detect_enable_status_) {
        result.phone_status = NO_PHONE;
        result.smoking_status = NO_SMOKING;
    } else {
        result.phone_status = PHONE_INVALID;
        result.smoking_status = SMOKING_INVALID;
    }
#endif
    if (bbox_ptr->score > 0.5) {
        TX_LOG_DEBUG("DmsProcess", "%f %d %d %d %d", bbox_ptr->score, bbox_ptr->bbox.x,
                     bbox_ptr->bbox.y, bbox_ptr->bbox.width, bbox_ptr->bbox.height);
        // result.driving_seat_status = 1;
#ifdef WITH_PHONE_SMOKING_DET
        if (phone_smoking_detect_enable_status_) {
            auto phone_cig_bboxs = get_phone_cig_bbox_();
            bool flag_has_phone = false;
            bool flag_has_smoking = false;
            for (auto bbox : *phone_cig_bboxs) {
                if (bbox.label == 1) {
                    flag_has_phone = true;
                } else if (bbox.label == 2) {
                    flag_has_smoking = true;
                }
            }
            float percent;
            if (phone_det_win.update(flag_has_phone, now_ts, percent)) {
                result.phone_status = HAS_PHONE;
                std::cout << "HAS_PHONE" << std::endl;
            }
            if (smoking_det_win.update(flag_has_smoking, now_ts, percent)) {
                result.smoking_status = HAS_SMOKING;
            }
        } else {
            result.phone_status = PHONE_INVALID;
            result.smoking_status = SMOKING_INVALID;
        }
#endif
        float score = bbox_ptr->score;
        int xmin = bbox_ptr->bbox.x;
        int ymin = bbox_ptr->bbox.y;
        int width = bbox_ptr->bbox.width;
        int height = bbox_ptr->bbox.height;
        int xmax = xmin + width;
        int ymax = ymin + height;
        result.face_info.score = score;
        result.face_info.xmin = xmin;
        result.face_info.xmax = xmax;
        result.face_info.ymin = ymin;
        result.face_info.ymax = ymax;

        auto face_keypoint_ptr = get_driving_face_keypoint_();
        auto face_attr_ptr = get_driving_face_attr_();

        result.face_info.isMask = face_attr_ptr->operator[](1) >= 0.7;
        result.face_info.isGlass = face_attr_ptr->operator[](0) >= 0.7;
        result.face_info.isIRBlock = face_attr_ptr->operator[](2) >= 0.7;
        // printf("is_mask:%d,is_irblock:%d \n", result.face_info.isMask, result.face_info.isIRBlock);
        // printf("is_mask_score:%f,is_glass_score:%f,is_irblock_score:%f \n",
        //        face_attr_ptr->operator[](1), face_attr_ptr->operator[](0),
        //        face_attr_ptr->operator[](2));

        std::vector<cv::Point> mouth_point;
        for (int i = 0; i < TX_MAX_FLD_SIZE; i++) {
            result.face_info.landmarks[i].x = face_keypoint_ptr->operator[](i).x;
            result.face_info.landmarks[i].y = face_keypoint_ptr->operator[](i).y;
            if (i > 4 && i < 9) {
                mouth_point.push_back(face_keypoint_ptr->operator[](i));
            }
        }
        auto face_angle_ptr = get_driving_face_angle_();
        result.face_info.head_yaw = face_angle_ptr->operator[](1);
        result.face_info.head_pitch = face_angle_ptr->operator[](0);
        result.face_info.head_roll = face_angle_ptr->operator[](2);
        face_angle_score = face_angle_ptr->operator[](3);
        mouthpoint_score = face_angle_ptr->operator[](4);
        // result.face_info.isMask = 0;

        // float mouth_width = face_keypoint_ptr->operator[](7).x - face_keypoint_ptr->operator[](5).x;
        // float mouth_height = face_keypoint_ptr->operator[](8).y - face_keypoint_ptr->operator[](6).y;
        //大角度防止误报红外阻断
        if (fabs(result.face_info.head_yaw) >= 48) {
            result.face_info.isIRBlock = 0;
        }

        // float yawm_v = calculate_mouth_opening(
        //     {face_keypoint_ptr->operator[](5), face_keypoint_ptr->operator[](6),
        //      face_keypoint_ptr->operator[](7), face_keypoint_ptr->operator[](8)});

        TXModelOut temp_model_out{0};

        if ((result.face_info.landmarks[3].x - result.face_info.landmarks[2].x) >= 32 &&
            result.face_info.isIRBlock == 0) {
            auto right_eye_landmark = get_driving_right_eye_landmarks_();
            result.face_info.right_eye_landmark.eye_score = right_eye_landmark->operator[](0);
            result.face_info.right_eye_landmark.eye_angle = right_eye_landmark->operator[](1);
            result.face_info.right_eye_landmark.eye_center.x = right_eye_landmark->operator[](2);
            result.face_info.right_eye_landmark.eye_center.y = right_eye_landmark->operator[](3);
            result.face_info.right_eye_landmark.eye_size.width = right_eye_landmark->operator[](4);
            result.face_info.right_eye_landmark.eye_size.height = right_eye_landmark->operator[](5);
            result.face_info.right_eye_landmark.iris_score = right_eye_landmark->operator[](6);
            result.face_info.right_eye_landmark.iris_center.x = right_eye_landmark->operator[](7);
            result.face_info.right_eye_landmark.iris_center.y = right_eye_landmark->operator[](8);
            result.face_info.right_eye_landmark.iris_radius = right_eye_landmark->operator[](9);
            result.face_info.right_eye_landmark.pupil_score = right_eye_landmark->operator[](10);
            result.face_info.right_eye_landmark.pupil_center.x = right_eye_landmark->operator[](11);
            result.face_info.right_eye_landmark.pupil_center.y = right_eye_landmark->operator[](12);
            result.face_info.right_eye_landmark.pupil_radius = right_eye_landmark->operator[](13);
            result.face_info.right_eye_landmark.yaw = right_eye_landmark->operator[](14);
            result.face_info.right_eye_landmark.pitch = right_eye_landmark->operator[](15);
            result.face_info.right_eye_landmark.opening = right_eye_landmark->operator[](16);
            right_up_down_proportion = right_eye_landmark->operator[](17);
            // std::cout<<"right eye a:"<<right_up_down_proportion<<" yaw:"<<result.face_info.right_eye_landmark.yaw<<" pitch:"<<result.face_info.right_eye_landmark.pitch<<" "<<result.face_info.right_eye_landmark.pupil_score<<result.face_info.right_eye_landmark.iris_score<<result.face_info.right_eye_landmark.eye_score<<std::endl;
            // std::system((std::string("echo ")+std::to_string(result.face_info.right_eye_landmark.yaw * 4.132734+ result.face_info.head_yaw)+","+std::to_string(result.face_info.right_eye_landmark.pitch *2.5+ result.face_info.head_pitch)+std::string(" >> log.txt")).c_str());
        }
        // printf("right_up_down_proportion:%f\n", right_up_down_proportion);

        if ((result.face_info.landmarks[1].x - result.face_info.landmarks[0].x) >= 32 &&
            result.face_info.isIRBlock == 0) {
            {
                auto left_eye_landmark = get_driving_left_eye_landmarks_();
                result.face_info.left_eye_landmark.eye_score = left_eye_landmark->operator[](0);
                result.face_info.left_eye_landmark.eye_angle = left_eye_landmark->operator[](1);
                result.face_info.left_eye_landmark.eye_center.x = left_eye_landmark->operator[](2);
                result.face_info.left_eye_landmark.eye_center.y = left_eye_landmark->operator[](3);
                result.face_info.left_eye_landmark.eye_size.width =
                    left_eye_landmark->operator[](4);
                result.face_info.left_eye_landmark.eye_size.height =
                    left_eye_landmark->operator[](5);
                result.face_info.left_eye_landmark.iris_score = left_eye_landmark->operator[](6);
                result.face_info.left_eye_landmark.iris_center.x = left_eye_landmark->operator[](7);
                result.face_info.left_eye_landmark.iris_center.y = left_eye_landmark->operator[](8);
                result.face_info.left_eye_landmark.iris_radius = left_eye_landmark->operator[](9);
                result.face_info.left_eye_landmark.pupil_score = left_eye_landmark->operator[](10);
                result.face_info.left_eye_landmark.pupil_center.x =
                    left_eye_landmark->operator[](11);
                result.face_info.left_eye_landmark.pupil_center.y =
                    left_eye_landmark->operator[](12);
                result.face_info.left_eye_landmark.pupil_radius = left_eye_landmark->operator[](13);
                result.face_info.left_eye_landmark.yaw = left_eye_landmark->operator[](14);
                result.face_info.left_eye_landmark.pitch = left_eye_landmark->operator[](15);
                result.face_info.left_eye_landmark.opening = left_eye_landmark->operator[](16);
                left_up_down_proportion = left_eye_landmark->operator[](17);
                // std::cout<<"left eye a:"<<left_up_down_proportion<<" yaw:"<<result.face_info.left_eye_landmark.yaw<<" pitch:"<<result.face_info.left_eye_landmark.pitch<<" "<<result.face_info.left_eye_landmark.pupil_score<<result.face_info.left_eye_landmark.iris_score<<result.face_info.left_eye_landmark.eye_score<<std::endl;
            }
        }
        // printf("left_up_down_proportion:%f\n", left_up_down_proportion);
        float fatigue_right = -1.0;
        if (result.face_info.right_eye_landmark.pupil_score > 0 ||
            result.face_info.right_eye_landmark.eye_score <= 0 ||
            result.face_info.right_eye_landmark.opening > 0.1) {
            fatigue_right = 0;
        } else if ((result.face_info.landmarks[3].x - result.face_info.landmarks[1].x) >= 32 &&
                   result.face_info.isIRBlock == 0) {
            fatigue_right = 1;
        }

        float fatigue_left = -1.0;
        if (result.face_info.left_eye_landmark.pupil_score > 0 ||
            result.face_info.left_eye_landmark.eye_score <= 0 ||
            result.face_info.left_eye_landmark.opening > 0.1) {
            fatigue_left = 0;
        } else if ((result.face_info.landmarks[1].x - result.face_info.landmarks[0].x) >= 32 &&
                   result.face_info.isIRBlock == 0) {
            fatigue_left = 1;
        }

        temp_model_out.l_eye_close = fatigue_left;
        temp_model_out.r_eye_close = fatigue_right;

        current_wear_mask = result.face_info.isMask;
        current_wear_ir_block = result.face_info.isIRBlock;

        if (mouthpoint_score >= 0) {
            temp_model_out.mouth_open = calculate_mouth_opening(mouth_point);
        } else {
            temp_model_out.mouth_open = -1;
        }
        // std::cout << "mouthpoint_score:" << mouthpoint_score << " temp_model_out.mouth_open:" << temp_model_out.mouth_open << std::endl;
        result.face_info.right_close_eye_score = temp_model_out.r_eye_close;
        result.face_info.left_close_eye_score = temp_model_out.l_eye_close;
        result.face_info.mouth_opening = temp_model_out.mouth_open;
        //这里将送入疲劳模块进行二次判断
        //如果红外阻断或者瞳孔可见则将闭眼分数置为-1
        if (result.face_info.right_eye_landmark.pupil_score > 0 ||
            result.face_info.right_eye_landmark.iris_score > 0) {
            fatigue_right = 0.0;
        }
        if (result.face_info.left_eye_landmark.pupil_score > 0 ||
            result.face_info.left_eye_landmark.iris_score > 0) {
            fatigue_left = 0.0;
        }

        if (result.face_info.isIRBlock) {
            fatigue_left = -1.0;
            fatigue_right = -1.0;
        }

        temp_model_out.l_eye_close = fatigue_left;
        temp_model_out.r_eye_close = fatigue_right;
        current_model_out = temp_model_out;

        // 眼睛瞳孔分数为0，且没有连续3帧为0，则使用上一帧保存的数据
        if (result.face_info.right_eye_landmark.pupil_score <=
            0 /*&& result.face_info.right_eye_landmark.eye_score <= 0*/) {
            if (++right_eye_lost_count <= 2) {
                result.face_info.right_eye_landmark = last_right_eye_landmark;
                result.face_info.right_eye_landmark.opening = 0.0f;
                // std::cout << "reset righteye info same as saved..." << right_eye_lost_count << std::endl;
            }
        } else {
            last_right_eye_landmark = result.face_info.right_eye_landmark;
            right_eye_lost_count = 0;
        }

        if (result.face_info.left_eye_landmark.pupil_score <=
            0 /* && result.face_info.left_eye_landmark.eye_score <= 0*/) {
            if (++left_eye_lost_count <= 2) {
                result.face_info.left_eye_landmark = last_left_eye_landmark;
                result.face_info.left_eye_landmark.opening = 0.0f;
                // std::cout << "reset lefteye info same as saved..." << left_eye_lost_count << std::endl;
            }
        } else {
            last_left_eye_landmark = result.face_info.left_eye_landmark;
            left_eye_lost_count = 0;
        }
        // std::cout << "r eye:" << result.face_info.right_eye_landmark.pupil_score << " " << result.face_info.right_eye_landmark.pitch <<
        //  " " << result.face_info.right_eye_landmark.yaw  << std::endl;
        // std::cout << "l eye:" << result.face_info.left_eye_landmark.pupil_score << " " << result.face_info.left_eye_landmark.pitch <<
        //  " " << result.face_info.left_eye_landmark.yaw  << std::endl;

        // float tmep_eye = fatigue_left > fatigue_right ? fatigue_left : fatigue_right;

        // printf("---------------- m:%f, e:%.2f, yj:%.2f pj:%.2f \n", temp_model_out.mouth_open, \
        //        tmep_eye,  result.face_info.head_yaw  , result.face_info.head_pitch);

    } else {
        int i_occlusion = get_occlusion_();
        if (i_occlusion == 3 || i_occlusion == 4) {
            current_camera_occlusion = true;
        } else {
            current_no_face = true;
        }

        current_model_out.l_eye_close = 0.0f;
        current_model_out.r_eye_close = 0.0f;
        current_model_out.mouth_open = 0.0f;
    }

    if (last_camera_history_warn_type == Camera_Norm) {
        bool tmp_occlusion_status =
            occlusion_warning.update(current_camera_occlusion, now_ts, car_info->speed);
        bool tmp_no_face_status =
            noface_warning.update((current_camera_occlusion || current_no_face ||
                                   current_wear_mask || current_wear_ir_block),
                                  now_ts, car_info->speed);
        if (tmp_occlusion_status == true) {
            last_camera_history_warn_type = Camera_Occ;
            occlusion_warning.clear(now_ts);
            occlusion_warning.init(1000, 0.99, 0);  //退出条件
            noface_warning.clear(now_ts);
            noface_warning.init(6000, 0.8, 10);  //人脸遮挡进入条件条件

        } else if (tmp_no_face_status == true) {
            last_camera_history_warn_type = No_face;
            noface_warning.clear(now_ts);
            noface_warning.init(2000, 0.99, 0);  //退出条件
            occlusion_warning.clear(now_ts);
            occlusion_warning.init(5000, 0.8, 0);  //相机遮挡进入条件条件
        }
        result.camera_status = (TXCameraStatus)Camera_Normal;
    } else if (last_camera_history_warn_type == Camera_Occ) {
        bool tmp_exit_occlusion_status =
            occlusion_warning.update(!current_camera_occlusion, now_ts, car_info->speed);
        bool tmp_no_face_status =
            noface_warning.update((current_camera_occlusion || current_no_face ||
                                   current_wear_mask || current_wear_ir_block),
                                  now_ts, car_info->speed);
        if (tmp_exit_occlusion_status) {
            if (tmp_no_face_status) {
                last_camera_history_warn_type = No_face;
                noface_warning.clear(now_ts);
                noface_warning.init(2000, 0.99, 0);  //退出条件
                occlusion_warning.clear(now_ts);
                occlusion_warning.init(5000, 0.8, 0);  //相机遮挡进入条件条件
            } else {
                last_camera_history_warn_type = Camera_Norm;
                occlusion_warning.clear(now_ts);
                occlusion_warning.init(5000, 0.8, 0);  //相机遮挡进入条件条件
                noface_warning.clear(now_ts);
                noface_warning.init(6000, 0.8, 10);  //人脸遮挡进入条件条件
            }
        }
        result.camera_status = (TXCameraStatus)Camera_Occlusion;
    } else if (last_camera_history_warn_type == No_face) {
        bool tmp_exit_no_face_status =
            noface_warning.update(!(current_camera_occlusion || current_no_face ||
                                    current_wear_mask || current_wear_ir_block),
                                  now_ts, car_info->speed);
        bool tmp_occlusion_status =
            occlusion_warning.update(current_camera_occlusion, now_ts, car_info->speed);

        if (tmp_occlusion_status) {
            last_camera_history_warn_type = Camera_Occ;
            occlusion_warning.clear(now_ts);
            occlusion_warning.init(1000, 0.99, 0);  //退出条件
            noface_warning.clear(now_ts);
            noface_warning.init(6000, 0.8, 10);  //人脸遮挡进入条件条件
        } else if (tmp_exit_no_face_status) {
            {
                last_camera_history_warn_type = Camera_Norm;
                occlusion_warning.clear(now_ts);
                occlusion_warning.init(5000, 0.8, 0);  //相机遮挡进入条件条件
                noface_warning.clear(now_ts);
                noface_warning.init(6000, 0.8, 10);  //人脸遮挡进入条件条件
            }
        }
        result.camera_status = (TXCameraStatus)Face_Occlusion;
    }
    //判断摄像头是否异常
    TXWarnInfo warn_info_out{0};
    if (result.camera_status == Camera_Normal) {
        //获取分心结果
        int distract_status = 0;
        // if (tx_dms_out_warning->GetCalibrationStatus() || car_info->speed < 20) {

        //这里做快速摇头晃脑带来的误检分神
        // if (tx_dms_out_warning->GetHeadPoseGazeShake(result.face_info)) {
        //     current_distract_type = TXDistractionType::Distraction_Normal;  //快速转头时不为分神
        //     tx_dms_out_warning->DistracRestAlarm();  //清除之前的数据重新计算
        //     printf("快速转头,分神重置\n");
        // } else {
        if (last_camera_history_warn_type == No_face ||
            last_camera_history_warn_type == Camera_Norm) {
            current_distraction_status = tx_dms_out_warning->GetCurrentDistractStatus(
                result.face_info, car_info, right_up_down_proportion, left_up_down_proportion,
                face_angle_score, current_camera_occlusion, now_ts);
        } else {
            if (last_camera_history_warn_type == Camera_Occ) {
                tx_dms_out_warning->DistracRestAlarm();
            }
            current_distraction_status = false;
        }

        //笑时误报分神,由于yaw引起的分神过滤
        if (result.face_info.mouth_opening > 0.5 && current_distraction_status &&
            (result.face_info.head_yaw < 40)) {
            current_distraction_status = false;
        }

        current_distract_type =
            tx_dms_out_warning->distrac_run_warning(now_ts, car_info, current_distraction_status);
        // std::cout << "current_distraction_status:" << current_distraction_status << " current_distract_type:" << current_distract_type << std::endl;
        if ((current_distract_type == Distraction_Fatigue ||
             current_distract_type == Distraction_Fatigue_Short ||
             current_distract_type == Distraction_Fatigue_Long) &&
            is_clear_eyeframes == false) {
            tx_dms_out_warning->DmsRestEyeAlarm();
            is_clear_eyeframes = true;
        } else if (current_distract_type == Distraction_Normal) {
            is_clear_eyeframes = false;
        }
        // std::cout<<tx_dms_out_warning->GetDistractReason()<<" current_distract_type="<<current_distract_type<<" current_distraction_status="<<current_distraction_status<<std::endl;
        // }

        //防止靠着窗户误报分神 司机稍微歪头开车，就会误报分神
        // float min_value = 0.0f;
        // float max_value = 0.0f;
        // tx_dms_out_warning->GetHeadPoseRoll(min_value, max_value);
        // if (result.face_info.head_roll < min_value || result.face_info.head_roll > max_value) {
        //     current_distraction_status = false;
        // }

        // if (car_info->speed < 20 && tx_dms_out_warning->GetCalibrationStatus() == false) {
        //     distract_status = 2;
        // } else {
        //     distract_status = 0;
        // }

        // } else {
        // if (!tx_dms_out_warning->GetCalibrationStatus() || !current_distraction_status) {
        distract_status =
            tx_dms_out_warning->auto_calibration_distraction(result.face_info, car_info,
                                                             now_ts);  //自动标定
        // }
        // distract_status =
        //     tx_dms_out_warning->auto_calibration_distraction(result.face_info, car_info,
        //                                                         now_ts);  //自动标定
        //     current_distract_type = Distraction_Invalid;
        // }

        current_calibrate_status = (TXDistracCaliStatus)distract_status;

        //获取疲劳结果
        if (tx_dms_out_warning->GetCalibrationStatus()) {
            float min_value = 0.0f;
            float max_value = 0.0f;
            tx_dms_out_warning->GetHeadPoseRoll(min_value, max_value); //在头的roll角符合范围时进行疲劳误检的抑制
            if (result.face_info.head_roll >= min_value && result.face_info.head_roll <= max_value) {

                tx_dms_out_warning->GetHeadPosePitch(min_value, max_value);
                if (result.face_info.head_pitch < min_value ||
                    result.face_info.head_pitch > max_value) {
                    //当头部pitch角度小于标定的最小pitch角度时，模型检出闭眼不算闭眼（防止误报疲劳）
                    current_model_out.l_eye_close = 0.1f;
                    current_model_out.r_eye_close = 0.1f;
                }

                tx_dms_out_warning->GetHeadPoseYaw(min_value, max_value);
                if (result.face_info.head_yaw < min_value || result.face_info.head_yaw > max_value) {
                    //当头部yaw角度小于标定减去偏差值的最小yaw角度时，模型检出闭眼不算闭眼（防止误报疲劳）
                    current_model_out.l_eye_close = 0.1f;
                    current_model_out.r_eye_close = 0.1f;
                }
            }
        }
        current_warn_type = tx_dms_out_warning->dms_run_warning(now_ts, result.face_info, car_info,
                                                                &current_model_out, warn_info_out);

    } else {
        // printf("last_camera_history_warn_type:%d, now reset Alarm!\n",
        //        last_camera_history_warn_type);
        tx_dms_out_warning->DmsRestAlarm();  //重置报警
        tx_dms_out_warning->DistracRestAlarm();

        if (tx_dms_out_warning->GetCalibrationStatus() == false) {
            tx_dms_out_warning
                ->ResetCalibration();  //当在未标定成功的状态下，异常情况下需要重新标定
            current_calibrate_status = CALIBRATE_INVALID;
        } else {
            current_calibrate_status = CALIBRATE_DONE;
        }
    }

    //  (result.camera_status == Camera_Occlusion)
    // if (result.camera_status == Face_Occlusion && car_info->speed == 0) {
    //     tx_dms_out_warning->ResetCalibration();  //重新进行二次人脸分神标定
    // }

    result.face_info.warnInfo = warn_info_out;
    //分神结果
    result.distraction_status = current_distract_type;
    //
    if (result.camera_status != Camera_Normal) {
        result.drowsiness_status = Drowsiness_Invalid;
        result.distraction_status = Distraction_Invalid;

        if (result.camera_status == Face_Occlusion && car_info->speed < 10) {
            result.camera_status = Camera_Normal;
        }

        if (is_first_face_occ && result.camera_status == Face_Occlusion) {
            result.camera_status = Camera_Normal;
        }

        if (!is_first_face_occ && result.camera_status == Face_Occlusion &&
            last_camera_history_warn_type == Camera_Norm) {
            is_first_face_occ = true;
        }
    } else {
        //速度必须大于10km/h
        // if (car_info->speed >= 10) {
        result.drowsiness_status = current_warn_type;
        // } else {
        //     result.drowsiness_status = Drowsiness_Normal;
        // }
    }
    //标定状态结果
    result.calibrate_status = current_calibrate_status;

    leye_thr = left_up_down_proportion;
    reye_thr = right_up_down_proportion;

    // CALLBACK_:
    // if (callback_)
    {
        TX_LOG_DEBUG(
            "DmsProcess",
            "frame_id:%ld face info %f ,%d ,%d ,%d ,%d camera_status:%d drowsiness_status:%d "
            "distraction_status:%d,calibrate_status:%d ",
            result.result_frame_id, result.face_info.score, result.face_info.xmin,
            result.face_info.ymin, result.face_info.xmax, result.face_info.ymax,
            result.camera_status, result.drowsiness_status, result.distraction_status,
            result.calibrate_status);
        if (now_ts - last_ts >= 1000) {
            printf("[%s]DmsProcess [camera_status:%d drowsiness_status:%d "
                   "distraction_status:%d,calibrate_status:%d ] \n",
                   getCurrentTimes().c_str(), result.camera_status, result.drowsiness_status,
                   result.distraction_status, result.calibrate_status);
            last_ts = now_ts;
        }
        // #ifdef __USE_ACTIVATE__
        //         if (ActivateService::instance().GetActivateStatus() == false) {
        //             LOG_INFO("DmsProcess", "DMS IS NOT Activate!!!!!!!!!!");
        //             result.dms_status = Normal;
        //         }
        // #endif
        result_out = result;
        // callback_(&result, user_data_);
    }
    return 0;
}

int DmsProcess::GetRightLeftEyeThr(float& left_eye_thr, float& right_eye_thr) {
    left_eye_thr = leye_thr;
    right_eye_thr = reye_thr;
    return 0;
}

std::string DmsProcess::GetDistractReason(TXCameraStatus camera_status) {
    if (camera_status == Camera_Normal)
        return tx_dms_out_warning->GetDistractReason();
    else
        return "invalid";
}

std::string DmsProcess::GetDistractParamers() {
    return tx_dms_out_warning->GetDistractParamers();
}

int DmsProcess::SetPhoneAndSmokingDetectEnableStatus(unsigned char flag) {
    phone_smoking_detect_enable_status_ = flag;
    phone_det_win.clear();
    smoking_det_win.clear();
    return 0;
}
}  // namespace tongxing