#include "tx_media_server_interface.h"
#include "cc_media_server.h"
#define EXPORT __attribute__((visibility("default")))
#ifdef __cplusplus
extern "C" {
#endif
EXPORT int TXMediaServerGetConnectStatus(){
    return tongxing::CcMediaServer::instance().get_push_data_status();
}
EXPORT int TXMediaServerPushImageAndMessage(const TXMediaServerImageInfo *image,char* message,int message_len,int scale){
        return    tongxing::CcMediaServer::instance().push_image_and_message(
            (const tongxing::CcMediaServer::ImageInfo*)image,std::string(message,message_len));
}
#ifdef __cplusplus
}
#endif