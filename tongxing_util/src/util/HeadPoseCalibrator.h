#ifndef HEAD_POSE_CALIBRATOR_H
#define HEAD_POSE_CALIBRATOR_H

#include <iostream>
#include <vector>
#include <deque>
#include <cmath>
#include <algorithm>
#include <opencv2/opencv.hpp>

typedef struct calistatus_ {
    bool head_cali_finish;
    bool leye_cali_finish;
    bool reye_cali_finish;
}calistatus;

namespace tongxing{

class HeadPoseCalibrator {
    public:
        HeadPoseCalibrator(int queue_size = 100, int queue_start_frame=50 ,int k_num = 2, 
            float cluster_radius = 6.0, float threshold_50f = 0.7, float threshold_100f = 0.8, float threshold_longer = 0.9,
            float eye_cluster_radius = 6.0, float eye_threshold_50f = 0.7, float eye_threshold_100f = 0.8, float eye_threshold_longerf = 0.9);
        void init();
        void addNewPoint(const std::vector<float>& new_point, 
            std::deque<std::vector<float>>& data_deque_, std::vector<std::vector<float>>& distance_matrix);
        bool execute(std::vector<float>& head_angles, std::vector<float>& lefteye_angles, 
        std::vector<float>& righteye_angles, std::vector<float>& centroid_result, 
        std::vector<float>& leye_centroid_result, std::vector<float>& reye_centroid_result, calistatus &status);
        bool aggregationCheck(std::deque<std::vector<float>> &data_deque_, std::vector<std::vector<float>> &dis_matrix, 
        float cluster_radius_, float threshold,  std::pair<size_t, std::vector<size_t>> &founded_index);
        void clear();

    private:
        int headqueue_size_;
        int queue_start_frame_;
        int k_num_;
        float cluster_radius_;
        float threshold_50f_;
        float threshold_100f_;
        float threshold_longerf_;
        float eye_cluster_radius_;
        float eye_threshold_50f_;
        float eye_threshold_100f_;
        float eye_threshold_longerf_;

        int process_counter_;
        bool has_cali_ok = false;
        bool global_cali_finish = false;
        std::vector<std::vector<float>> headdis_matrix;
        std::vector<std::vector<float>> leyedis_matrix;
        std::vector<std::vector<float>> reyedis_matrix;

        std::deque<std::vector<float>> headdata_deque_;
        std::deque<std::vector<float>> leyedata_deque_;
        std::deque<std::vector<float>> reyedata_deque_;

        std::vector<float> thelast_head_centroid;
        std::vector<float> thelast_leye_centroid;
        std::vector<float> thelast_reye_centroid;

};
}
#endif // HEAD_POSE_CALIBRATOR_H
