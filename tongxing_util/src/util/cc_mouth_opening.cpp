#include "cc_mouth_opening.h"
#include <math.h>
namespace tongxing{

static float getAngelOfTwoVector(const cv::Point &pt1,const cv::Point &pt2,const cv::Point &c)
{
	float theta = atan2(pt1.y - c.y, pt1.x - c.x) - atan2(pt2.y - c.y, pt2.x - c.x);
	if (theta > CV_PI)
		theta -= 2 * CV_PI;
	if (theta < -CV_PI)
		theta += 2 * CV_PI;
 
	theta = theta * 180.0 / CV_PI;
	return theta;
}
    float calculate_mouth_opening(const std::vector<cv::Point>& mouth_points){
        cv::Point2f vec1,vec2,vec;
        vec1.x=mouth_points[0].x-(mouth_points[1].x+mouth_points[3].x)/2;
        vec1.y=mouth_points[0].y-(mouth_points[1].y+mouth_points[3].y)/2;
        vec2.x=mouth_points[2].x-(mouth_points[1].x+mouth_points[3].x)/2;
        vec2.y=mouth_points[2].y-(mouth_points[1].y+mouth_points[3].y)/2;
        float distance0=vec1.x*vec1.x+vec1.y*vec1.y;
        float distance1=vec2.x*vec2.x+vec2.y*vec2.y;
        // std::cout<<distance0<<" "<<distance1<<std::endl;
        float theta = 0;
        // float av = (mouth_points[0].z+mouth_points[1].z+mouth_points[2].z+mouth_points[3].z)/4;
        // if (av<0.44)
        // {
        //     return -1;
        // }
      
        
        if(distance0>distance1){
            theta=getAngelOfTwoVector(mouth_points[1],mouth_points[3],mouth_points[0]);
        }
        else{
            theta=getAngelOfTwoVector(mouth_points[1],mouth_points[3],mouth_points[2]);
        }
        // vec=vec2-vec1;
    //    std::cout<<theta<<std::endl;
        // std::cout<<vec1<<vec2<<vec<<radian<<std::endl;
        return abs(theta/(90));
    }
}