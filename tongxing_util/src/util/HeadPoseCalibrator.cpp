#include "HeadPoseCalibrator.h"

namespace tongxing{

    HeadPoseCalibrator::HeadPoseCalibrator(int queue_size, int queue_start_frame,\
        int k_num, float cluster_radius,float threshold_50f, float threshold_100f, float threshold_longer,
        float eye_cluster_radius,float eye_threshold_50f, float eye_threshold_100f, float eye_threshold_longer)
        : headqueue_size_(queue_size), queue_start_frame_(queue_start_frame),k_num_(k_num), 
            cluster_radius_(cluster_radius), threshold_50f_(threshold_50f), threshold_100f_(threshold_100f), threshold_longerf_(threshold_longer),
            eye_cluster_radius_(eye_cluster_radius), eye_threshold_50f_(eye_threshold_50f), eye_threshold_100f_(eye_threshold_100f), eye_threshold_longerf_(eye_threshold_longer),

            process_counter_(0) {
    }

    void HeadPoseCalibrator::init() {
        headdata_deque_.resize(headqueue_size_+1);
        headdata_deque_.clear();
        headdis_matrix.reserve(headqueue_size_+1);
        headdis_matrix.clear();

        leyedata_deque_.resize(headqueue_size_+1);
        leyedata_deque_.clear();
        leyedis_matrix.reserve(headqueue_size_+1);
        leyedis_matrix.clear();

        reyedata_deque_.resize(headqueue_size_+1);
        reyedata_deque_.clear();
        reyedis_matrix.reserve(headqueue_size_+1);
        reyedis_matrix.clear();

        thelast_head_centroid.clear();
        thelast_leye_centroid.clear();
        thelast_reye_centroid.clear();
        process_counter_ = 0;
        has_cali_ok = false;
        global_cali_finish = false;
    }

    inline float euclideanDistance(const std::vector<float>& a, const std::vector<float>& b) {
        float sum = 0.0;
        for (size_t i = 0; i < a.size(); ++i) {
            float diff = a[i] - b[i];
            sum += diff * diff;
        }
        return std::sqrt(sum);
    }

    
    void HeadPoseCalibrator::addNewPoint(const std::vector<float>& new_point, 
        std::deque<std::vector<float>>& data_deque_, std::vector<std::vector<float>>& distance_matrix) {
        size_t n = data_deque_.size();

        // 只计算新加入的数据与原有数据的距离
        std::vector<float> new_distances;
        new_distances.reserve(headqueue_size_+1);
        for (size_t i = 0; i < n; ++i) {
            float distance = euclideanDistance(new_point, data_deque_[i]);
            new_distances.push_back(distance);
        }

        distance_matrix.push_back(new_distances);
        for (size_t i = 0; i < n; ++i) {
            distance_matrix[i].push_back(new_distances[i]);
        }

        data_deque_.push_back(new_point);

        // 如果超过窗口大小，移除最旧的数据点
        if (data_deque_.size() > headqueue_size_) {
            // 移除最旧的数据点
            data_deque_.pop_front();

            // 移除距离矩阵中对应的行和列
            distance_matrix.erase(distance_matrix.begin());
            for (auto& row : distance_matrix) {
                row.erase(row.begin());
            }    
        }
    }

    // 聚集度判断
    bool HeadPoseCalibrator::aggregationCheck(std::deque<std::vector<float>> &data_deque_, std::vector<std::vector<float>> &dis_matrix, 
        float cluster_radius_, float threshold, std::pair<size_t, std::vector<size_t>> &founded_index) {

        // 1. 对数据点进行一轮聚集程度的筛选，找出满足条件的点集
        // 根据距离矩阵计算聚集程度
        size_t n = data_deque_.size();
        std::vector<size_t> counts;

        bool found = false;
        float max_ratio = 0.0;
        float ratio = 0.0;

        for (size_t i = 0; i < n; ++i) {
            counts.clear();

            for (size_t j = 0; j < n; ++j) {
                if (i == j) continue;
                if (dis_matrix[i][j] <= cluster_radius_) {
                    counts.push_back(j); // 存储符合条件的点的索引
                    // std::cout << "headdis_matrix[i][j]:" << headdis_matrix[i][j] << std::endl;
                }
            }
            ratio = static_cast<float>(counts.size()) / (n-1);
            if (ratio >= threshold) {
                found = true;
            }
            if (ratio >= max_ratio) {
                max_ratio = ratio;
                founded_index = {i, counts}; // 找出符合条件最多的
            }
        }

        // std::cout << "ratio:" << max_ratio << " " << threshold << std::endl;
        return found;
    }

    bool HeadPoseCalibrator::execute(std::vector<float>& head_angles, std::vector<float>& lefteye_angles, 
        std::vector<float>& righteye_angles, std::vector<float>& centroid_result, 
        std::vector<float>& leye_centroid_result, std::vector<float>& reye_centroid_result, calistatus &status) {
        
        // 重置传入参数地址
        status.head_cali_finish = false;
        status.leye_cali_finish = false;
        status.reye_cali_finish = false;
        centroid_result.clear();
        leye_centroid_result.clear();
        reye_centroid_result.clear();

        // 每一帧更新deque和距离矩阵
        addNewPoint(head_angles, headdata_deque_, headdis_matrix);
        addNewPoint(lefteye_angles, leyedata_deque_, leyedis_matrix);
        addNewPoint(righteye_angles, reyedata_deque_, reyedis_matrix);
        if (headdata_deque_.size() <= queue_start_frame_-10) {
            return false;
        }

        // 每10帧判断一次是否标定成功 
        process_counter_++;
        if (global_cali_finish) {
            if (process_counter_ < 50) {
                return false;
            }
        } else {
            if (process_counter_ < 10) {
                return false;
            }
        }

        process_counter_ = 0;

        // 标定的阈值在不同的累积帧数下不同
        float threshold = 0.0;
        float eye_threshold = 0.0;
        if (headdata_deque_.size() >= 100 && headdata_deque_.size() < 200) {
            threshold = threshold_50f_;
            eye_threshold = eye_threshold_50f_;
        }
        else if (headdata_deque_.size() >= 200 && headdata_deque_.size() < 300) {
            threshold = threshold_100f_;
            eye_threshold = eye_threshold_100f_;
        }
        else {
            threshold = threshold_longerf_;
            eye_threshold = eye_threshold_longerf_;
        }
        // 在队列被填充满，但还未标定成功时，会强制输出一个标定值，后续再慢慢修正
        if (headdata_deque_.size() >= headqueue_size_ && has_cali_ok == false) {
            threshold = 0.1;
            eye_threshold = 0.1;
        }

        //聚集度判断
         std::pair<size_t, std::vector<size_t>> founded_index;
         std::pair<size_t, std::vector<size_t>> leyefounded_index;
         std::pair<size_t, std::vector<size_t>> reyefounded_index;
        std::vector<size_t> leyecounts;
        std::vector<size_t> reyecounts;

        bool cali_status = false;
        bool head_found = false;
        bool reye_found = false;
        bool leye_found = false;

        head_found = aggregationCheck(headdata_deque_, headdis_matrix, cluster_radius_, threshold, founded_index);

        // 头部不满足聚集度，不进行后续的眼睛判断
        if (!head_found) {
            return false;
        }
        
        // 对眼睛使用同样的聚集度判断
        leye_found = aggregationCheck(leyedata_deque_, leyedis_matrix, eye_cluster_radius_, eye_threshold, leyefounded_index);
        reye_found = aggregationCheck(reyedata_deque_, reyedis_matrix, eye_cluster_radius_, eye_threshold, reyefounded_index);

        // 当有一项符合要求时返回true
        if (head_found || leye_found || reye_found)
        {
            cali_status = true;
        }

        std::vector<std::vector<float>> selected_points;
        std::vector<std::vector<float>> leye_selected_points;
        std::vector<std::vector<float>> reye_selected_points;

        for (auto index : founded_index.second) {
            selected_points.push_back(headdata_deque_[index]);
        }
        // std::cout << "selected_points.size():" << selected_points.size() << std::endl;
        for (auto index : leyefounded_index.second) {
            leye_selected_points.push_back(leyedata_deque_[index]);
        }
        // std::cout << "leye_selected_points.size():" << leye_selected_points.size() << std::endl;
        for (auto index : reyefounded_index.second) {
            reye_selected_points.push_back(reyedata_deque_[index]);
        }
        // std::cout << "reye_selected_points.size():" << reye_selected_points.size() << std::endl;

        std::vector<float> centroid(3, 0.0);
        if (head_found) {
            // 2.对筛选出的点集进行 k-means 聚类
            cv::Mat data_mat(static_cast<int>(selected_points.size()), 3, CV_32F);
            for (size_t i = 0; i < selected_points.size(); ++i) {
                data_mat.at<float>(static_cast<int>(i), 0) = static_cast<float>(selected_points[i][0]);
                data_mat.at<float>(static_cast<int>(i), 1) = static_cast<float>(selected_points[i][1]);
                data_mat.at<float>(static_cast<int>(i), 2) = static_cast<float>(selected_points[i][2]);
            }

            cv::Mat labels;
            cv::Mat centers;

            int attempts = 10;
            cv::TermCriteria criteria = cv::TermCriteria(cv::TermCriteria::MAX_ITER + cv::TermCriteria::EPS, 100, 1e-4);
            cv::kmeans(data_mat, k_num_, labels, criteria, attempts, cv::KMEANS_PP_CENTERS, centers);

            // 找到包含最多数据点的簇
            std::vector<int> cluster_counts(k_num_, 0);
            for (int i = 0; i < labels.rows; ++i) {
                int label = labels.at<int>(i, 0);
                cluster_counts[label]++;
            }
            int max_cluster_index = std::distance(cluster_counts.begin(), std::max_element(cluster_counts.begin(), cluster_counts.end()));
            int max_cluster_count = cluster_counts[max_cluster_index];

            // 获取质心坐标
            cv::Mat centroid_mat = centers.row(max_cluster_index);
            centroid = {
                static_cast<float>(centroid_mat.at<float>(0, 0)),
                static_cast<float>(centroid_mat.at<float>(0, 1)),
                static_cast<float>(centroid_mat.at<float>(0, 2))
            };
        }

        // 计算符合条件的左右眼角度均值
        std::vector<float> leye_centroid(2, 0.0);
        std::vector<float> reye_centroid(2, 0.0);
        if (leye_found) {
            size_t num_leye_points = leye_selected_points.size();
            if (num_leye_points > 0) {
                for (const auto& point : leye_selected_points) {
                    leye_centroid[0] += point[0];
                    leye_centroid[1] += point[1];
                }
                leye_centroid[0] /= num_leye_points;
                leye_centroid[1] /= num_leye_points;
            }
        }
        if (reye_found) {
            size_t num_reye_points = reye_selected_points.size();
            if (num_reye_points > 0) {
                for (const auto& point : reye_selected_points) {
                    reye_centroid[0] += point[0];
                    reye_centroid[1] += point[1];
                }
                reye_centroid[0] /= num_reye_points;
                reye_centroid[1] /= num_reye_points;
            }
        }

        // 二次标定的值需要依据第一次标定的值
        if (global_cali_finish) {
            // 两次标定差异过大，则丢弃本次数据
            if (thelast_head_centroid.size() > 0) {
                float head_dis = euclideanDistance(thelast_head_centroid, centroid);
                if (head_dis > 5.0) {
                    centroid = thelast_head_centroid;
                    cali_status = false;
                }
            }
            if (thelast_leye_centroid.size() > 0) {
                float leye_dis = euclideanDistance(thelast_leye_centroid, leye_centroid);
                if (leye_dis > 2.0) {
                    leye_centroid = thelast_leye_centroid;
                    cali_status = false;
                }
            }
            if (thelast_reye_centroid.size() > 0) {
                float reye_dis = euclideanDistance(thelast_reye_centroid, reye_centroid);
                if (reye_dis > 2.0) {
                    reye_centroid = thelast_reye_centroid;
                    cali_status = false;
                }
            }
        }
        // 上一次标定完成的数据
        if (!global_cali_finish && cali_status) {
            thelast_head_centroid = centroid;
            thelast_leye_centroid = leye_centroid;
            thelast_reye_centroid = reye_centroid;
        }

        if (head_found && leye_found && reye_found) {
            has_cali_ok = true;
            global_cali_finish = true;
        }

        // 返回结果
        centroid_result = centroid;
        leye_centroid_result = leye_centroid;
        reye_centroid_result = reye_centroid;

        status.head_cali_finish = head_found;
        status.leye_cali_finish = leye_found;
        status.reye_cali_finish = reye_found;

        return cali_status; 
    }

    void HeadPoseCalibrator::clear() {
        headdata_deque_.clear();
        reyedata_deque_.clear();
        leyedata_deque_.clear();
        headdis_matrix.clear();
        leyedis_matrix.clear();
        reyedis_matrix.clear();
        thelast_head_centroid.clear();
        thelast_leye_centroid.clear();
        thelast_reye_centroid.clear();

        process_counter_ = 0;
        has_cali_ok = false;
        global_cali_finish = false;
    }
}
// example
// int main(int argc, char const *argv[]) {

//     int queue_length = 0;
//     int k_num = 0;
//     float cluster_radius = 0.0;
//     float threshold_50f = 0.0;
//     float threshold_100f = 0.0;
//     float threshold_longerf = 0.0;
//     // ---------------eyes config---------------
//     float eye_cluster_radius = 0.0;
//     float eye_threshold_50f = 0.0;
//     float eye_threshold_100f = 0.0;
//     float eye_threshold_longerf = 0.0;

//     readconfig("./config.json", queue_length, k_num, cluster_radius, \
//         threshold_50f, threshold_100f, threshold_longerf,
//         eye_cluster_radius, eye_threshold_50f, eye_threshold_100f, eye_threshold_longerf);
//     std::cout << "queue_length:" << queue_length << " k_num:" << k_num << std::endl;
//     std::cout << "cluster_radius:" << cluster_radius << " threshold_50f:" << threshold_50f << " threshold_100f:" << threshold_100f <<
//      " threshold_longerf:" << threshold_longerf << std::endl;
//     std::cout << "eye_cluster_radius:" << eye_cluster_radius << " eye_threshold_50f:" << eye_threshold_50f << " eye_threshold_100f:" << eye_threshold_100f <<
//      " eye_threshold_longerf:" << eye_threshold_longerf << std::endl;
//     HeadPoseCalibrator calibrator(queue_length, 50,k_num ,
//         cluster_radius, threshold_50f, threshold_100f, threshold_longerf,
//          eye_cluster_radius, eye_threshold_50f, eye_threshold_100f, eye_threshold_longerf);
//     calibrator.init();

//     std::string parse_path = std::string(argv[1]);

//     std::vector<std::string> suffix = {".json"};
//     std::vector<std::string> filelist;
//     read_files(parse_path, suffix, filelist);
//     std::cout << "filelist.size():" << filelist.size() << std::endl;
//     int cnt = 0;
//     for (auto& file : filelist) {
//         // std::cout << cnt<< " file:" << file << std::endl;
//         float headpitch = 0.0;
//         float headyaw = 0.0;
//         float headroll = 0.0;
//         float leyepitch = 0.0;
//         float leyeyaw = 0.0;
//         float reyepitch = 0.0;
//         float reyeyaw = 0.0;
//         cnt ++;
//         readheadpose(file.c_str(), headpitch, headyaw, headroll, leyepitch, leyeyaw, reyepitch, reyeyaw);
//         // std::cout << "read leye pitch:"<<leyepitch << " yaw:" << leyeyaw <<
//         //  " reye pitch:"<< reyepitch << " yaw:"<< reyeyaw << std::endl;
//         std::vector<float> head_angles = {headpitch, headyaw, headroll};
//         std::vector<float> lefteye_angles = {leyepitch, leyeyaw};
//         std::vector<float> righteye_angles = {reyepitch, reyeyaw};
//         std::vector<float> centroid;
//         std::vector<float> leyecentroid;
//         std::vector<float> reyecentroid;
//         calistatus status;
//         if (leyepitch == 0 || reyepitch == 0 || leyeyaw == 0 || reyeyaw == 0)
//         {
//             // std::cout << "eye not visuble, continue..." << std::endl;
//             continue;
//         }
//         // 在头和眼睛有一个满足要求时，返回成功，但需要继续标定直到头部和眼睛都满足要求为止
//         if (calibrator.execute(head_angles, lefteye_angles, righteye_angles, centroid, leyecentroid, reyecentroid, status)) {
//             std::cout << "cali cnt:" << cnt << std::endl;
//             std::cout << "cali success! centroid position:" << std::endl;
//             if (status.head_cali_finish) {
//                 std::cout << "Pitch: " << centroid[0] << std::endl;
//                 std::cout << "Yaw:   " << centroid[1] << std::endl;
//                 std::cout << "Roll:  " << centroid[2] << std::endl;
//             }
//             if (status.leye_cali_finish) {
//                 std::cout << "leye pitch: " << leyecentroid[0] << std::endl;
//                 std::cout << "leye yaw: " << leyecentroid[1] << std::endl;
//             }
//             if (status.reye_cali_finish) {
//                 std::cout << "reye pitch: " << reyecentroid[0] << std::endl;
//                 std::cout << "reye yaw: " << reyecentroid[1] << std::endl;
//             }

//             // 在头和眼睛都校准完成时，跳出循环
//             if (status.head_cali_finish && (status.leye_cali_finish || status.reye_cali_finish))
//                 break;
//         }

//     }

//     calibrator.clear();

//     return 0;
// }

