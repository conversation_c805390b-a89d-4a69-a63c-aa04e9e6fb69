#ifndef _SVM_MODEL_H_
#define _SVM_MODEL_H_
#include <opencv2/opencv.hpp>
#include "cc_module.h"

namespace tongxing {
    typedef enum eyesight_region
    {
        eyesight_region_none = -1,
        eyesight_region_front = 0,
        eyesight_region_center_panel = 1,
        // eyesight_region_left_mirror = 2,
        // eyesight_region_right_mirror = 3
        eyesight_region_left = 2,
        eyesight_region_right = 3
    }eyesight_region_;

    class CcSvmModel : public CcModule
    {
        public:
            CcSvmModel();
            ~CcSvmModel();
            int init(int mapping_width, int mapping_height, 
                const std::vector<std::vector<cv::Point2f>> &hull, float tolerate_percentage);

            static std::shared_ptr<CcSvmModel> instance;
            static std::shared_ptr<CcSvmModel> getInstance();
            int predict(float pitch, float yaw,  float roll, int region,cv::Point2f &point);
            int isinHull(cv::Point2f point, std::vector<std::vector<cv::Point2f>> hulls, double tolerate_percentage);
            cv::Point2f angle3d_mappingto_len2d(std::vector<float>& angle3d);
            int set_cali_angle3d(float pitch_cali, float yaw_cali,  float roll_cali);
            void clear();

        private:
            bool init_flag = false;
            std::shared_ptr<cv::ml::SVM> svm_;
            int mapping_width = 200, mapping_height = 200;
            float tolerate_percentage = 0.0;
            std::vector<std::vector<std::vector<cv::Point2f>>> hull_;
            std::vector<float> cali_angle3d_;
            int last_region_index = 0;
    };
}

#endif // !_SVM_MODEL_H_

