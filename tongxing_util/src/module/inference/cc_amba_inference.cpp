#include "cc_amba_inference.h"
#include "CalmCarLog.h"
#include "cc_assert.h"
#include "cc_resource_register.h"
#include "opencv2/opencv.hpp"
#include <fstream>
#include <string.h>
namespace tongxing
{
    int CcAmbaInference::init(const Json::Value &config)
    {
        ea_net_params_t ea_net_params = {0};
        if (flag_init)
        {
            return -1;
        }
        root_ = config;
        int res = 0;
        BlobData model_data;
        if (root_["type"].asString() == "file")
        {
            std::string model_path = root_["filename"].asString();
            cc_assert(model_data.loadFromFile(model_path) == 0);
        }
        else if (root_["type"].asString() == "inside")
        {
            auto model_data_ =
                CcResourcDataRegister::instance().get_function(root_["filename"].asString());
            model_data.pu8VirAddr = (uint8_t *)model_data_.second;
            model_data.u32Size = model_data_.first;
        }
        else
        {
            cc_assert(false);
        }
        net = ea_net_new(NULL);
        if (net == NULL)
        {
            exit(-1);
            return -2;
        }
        if (config["flag_crop"].isBool())
        {
            flag_crop_ = config["flag_crop"].asBool();
        }

        // ea_net_config_input(net, "input");
        // ea_net_config_output(net, "output");
        res = ea_net_load(net, EA_NET_LOAD_DRAM, (void *)model_data.pu8VirAddr, 1);
        if (res != 0)
        {
            ea_net_free(net);
            net = NULL;
            exit(-1);
            return -3;
        }
        input_tensor = ea_net_input_by_index(net, 0);
        // ea_tensor_sync_cache(input_tensor, EA_VP, EA_CPU);
        const size_t *tensor_shape = ea_tensor_shape(input_tensor);
        tensor_c = tensor_shape[1];
        tensor_h = tensor_shape[2];
        tensor_w = tensor_shape[3];
        m_name_ = root_["m_name"].asString();
        TX_LOG_INFO("CcAmbaInference", "%s c:%d h:%d w:%d", m_name_.c_str(),
                 tensor_c, tensor_h, tensor_w);
        flag_init = true;
        return 0;
    }
    int CcAmbaInference::setInput(const std::vector<std::shared_ptr<NumArray> > &in)
    {
        input_ = in;
        return 0;
    }
    int num_image = 0;
    int CcAmbaInference::execute()
    {
        TX_LOG_DEBUG("CcAmbaInference", "--execute--- START %s", m_name_.c_str());

        if(input_.size()==1){
            uint8_t* in=input_[0]->data;
            char* data_tensor = (char *)ea_tensor_data(input_tensor);
            // if (m_name_ == "phone")
            // {
            //     cv::Mat img(tensor_h,tensor_w,CV_8UC1,in);
            //     cv::imwrite("./m_image/"+m_name_+ std::to_string(num_image)+".jpg",img);
            //     num_image++;
            // }
          
            memcpy(data_tensor,in,input_[0]->data_blob_ptr->u32Size);
            ea_tensor_sync_cache(input_tensor,EA_CPU,EA_VP);
        }
        ea_net_forward(net, 1);
        TX_LOG_DEBUG("CcAmbaInference", "--execute--- end ");
        return 0;
    }
    size_t CcAmbaInference::getOutputNum()
    {
        return ea_net_output_num(net);
    }
    std::shared_ptr<NumArray> CcAmbaInference::getOutput(int index)
    {
        ea_tensor_t *output_tensor = ea_net_output_by_index(net, index);
        ea_tensor_sync_cache(output_tensor, EA_VP, EA_CPU);
        std::shared_ptr<NumArray> ouput_numarray(new NumArray);
        ouput_numarray->data = (unsigned char *)ea_tensor_data(output_tensor);
        ouput_numarray->type = NumArray::FLOAT32;
        ouput_numarray->word_size = 4;
        const size_t *shape = ea_tensor_shape(output_tensor);
        ouput_numarray->shape.push_back(shape[0]);
        ouput_numarray->shape.push_back(shape[1]);
        ouput_numarray->shape.push_back(shape[2]);
        ouput_numarray->shape.push_back((shape[3] % 8) > 0 ? (shape[3] + (8 - (shape[3] % 8))) : shape[3]);
        //std::cout<<" out data : "<<shape[0]<<","<<shape[1]<<","<<shape[2]<<","<<((shape[3]%8)>0?(shape[3]+(8-(shape[3]%8))):shape[3])<<" "<< ea_tensor_size(output_tensor)<<std::endl;
        return ouput_numarray;
    }
    CcAmbaInference::~CcAmbaInference()
    {
    }
    REGISTER_CC_MODULE(amba_inference, CcAmbaInference)
}