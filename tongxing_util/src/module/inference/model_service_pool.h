#ifndef _MODEL_SERVICE_POOL_H_
#define _MODEL_SERVICE_POOL_H_

#include <vector>
#include <string>
#include <mutex>
#include <atomic>
#include <future>
#include <queue>
#include <memory>
#include <chrono>
#include <unordered_map>
#include "curl/curl.h"
#include "json.h"

namespace tongxing {

// 服务状态枚举
enum class ServiceStatus {
    AVAILABLE,    // 可用
    BUSY,        // 忙碌
    UNAVAILABLE  // 不可用
};

// 服务信息结构
struct ServiceInfo {
    std::string ip;
    int port;
    ServiceStatus status;
    std::chrono::steady_clock::time_point last_check;
    int consecutive_failures;
    double avg_response_time_ms;
    
    ServiceInfo(const std::string& ip, int port) 
        : ip(ip), port(port), status(ServiceStatus::AVAILABLE), 
          consecutive_failures(0), avg_response_time_ms(0.0) {
        last_check = std::chrono::steady_clock::now();
    }
};

// 请求结果结构
struct RequestResult {
    bool success;
    std::string response_data;
    std::string error_message;
    double response_time_ms;
    std::string service_url;
};

// 模型服务池类
class ModelServicePool {
public:
    ModelServicePool();
    ~ModelServicePool();
    
    // 初始化服务池
    bool initialize(const std::string& config_file = "ip_pool.json");
    
    // 发送异步请求
    std::future<RequestResult> sendAsyncRequest(
        const std::string& endpoint,
        const std::vector<unsigned char>& data,
        const std::string& content_type = "image/jpeg"
    );
    
    // 发送同步请求（向后兼容）
    RequestResult sendSyncRequest(
        const std::string& endpoint,
        const std::vector<unsigned char>& data,
        const std::string& content_type = "image/jpeg"
    );
    
    // 获取可用服务数量
    size_t getAvailableServiceCount() const;
    
    // 获取服务池状态
    std::vector<ServiceInfo> getServiceStatus() const;
    
    // 健康检查
    void performHealthCheck();

private:
    // 配置参数
    struct PoolConfig {
        int max_concurrent_requests;
        int request_timeout_ms;
        int retry_attempts;
        int health_check_interval_ms;
        std::string load_balance_strategy;
    };
    
    std::vector<ServiceInfo> services_;
    PoolConfig config_;
    mutable std::mutex services_mutex_;
    std::atomic<size_t> current_service_index_;
    
    // 私有方法
    bool loadConfig(const std::string& config_file);
    std::string selectNextService(const std::string& endpoint);
    RequestResult executeRequest(const std::string& url, 
                                const std::vector<unsigned char>& data,
                                const std::string& content_type);
    void updateServiceStatus(const std::string& service_ip, bool success, double response_time);
    bool isServiceHealthy(const ServiceInfo& service) const;
    
    // CURL回调函数
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, void* userp);
};

} // namespace tongxing

#endif // _MODEL_SERVICE_POOL_H_
