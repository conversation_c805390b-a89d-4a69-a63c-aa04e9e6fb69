#include "tx_oax4600_http_inference.h"
#include <google/protobuf/message.h>
#include <stdio.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>
#include <iostream>
#include "cc_resource_register.h"
#include "inference_result.pb.h"  // Protobuf 生成的头文件

namespace tongxing {

int TXOAX4600HttpInference::init(const Json::Value& root) {
    int ret = 0;
    root_ = root;

    if (root_["type"].asString() == "inside") {
        http_url_ = root_["http_url"].asString();  //通过http方式指定模型推理url
        model_url_ = root_["model_url"].asString();

    } else {
        cc_assert(false);
    }

    // 检查模式配置
    use_pool_mode_ = root_.get("use_service_pool", false).asBool();
    use_async_mode_ = root_.get("use_async_mode", true).asBool(); // 默认启用异步模式

    // 优先尝试异步模式
    if (use_async_mode_) {
        async_sender_ = std::make_shared<AsyncImageSender>();
        if (async_sender_->initialize(100, 2)) { // 队列大小100，2个发送线程
            std::cout << "[TXOAX4600HttpInference] Async sender initialized successfully" << std::endl;
            uploader.reset(new FileUploader(async_sender_, http_url_));
            return ret;
        } else {
            std::cout << "[TXOAX4600HttpInference] Async sender initialization failed, trying service pool mode" << std::endl;
            use_async_mode_ = false;
        }
    }

    // 尝试服务池模式
    if (use_pool_mode_) {
        service_pool_ = std::make_shared<ModelServicePool>();
        if (service_pool_->initialize("ip_pool.json")) {
            std::cout << "[TXOAX4600HttpInference] Service pool initialized successfully" << std::endl;
            uploader.reset(new FileUploader(service_pool_));
            return ret;
        } else {
            std::cout << "[TXOAX4600HttpInference] Service pool initialization failed, falling back to legacy mode" << std::endl;
            use_pool_mode_ = false;
        }
    }

    //检测执行程序下是否存在存在指定IP+port文件（传统模式）
    std::string config_file = "ip_port.json";
    if (access(config_file.c_str(), F_OK) == 0) {
        Json::Reader json_reader;
        Json::Value root;
        std::ifstream infile(config_file, std::ios::binary);
        json_reader.parse(infile, root);

        std::string ip = root["ip"].asString();
        std::string port = root["port"].asString();

        // 查找最后一个斜杠的位置
        size_t lastSlashPos = http_url_.find_last_of('/');
        size_t lastSlashPos2 = model_url_.find_last_of('/');
        auto temp_http_url = http_url_;
        auto temp_model_url = model_url_;

        http_url_ = "http://" + ip + ":" + port + temp_http_url.substr(lastSlashPos);

        model_url_ = "http://" + ip + ":" + port + temp_model_url.substr(lastSlashPos2);
    }
    uploader.reset(new FileUploader(http_url_, model_url_));

    return ret;
}

TXOAX4600HttpInference::~TXOAX4600HttpInference() {}
int TXOAX4600HttpInference::setInput(const std::vector<std::shared_ptr<NumArray>>& in) {
    input_ = in;
    return 0;
}
int TXOAX4600HttpInference::execute() {
    std::vector<std::shared_ptr<NumArray>> outputs;
    uploader->upload_image_data(input_, outputs);

    output_ = outputs;

    return 0;
}
size_t TXOAX4600HttpInference::getOutputNum() {
    return output_.size();
}
std::shared_ptr<NumArray> TXOAX4600HttpInference::getOutput(int index) {
    return output_[index];
}
REGISTER_CC_MODULE(oax4600_http_inference, TXOAX4600HttpInference)
}  // namespace tongxing