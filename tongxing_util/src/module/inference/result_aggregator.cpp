#include "result_aggregator.h"
#include <iostream>
#include <algorithm>

namespace tongxing {

ResultAggregator::ResultAggregator() 
    : timeout_ms_(5000), max_pending_results_(100),
      next_sequence_id_(1), next_output_sequence_(1),
      completed_count_(0), timeout_count_(0) {
}

ResultAggregator::~ResultAggregator() {
    cleanup();
}

bool ResultAggregator::initialize(const std::set<std::string>& required_models,
                                 int timeout_ms,
                                 int max_pending_results) {
    if (required_models.empty()) {
        std::cerr << "[ResultAggregator] Required models list cannot be empty" << std::endl;
        return false;
    }
    
    required_models_ = required_models;
    timeout_ms_ = timeout_ms;
    max_pending_results_ = max_pending_results;
    
    std::cout << "[ResultAggregator] Initialized with " << required_models_.size() 
              << " required models, timeout: " << timeout_ms_ << "ms" << std::endl;
    
    for (const auto& model : required_models_) {
        std::cout << "[ResultAggregator] Required model: " << model << std::endl;
    }
    
    return true;
}

uint64_t ResultAggregator::startNewImage() {
    uint64_t sequence_id = next_sequence_id_.fetch_add(1);
    
    std::lock_guard<std::mutex> lock(results_mutex_);
    
    // 检查是否超过最大待处理数量
    if (pending_results_.size() >= static_cast<size_t>(max_pending_results_)) {
        std::cerr << "[ResultAggregator] Too many pending results, dropping oldest" << std::endl;
        cleanupOldResults();
    }
    
    auto state = std::make_shared<ImageResultState>(sequence_id, required_models_);
    pending_results_[sequence_id] = state;
    
    std::cout << "[ResultAggregator] Started new image processing, sequence_id: " 
              << sequence_id << std::endl;
    
    return sequence_id;
}

bool ResultAggregator::addModelResult(uint64_t sequence_id,
                                     const std::string& model_name,
                                     const InferenceResult& result) {
    std::lock_guard<std::mutex> lock(results_mutex_);
    
    auto it = pending_results_.find(sequence_id);
    if (it == pending_results_.end()) {
        std::cerr << "[ResultAggregator] Unknown sequence_id: " << sequence_id << std::endl;
        return false;
    }
    
    auto& state = it->second;
    
    // 检查是否是必需的模型
    if (required_models_.find(model_name) == required_models_.end()) {
        std::cout << "[ResultAggregator] Ignoring result from non-required model: " 
                  << model_name << std::endl;
        return false;
    }
    
    // 检查是否已经收到过这个模型的结果
    if (state->received_models.find(model_name) != state->received_models_.end()) {
        std::cout << "[ResultAggregator] Duplicate result from model: " << model_name 
                  << " for sequence: " << sequence_id << std::endl;
        return false;
    }
    
    // 添加结果
    state->collected_results[model_name] = result;
    state->received_models.insert(model_name);
    
    std::cout << "[ResultAggregator] Added result from " << model_name 
              << " for sequence " << sequence_id 
              << " (" << state->received_models.size() << "/" << required_models_.size() << ")" << std::endl;
    
    // 检查是否完成
    if (state->checkComplete()) {
        processCompletedResult(sequence_id);
    }
    
    return true;
}

bool ResultAggregator::getNextResult(CombinedInferenceResult& result) {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    
    // 检查是否有按序列准备好的结果
    uint64_t expected_sequence = next_output_sequence_.load();
    
    auto it = completed_results_.find(expected_sequence);
    if (it != completed_results_.end()) {
        result = std::move(it->second);
        completed_results_.erase(it);
        next_output_sequence_.fetch_add(1);
        
        std::cout << "[ResultAggregator] Output result for sequence: " << expected_sequence << std::endl;
        return true;
    }
    
    return false;
}

void ResultAggregator::checkTimeouts() {
    std::lock_guard<std::mutex> lock(results_mutex_);
    
    auto now = std::chrono::steady_clock::now();
    std::vector<uint64_t> timeout_sequences;
    
    for (auto& pair : pending_results_) {
        auto& state = pair.second;
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - state->start_time).count();
        
        if (elapsed > timeout_ms_ && !state->is_complete && !state->is_timeout) {
            timeout_sequences.push_back(pair.first);
        }
    }
    
    for (uint64_t seq_id : timeout_sequences) {
        handleTimeout(seq_id);
    }
}

void ResultAggregator::processCompletedResult(uint64_t sequence_id) {
    auto it = pending_results_.find(sequence_id);
    if (it == pending_results_.end()) {
        return;
    }
    
    auto& state = it->second;
    
    // 创建组合结果
    CombinedInferenceResult combined_result(sequence_id);
    combined_result.model_results = state->collected_results;
    combined_result.is_complete = true;
    combined_result.has_timeout = false;
    combined_result.timestamp = state->start_time;
    
    // 移动到完成队列
    {
        std::lock_guard<std::mutex> queue_lock(queue_mutex_);
        completed_results_[sequence_id] = std::move(combined_result);
    }
    
    // 从待处理列表中移除
    pending_results_.erase(it);
    completed_count_.fetch_add(1);
    
    // 调用回调（如果有）
    if (result_callback_) {
        result_callback_(completed_results_[sequence_id]);
    }
    
    std::cout << "[ResultAggregator] Completed result for sequence: " << sequence_id << std::endl;
}

void ResultAggregator::handleTimeout(uint64_t sequence_id) {
    auto it = pending_results_.find(sequence_id);
    if (it == pending_results_.end()) {
        return;
    }
    
    auto& state = it->second;
    state->is_timeout = true;
    
    // 创建部分结果
    CombinedInferenceResult combined_result(sequence_id);
    combined_result.model_results = state->collected_results;
    combined_result.is_complete = false;
    combined_result.has_timeout = true;
    combined_result.missing_models = state->getMissingModels();
    combined_result.timestamp = state->start_time;
    
    std::cout << "[ResultAggregator] Timeout for sequence " << sequence_id 
              << ", missing models: ";
    for (const auto& model : combined_result.missing_models) {
        std::cout << model << " ";
    }
    std::cout << std::endl;
    
    // 移动到完成队列
    {
        std::lock_guard<std::mutex> queue_lock(queue_mutex_);
        completed_results_[sequence_id] = std::move(combined_result);
    }
    
    // 从待处理列表中移除
    pending_results_.erase(it);
    timeout_count_.fetch_add(1);
    
    // 调用回调（如果有）
    if (result_callback_) {
        result_callback_(completed_results_[sequence_id]);
    }
}

void ResultAggregator::cleanupOldResults() {
    // 清理最老的10%的待处理结果
    size_t cleanup_count = std::max(1, static_cast<int>(pending_results_.size() * 0.1));
    
    std::vector<std::pair<uint64_t, std::chrono::steady_clock::time_point>> time_sorted;
    for (const auto& pair : pending_results_) {
        time_sorted.emplace_back(pair.first, pair.second->start_time);
    }
    
    std::sort(time_sorted.begin(), time_sorted.end(),
              [](const auto& a, const auto& b) { return a.second < b.second; });
    
    for (size_t i = 0; i < cleanup_count && i < time_sorted.size(); ++i) {
        uint64_t seq_id = time_sorted[i].first;
        std::cout << "[ResultAggregator] Cleaning up old result: " << seq_id << std::endl;
        pending_results_.erase(seq_id);
    }
}

size_t ResultAggregator::getPendingCount() const {
    std::lock_guard<std::mutex> lock(results_mutex_);
    return pending_results_.size();
}

size_t ResultAggregator::getCompletedCount() const {
    return completed_count_.load();
}

size_t ResultAggregator::getTimeoutCount() const {
    return timeout_count_.load();
}

void ResultAggregator::setResultCallback(std::function<void(const CombinedInferenceResult&)> callback) {
    result_callback_ = callback;
}

void ResultAggregator::cleanup() {
    std::lock_guard<std::mutex> results_lock(results_mutex_);
    std::lock_guard<std::mutex> queue_lock(queue_mutex_);
    
    pending_results_.clear();
    completed_results_.clear();
    
    std::cout << "[ResultAggregator] Cleanup completed. Final stats - Completed: " 
              << completed_count_.load() << ", Timeout: " << timeout_count_.load() << std::endl;
}

} // namespace tongxing
