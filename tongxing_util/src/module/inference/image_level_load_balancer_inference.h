#ifndef _IMAGE_LEVEL_LOAD_BALANCER_INFERENCE_H_
#define _IMAGE_LEVEL_LOAD_BALANCER_INFERENCE_H_

#include "cc_module.h"
#include "image_level_load_balancer.h"
#include "json.h"
#include <memory>
#include <string>
#include <vector>

namespace tongxing {

/**
 * 图片级别负载均衡推理类
 * 集成到现有的推理框架中，提供负载均衡功能
 */
class ImageLevelLoadBalancerInference : public CcModule {
public:
    ImageLevelLoadBalancerInference();
    virtual ~ImageLevelLoadBalancerInference();
    
    // 继承自CcModule的接口
    virtual int init(const Json::Value& root) override;
    virtual int setInput(const std::vector<std::shared_ptr<NumArray>>& in) override;
    virtual int execute() override;
    virtual size_t getOutputNum() override;
    virtual std::shared_ptr<NumArray> getOutput(int index) override;
    
    // 获取负载均衡器状态
    Json::Value getLoadBalancerStatus() const;
    
    // 获取性能统计
    Json::Value getPerformanceStats() const;

private:
    // 输入输出数据
    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;

    // 核心组件
    std::shared_ptr<ImageLevelLoadBalancer> load_balancer_;

    // 配置参数
    std::vector<std::string> service_ips_;
    int port_;
    std::vector<std::string> model_endpoints_;
    bool enforce_sequential_output_;
    int max_concurrent_per_service_;
    int request_timeout_ms_;
    int health_check_interval_ms_;
    
    // 状态管理
    bool initialized_;
    std::string config_type_;
    
    // 统计信息
    std::atomic<size_t> total_requests_;
    std::atomic<size_t> successful_requests_;
    mutable std::mutex stats_mutex_;
    double total_processing_time_;
    std::chrono::steady_clock::time_point start_time_;
    
    // 私有方法
    bool loadConfiguration(const Json::Value& config);
    bool validateConfiguration() const;
    void updateStatistics(bool success, double processing_time);
    std::vector<std::shared_ptr<NumArray>> convertImageProcessingResult(
        const ImageProcessingResult& result);
    void logPerformanceMetrics() const;
};

} // namespace tongxing

#endif // _IMAGE_LEVEL_LOAD_BALANCER_INFERENCE_H_
