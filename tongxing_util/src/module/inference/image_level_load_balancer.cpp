#include "image_level_load_balancer.h"
#include <iostream>
#include <algorithm>
#include <thread>
#include "curl/curl.h"

namespace tongxing {

ImageLevelLoadBalancer::ImageLevelLoadBalancer() 
    : default_port_(1180), next_sequence_id_(1), next_output_sequence_(1),
      completed_count_(0), total_processing_time_(0.0) {
    curl_global_init(CURL_GLOBAL_DEFAULT);
}

ImageLevelLoadBalancer::~ImageLevelLoadBalancer() {
    curl_global_cleanup();
}

bool ImageLevelLoadBalancer::initialize(const std::vector<std::string>& service_ips,
                                       int port,
                                       const std::vector<std::string>& model_endpoints) {
    if (service_ips.empty()) {
        std::cerr << "[ImageLevelLoadBalancer] Service IPs list cannot be empty" << std::endl;
        return false;
    }
    
    default_port_ = port;
    model_endpoints_ = model_endpoints.empty() ? 
        std::vector<std::string>{"FaceDetection", "FaceKeypoints", "eye", "Dms_PhoneSmoking"} :
        model_endpoints;
    
    // 初始化服务实例
    service_instances_.clear();
    for (const auto& ip : service_ips) {
        service_instances_.emplace_back(ip, port);
    }
    
    std::cout << "[ImageLevelLoadBalancer] Initialized with " << service_instances_.size() 
              << " service instances" << std::endl;
    
    for (const auto& endpoint : model_endpoints_) {
        std::cout << "[ImageLevelLoadBalancer] Model endpoint: " << endpoint << std::endl;
    }
    
    // 执行初始健康检查
    performHealthCheck();
    
    return true;
}

uint64_t ImageLevelLoadBalancer::submitImageProcessing(const std::vector<std::shared_ptr<NumArray>>& input) {
    uint64_t sequence_id = next_sequence_id_.fetch_add(1);
    
    // 选择最佳服务实例
    std::string selected_service = selectBestService();
    if (selected_service.empty()) {
        std::cerr << "[ImageLevelLoadBalancer] No available service for sequence: " << sequence_id << std::endl;
        return 0; // 返回0表示失败
    }
    
    // 创建处理请求
    ImageProcessingRequest request(sequence_id, input);
    request.assigned_service_ip = selected_service;
    
    std::cout << "[ImageLevelLoadBalancer] Assigned sequence " << sequence_id 
              << " to service: " << selected_service << std::endl;
    
    // 更新服务负载
    updateServiceLoad(selected_service, 1);
    
    // 异步处理
    auto future = processImageOnService(request, selected_service);
    
    {
        std::lock_guard<std::mutex> lock(requests_mutex_);
        pending_requests_[sequence_id] = std::move(request);
        processing_futures_[sequence_id] = std::move(future);
    }
    
    return sequence_id;
}

bool ImageLevelLoadBalancer::getNextResult(ImageProcessingResult& result) {
    uint64_t expected_sequence = next_output_sequence_.load();
    
    // 检查是否有等待输出的结果
    {
        std::lock_guard<std::mutex> lock(results_mutex_);
        auto it = completed_results_.find(expected_sequence);
        if (it != completed_results_.end()) {
            result = std::move(it->second);
            completed_results_.erase(it);
            next_output_sequence_.fetch_add(1);
            
            std::cout << "[ImageLevelLoadBalancer] Output result for sequence: " << expected_sequence << std::endl;
            return true;
        }
    }
    
    // 检查是否有新完成的处理
    std::vector<uint64_t> completed_sequences;
    {
        std::lock_guard<std::mutex> lock(requests_mutex_);
        for (auto it = processing_futures_.begin(); it != processing_futures_.end();) {
            if (it->second.wait_for(std::chrono::milliseconds(0)) == std::future_status::ready) {
                uint64_t seq_id = it->first;
                auto processing_result = it->second.get();
                
                // 更新服务负载和统计
                updateServiceLoad(processing_result.service_ip, -1);
                updateServiceStats(processing_result.service_ip, 
                                 !processing_result.has_error,
                                 processing_result.total_processing_time_ms);
                
                // 移动到完成结果
                {
                    std::lock_guard<std::mutex> results_lock(results_mutex_);
                    completed_results_[seq_id] = std::move(processing_result);
                }
                
                completed_sequences.push_back(seq_id);
                pending_requests_.erase(seq_id);
                it = processing_futures_.erase(it);
                completed_count_.fetch_add(1);
            } else {
                ++it;
            }
        }
    }
    
    // 再次尝试获取期望的序列结果
    if (!completed_sequences.empty()) {
        std::lock_guard<std::mutex> lock(results_mutex_);
        auto it = completed_results_.find(expected_sequence);
        if (it != completed_results_.end()) {
            result = std::move(it->second);
            completed_results_.erase(it);
            next_output_sequence_.fetch_add(1);
            
            std::cout << "[ImageLevelLoadBalancer] Output result for sequence: " << expected_sequence << std::endl;
            return true;
        }
    }
    
    return false;
}

std::string ImageLevelLoadBalancer::selectBestService() {
    std::lock_guard<std::mutex> lock(instances_mutex_);
    
    ServiceInstance* best_service = nullptr;
    int min_load = INT_MAX;
    
    for (auto& instance : service_instances_) {
        if (instance.canAcceptRequest() && instance.current_load < min_load) {
            min_load = instance.current_load;
            best_service = &instance;
        }
    }
    
    return best_service ? best_service->ip : "";
}

std::future<ImageProcessingResult> ImageLevelLoadBalancer::processImageOnService(
    const ImageProcessingRequest& request,
    const std::string& service_ip) {
    
    return std::async(std::launch::async, [this, request, service_ip]() -> ImageProcessingResult {
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // 创建单服务多模型处理器
        std::string base_url = "http://" + service_ip + ":" + std::to_string(default_port_);
        SingleServiceMultiModelProcessor processor(base_url, model_endpoints_);
        
        // 在单个服务上处理所有模型
        auto result = processor.processAllModels(request);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        result.total_processing_time_ms = 
            std::chrono::duration<double, std::milli>(end_time - start_time).count();
        result.service_ip = service_ip;
        
        std::cout << "[ImageLevelLoadBalancer] Completed processing sequence " 
                  << request.sequence_id << " on service " << service_ip 
                  << " in " << result.total_processing_time_ms << "ms" << std::endl;
        
        return result;
    });
}

void ImageLevelLoadBalancer::updateServiceLoad(const std::string& service_ip, int delta) {
    std::lock_guard<std::mutex> lock(instances_mutex_);
    
    for (auto& instance : service_instances_) {
        if (instance.ip == service_ip) {
            instance.current_load += delta;
            instance.current_load = std::max(0, instance.current_load);
            break;
        }
    }
}

void ImageLevelLoadBalancer::updateServiceStats(const std::string& service_ip,
                                               bool success,
                                               double processing_time) {
    std::lock_guard<std::mutex> lock(instances_mutex_);
    
    for (auto& instance : service_instances_) {
        if (instance.ip == service_ip) {
            if (success) {
                instance.consecutive_failures = 0;
                // 更新平均处理时间
                instance.avg_processing_time_ms = 
                    (instance.avg_processing_time_ms + processing_time) / 2.0;
            } else {
                instance.consecutive_failures++;
                if (instance.consecutive_failures >= 3) {
                    instance.is_available = false;
                    std::cout << "[ImageLevelLoadBalancer] Marking service " << service_ip 
                              << " as unavailable due to consecutive failures" << std::endl;
                }
            }
            break;
        }
    }
    
    if (success) {
        total_processing_time_.fetch_add(processing_time);
    }
}

void ImageLevelLoadBalancer::performHealthCheck() {
    std::cout << "[ImageLevelLoadBalancer] Performing health check..." << std::endl;
    
    std::lock_guard<std::mutex> lock(instances_mutex_);
    for (auto& instance : service_instances_) {
        // 简单的健康检查：检查服务是否响应
        // 这里可以发送一个轻量级的请求来检查服务状态
        
        // 重置连续失败次数较高的服务
        if (instance.consecutive_failures >= 3) {
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(
                now - instance.last_health_check).count();
            
            if (elapsed >= 5) { // 5分钟后重试
                instance.consecutive_failures = 0;
                instance.is_available = true;
                instance.last_health_check = now;
                std::cout << "[ImageLevelLoadBalancer] Restored service: " << instance.ip << std::endl;
            }
        }
        
        std::cout << "[ImageLevelLoadBalancer] Service " << instance.ip 
                  << " - Available: " << instance.is_available 
                  << ", Load: " << instance.current_load 
                  << ", Avg Time: " << instance.avg_processing_time_ms << "ms" << std::endl;
    }
}

size_t ImageLevelLoadBalancer::getPendingRequestCount() const {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    return pending_requests_.size();
}

size_t ImageLevelLoadBalancer::getCompletedRequestCount() const {
    return completed_count_.load();
}

double ImageLevelLoadBalancer::getAverageProcessingTime() const {
    size_t count = completed_count_.load();
    return count > 0 ? total_processing_time_.load() / count : 0.0;
}

Json::Value ImageLevelLoadBalancer::getLoadBalancerStatus() const {
    Json::Value status;
    
    status["pending_requests"] = static_cast<int>(getPendingRequestCount());
    status["completed_requests"] = static_cast<int>(getCompletedRequestCount());
    status["average_processing_time_ms"] = getAverageProcessingTime();
    
    Json::Value services(Json::arrayValue);
    {
        std::lock_guard<std::mutex> lock(instances_mutex_);
        for (const auto& instance : service_instances_) {
            Json::Value service;
            service["ip"] = instance.ip;
            service["port"] = instance.port;
            service["available"] = instance.is_available;
            service["current_load"] = instance.current_load;
            service["max_concurrent"] = instance.max_concurrent_requests;
            service["avg_processing_time_ms"] = instance.avg_processing_time_ms;
            service["consecutive_failures"] = instance.consecutive_failures;
            services.append(service);
        }
    }
    status["services"] = services;
    
    return status;
}

// SingleServiceMultiModelProcessor 实现
SingleServiceMultiModelProcessor::SingleServiceMultiModelProcessor(
    const std::string& base_url,
    const std::vector<std::string>& endpoints)
    : base_url_(base_url), model_endpoints_(endpoints) {
}

ImageProcessingResult SingleServiceMultiModelProcessor::processAllModels(
    const ImageProcessingRequest& request) {

    ImageProcessingResult result(request.sequence_id);
    result.timestamp = request.timestamp;

    std::cout << "[SingleServiceMultiModelProcessor] Processing sequence "
              << request.sequence_id << " on " << base_url_ << std::endl;

    try {
        // 在同一个服务上依次调用所有模型
        for (const auto& endpoint : model_endpoints_) {
            std::cout << "[SingleServiceMultiModelProcessor] Calling model: " << endpoint << std::endl;

            auto model_result = callModelEndpoint(endpoint, request.input_data);
            result.model_results[endpoint] = model_result;

            std::cout << "[SingleServiceMultiModelProcessor] Completed model: " << endpoint << std::endl;
        }

        result.is_complete = true;
        result.has_error = false;

    } catch (const std::exception& e) {
        result.has_error = true;
        result.error_message = "Processing failed: " + std::string(e.what());
        std::cerr << "[SingleServiceMultiModelProcessor] Error: " << result.error_message << std::endl;
    }

    return result;
}

InferenceResult SingleServiceMultiModelProcessor::callModelEndpoint(
    const std::string& endpoint,
    const std::vector<std::shared_ptr<NumArray>>& input) {

    InferenceResult inference_result;

    CURL* curl = curl_easy_init();
    if (!curl) {
        throw std::runtime_error("Failed to initialize CURL for endpoint: " + endpoint);
    }

    try {
        // 转换输入数据
        auto image_data = convertInputToImageData(input);

        // 构建完整URL
        std::string full_url = base_url_ + "/" + endpoint;

        struct curl_slist* headers = nullptr;
        headers = curl_slist_append(headers, "Content-Type: image/jpeg");

        std::string response_data;

        curl_easy_setopt(curl, CURLOPT_URL, full_url.c_str());
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS,
                        reinterpret_cast<const char*>(image_data.data()));
        curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, image_data.size());
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT_MS, 5000);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response_data);

        CURLcode res = curl_easy_perform(curl);

        if (res == CURLE_OK) {
            long http_code = 0;
            curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);

            if (http_code == 200) {
                if (!inference_result.ParseFromString(response_data)) {
                    throw std::runtime_error("Failed to parse protobuf response from: " + endpoint);
                }
            } else {
                throw std::runtime_error("HTTP error " + std::to_string(http_code) + " from: " + endpoint);
            }
        } else {
            throw std::runtime_error("CURL error: " + std::string(curl_easy_strerror(res)) + " for: " + endpoint);
        }

        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);

    } catch (...) {
        curl_easy_cleanup(curl);
        throw;
    }

    return inference_result;
}

std::vector<unsigned char> SingleServiceMultiModelProcessor::convertInputToImageData(
    const std::vector<std::shared_ptr<NumArray>>& input) {

    if (input.empty() || !input[0]) {
        throw std::runtime_error("Invalid input data");
    }

    auto array = input[0];
    auto batch_size = array->shape[0];
    auto channel = array->shape[1];
    auto height = array->shape[2];
    auto width = array->shape[3];

    int data_length = batch_size * channel * height * width;

    std::vector<unsigned char> image_data;
    image_data.reserve(data_length);
    image_data.assign(array->data, array->data + data_length);

    return image_data;
}

size_t SingleServiceMultiModelProcessor::WriteCallback(void* contents, size_t size, size_t nmemb, void* userp) {
    ((std::string*)userp)->append((char*)contents, size * nmemb);
    return size * nmemb;
}

} // namespace tongxing
