#ifndef _ASYNC_IMAGE_SENDER_H_
#define _ASYNC_IMAGE_SENDER_H_

#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <memory>
#include <functional>
#include <chrono>
#include "curl/curl.h"
#include "cc_numarray_tool.h"

namespace tongxing {

// 图片请求结构
struct ImageRequest {
    std::vector<unsigned char> image_data;
    std::string url;
    std::string content_type;
    std::chrono::steady_clock::time_point timestamp;
    
    // 可选的回调函数（用于需要结果的场景）
    std::function<void(bool success, const std::string& response)> callback;
    
    ImageRequest(const std::vector<unsigned char>& data, 
                const std::string& url,
                const std::string& content_type = "image/jpeg")
        : image_data(data), url(url), content_type(content_type) {
        timestamp = std::chrono::steady_clock::now();
    }
};

// 异步图片发送器
class AsyncImageSender {
public:
    AsyncImageSender();
    ~AsyncImageSender();
    
    // 初始化发送器
    bool initialize(int max_queue_size = 100, int sender_threads = 1);
    
    // 停止发送器
    void shutdown();
    
    // 非阻塞发送图片（立即返回）
    bool sendImageAsync(const std::vector<std::shared_ptr<NumArray>>& inputs,
                       const std::string& base_url);
    
    // 带回调的异步发送
    bool sendImageAsyncWithCallback(
        const std::vector<std::shared_ptr<NumArray>>& inputs,
        const std::string& base_url,
        std::function<void(bool, const std::string&)> callback);
    
    // 获取队列状态
    size_t getQueueSize() const;
    size_t getProcessedCount() const;
    size_t getFailedCount() const;
    
    // 获取性能统计
    double getAverageSendTime() const;
    
private:
    // 配置参数
    int max_queue_size_;
    int sender_threads_;
    
    // 队列管理
    std::queue<ImageRequest> request_queue_;
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::condition_variable queue_not_full_cv_;
    
    // 线程管理
    std::vector<std::thread> sender_threads_vec_;
    std::atomic<bool> running_;
    std::atomic<bool> shutdown_requested_;
    
    // 统计信息
    std::atomic<size_t> processed_count_;
    std::atomic<size_t> failed_count_;
    std::atomic<double> total_send_time_;
    
    // 私有方法
    void senderThreadFunc();
    bool executeRequest(const ImageRequest& request);
    std::vector<unsigned char> convertInputsToImageData(
        const std::vector<std::shared_ptr<NumArray>>& inputs);
    
    // CURL回调
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, void* userp);
};

} // namespace tongxing

#endif // _ASYNC_IMAGE_SENDER_H_
