#include "model_service_pool.h"
#include <fstream>
#include <iostream>
#include <algorithm>
#include <thread>

namespace tongxing {

ModelServicePool::ModelServicePool() : current_service_index_(0) {
    // 初始化CURL
    curl_global_init(CURL_GLOBAL_DEFAULT);
}

ModelServicePool::~ModelServicePool() {
    // 清理CURL
    curl_global_cleanup();
}

bool ModelServicePool::initialize(const std::string& config_file) {
    try {
        if (!loadConfig(config_file)) {
            std::cerr << "[ModelServicePool] Failed to load config from: " << config_file << std::endl;
            return false;
        }
        
        std::cout << "[ModelServicePool] Initialized with " << services_.size() 
                  << " services" << std::endl;
        
        // 执行初始健康检查
        performHealthCheck();
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "[ModelServicePool] Initialization failed: " << e.what() << std::endl;
        return false;
    }
}

bool ModelServicePool::loadConfig(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "[ModelServicePool] Cannot open config file: " << config_file << std::endl;
        return false;
    }
    
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errs;
    
    if (!Json::parseFromStream(builder, file, &root, &errs)) {
        std::cerr << "[ModelServicePool] JSON parse error: " << errs << std::endl;
        return false;
    }
    
    // 加载IP列表
    const Json::Value& ips = root["ips"];
    if (!ips.isArray() || ips.empty()) {
        std::cerr << "[ModelServicePool] Invalid or empty IPs array" << std::endl;
        return false;
    }
    
    int port = root.get("port", 1180).asInt();
    
    services_.clear();
    for (const auto& ip : ips) {
        if (ip.isString()) {
            services_.emplace_back(ip.asString(), port);
        }
    }
    
    // 加载池配置
    const Json::Value& pool_config = root["pool_config"];
    config_.max_concurrent_requests = pool_config.get("max_concurrent_requests", 7).asInt();
    config_.request_timeout_ms = pool_config.get("request_timeout_ms", 5000).asInt();
    config_.retry_attempts = pool_config.get("retry_attempts", 2).asInt();
    config_.health_check_interval_ms = pool_config.get("health_check_interval_ms", 30000).asInt();
    config_.load_balance_strategy = pool_config.get("load_balance_strategy", "round_robin").asString();
    
    return !services_.empty();
}

std::string ModelServicePool::selectNextService(const std::string& endpoint) {
    std::lock_guard<std::mutex> lock(services_mutex_);
    
    // 简单轮询策略
    size_t attempts = 0;
    while (attempts < services_.size()) {
        size_t index = current_service_index_.fetch_add(1) % services_.size();
        const auto& service = services_[index];
        
        if (isServiceHealthy(service)) {
            return "http://" + service.ip + ":" + std::to_string(service.port) + "/" + endpoint;
        }
        attempts++;
    }
    
    // 如果没有健康的服务，返回第一个服务（降级处理）
    if (!services_.empty()) {
        const auto& service = services_[0];
        std::cout << "[ModelServicePool] No healthy services, using fallback: " 
                  << service.ip << std::endl;
        return "http://" + service.ip + ":" + std::to_string(service.port) + "/" + endpoint;
    }
    
    return "";
}

RequestResult ModelServicePool::executeRequest(const std::string& url,
                                              const std::vector<unsigned char>& data,
                                              const std::string& content_type) {
    RequestResult result;
    result.success = false;
    result.service_url = url;
    
    CURL* curl = curl_easy_init();
    if (!curl) {
        result.error_message = "Failed to initialize CURL";
        return result;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        struct curl_slist* headers = nullptr;
        std::string content_type_header = "Content-Type: " + content_type;
        headers = curl_slist_append(headers, content_type_header.c_str());
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, reinterpret_cast<const char*>(data.data()));
        curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, data.size());
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT_MS, config_.request_timeout_ms);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &result.response_data);
        
        CURLcode res = curl_easy_perform(curl);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        result.response_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
        
        if (res == CURLE_OK) {
            long http_code = 0;
            curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
            
            if (http_code == 200) {
                result.success = true;
            } else {
                result.error_message = "HTTP error code: " + std::to_string(http_code);
            }
        } else {
            result.error_message = "CURL error: " + std::string(curl_easy_strerror(res));
        }
        
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        
    } catch (const std::exception& e) {
        result.error_message = "Exception: " + std::string(e.what());
        curl_easy_cleanup(curl);
    }
    
    return result;
}

std::future<RequestResult> ModelServicePool::sendAsyncRequest(
    const std::string& endpoint,
    const std::vector<unsigned char>& data,
    const std::string& content_type) {
    
    return std::async(std::launch::async, [this, endpoint, data, content_type]() -> RequestResult {
        RequestResult result;
        
        for (int attempt = 0; attempt <= config_.retry_attempts; ++attempt) {
            std::string url = selectNextService(endpoint);
            if (url.empty()) {
                result.error_message = "No available services";
                break;
            }
            
            result = executeRequest(url, data, content_type);
            
            // 更新服务状态
            size_t pos = url.find("://") + 3;
            size_t end_pos = url.find(":", pos);
            if (end_pos != std::string::npos) {
                std::string service_ip = url.substr(pos, end_pos - pos);
                updateServiceStatus(service_ip, result.success, result.response_time_ms);
            }
            
            if (result.success) {
                break;
            }
            
            if (attempt < config_.retry_attempts) {
                std::cout << "[ModelServicePool] Request failed, retrying... (attempt " 
                          << (attempt + 2) << "/" << (config_.retry_attempts + 1) << ")" << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(100 * (attempt + 1)));
            }
        }
        
        return result;
    });
}

RequestResult ModelServicePool::sendSyncRequest(
    const std::string& endpoint,
    const std::vector<unsigned char>& data,
    const std::string& content_type) {

    auto future = sendAsyncRequest(endpoint, data, content_type);
    return future.get();
}

size_t ModelServicePool::getAvailableServiceCount() const {
    std::lock_guard<std::mutex> lock(services_mutex_);
    return std::count_if(services_.begin(), services_.end(),
                        [this](const ServiceInfo& service) {
                            return isServiceHealthy(service);
                        });
}

std::vector<ServiceInfo> ModelServicePool::getServiceStatus() const {
    std::lock_guard<std::mutex> lock(services_mutex_);
    return services_;
}

void ModelServicePool::updateServiceStatus(const std::string& service_ip, bool success, double response_time) {
    std::lock_guard<std::mutex> lock(services_mutex_);

    auto it = std::find_if(services_.begin(), services_.end(),
                          [&service_ip](const ServiceInfo& service) {
                              return service.ip == service_ip;
                          });

    if (it != services_.end()) {
        it->last_check = std::chrono::steady_clock::now();

        if (success) {
            it->status = ServiceStatus::AVAILABLE;
            it->consecutive_failures = 0;
            // 更新平均响应时间（简单移动平均）
            it->avg_response_time_ms = (it->avg_response_time_ms + response_time) / 2.0;
        } else {
            it->consecutive_failures++;
            if (it->consecutive_failures >= 3) {
                it->status = ServiceStatus::UNAVAILABLE;
            }
        }
    }
}

bool ModelServicePool::isServiceHealthy(const ServiceInfo& service) const {
    return service.status == ServiceStatus::AVAILABLE &&
           service.consecutive_failures < 3;
}

void ModelServicePool::performHealthCheck() {
    std::cout << "[ModelServicePool] Performing health check..." << std::endl;

    // 简单的健康检查：发送小的测试请求
    std::vector<unsigned char> test_data(100, 0); // 100字节的测试数据

    for (auto& service : services_) {
        std::string url = "http://" + service.ip + ":" + std::to_string(service.port) + "/health";
        auto result = executeRequest(url, test_data, "application/octet-stream");

        updateServiceStatus(service.ip, result.success, result.response_time_ms);

        std::cout << "[ModelServicePool] Service " << service.ip
                  << " status: " << (result.success ? "HEALTHY" : "UNHEALTHY") << std::endl;
    }
}

size_t ModelServicePool::WriteCallback(void* contents, size_t size, size_t nmemb, void* userp) {
    ((std::string*)userp)->append((char*)contents, size * nmemb);
    return size * nmemb;
}

} // namespace tongxing
