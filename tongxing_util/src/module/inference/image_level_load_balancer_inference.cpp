#include "image_level_load_balancer_inference.h"
#include <iostream>
#include <chrono>

namespace tongxing {

ImageLevelLoadBalancerInference::ImageLevelLoadBalancerInference()
    : port_(1180), enforce_sequential_output_(true), 
      max_concurrent_per_service_(2), request_timeout_ms_(8000),
      health_check_interval_ms_(30000), initialized_(false),
      total_requests_(0), successful_requests_(0), total_processing_time_(0.0) {
    
    start_time_ = std::chrono::steady_clock::now();
}

ImageLevelLoadBalancerInference::~ImageLevelLoadBalancerInference() {
    deinit();
}

int ImageLevelLoadBalancerInference::init(const Json::Value& root) {
    try {
        std::cout << "[ImageLevelLoadBalancerInference] Initializing..." << std::endl;
        
        if (!loadConfiguration(root)) {
            std::cerr << "[ImageLevelLoadBalancerInference] Failed to load configuration" << std::endl;
            return -1;
        }
        
        if (!validateConfiguration()) {
            std::cerr << "[ImageLevelLoadBalancerInference] Configuration validation failed" << std::endl;
            return -1;
        }
        
        // 创建负载均衡器
        load_balancer_ = std::make_shared<ImageLevelLoadBalancer>();
        
        if (!load_balancer_->initialize(service_ips_, port_, model_endpoints_)) {
            std::cerr << "[ImageLevelLoadBalancerInference] Failed to initialize load balancer" << std::endl;
            return -1;
        }
        
        initialized_ = true;
        
        std::cout << "[ImageLevelLoadBalancerInference] Successfully initialized with:" << std::endl;
        std::cout << "  Service IPs: ";
        for (const auto& ip : service_ips_) {
            std::cout << ip << " ";
        }
        std::cout << std::endl;
        std::cout << "  Port: " << port_ << std::endl;
        std::cout << "  Model endpoints: ";
        for (const auto& endpoint : model_endpoints_) {
            std::cout << endpoint << " ";
        }
        std::cout << std::endl;
        std::cout << "  Sequential output: " << (enforce_sequential_output_ ? "enabled" : "disabled") << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "[ImageLevelLoadBalancerInference] Initialization failed: " << e.what() << std::endl;
        return -1;
    }
}

int ImageLevelLoadBalancerInference::inference(const std::vector<std::shared_ptr<NumArray>>& in,
                                              std::vector<std::shared_ptr<NumArray>>& outputs) {
    if (!initialized_ || !load_balancer_) {
        std::cerr << "[ImageLevelLoadBalancerInference] Not initialized" << std::endl;
        return -1;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    total_requests_.fetch_add(1);
    
    try {
        // 提交图片处理请求
        uint64_t sequence_id = load_balancer_->submitImageProcessing(in);
        if (sequence_id == 0) {
            std::cerr << "[ImageLevelLoadBalancerInference] Failed to submit image processing" << std::endl;
            return -1;
        }
        
        std::cout << "[ImageLevelLoadBalancerInference] Submitted image processing, sequence_id: " 
                  << sequence_id << std::endl;
        
        // 等待结果
        ImageProcessingResult result(0);
        bool got_result = false;
        
        // 等待结果，最多等待配置的超时时间
        int max_retries = request_timeout_ms_ / 100; // 每100ms检查一次
        for (int retry = 0; retry < max_retries; ++retry) {
            if (load_balancer_->getNextResult(result)) {
                got_result = true;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        if (!got_result) {
            std::cerr << "[ImageLevelLoadBalancerInference] Timeout waiting for result" << std::endl;
            return -1;
        }
        
        if (result.has_error) {
            std::cerr << "[ImageLevelLoadBalancerInference] Processing error: " 
                      << result.error_message << std::endl;
            return -1;
        }
        
        // 转换结果
        outputs = convertImageProcessingResult(result);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto processing_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
        
        updateStatistics(true, processing_time);
        
        std::cout << "[ImageLevelLoadBalancerInference] Successfully processed sequence " 
                  << result.sequence_id << " on service " << result.service_ip
                  << " in " << processing_time << "ms" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto processing_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
        
        updateStatistics(false, processing_time);
        
        std::cerr << "[ImageLevelLoadBalancerInference] Inference failed: " << e.what() << std::endl;
        return -1;
    }
}

int ImageLevelLoadBalancerInference::deinit() {
    if (initialized_) {
        logPerformanceMetrics();
        
        load_balancer_.reset();
        initialized_ = false;
        
        std::cout << "[ImageLevelLoadBalancerInference] Deinitialized" << std::endl;
    }
    return 0;
}

bool ImageLevelLoadBalancerInference::loadConfiguration(const Json::Value& config) {
    try {
        // 加载服务IP列表
        const Json::Value& ips = config["service_ips"];
        if (!ips.isArray() || ips.empty()) {
            std::cerr << "[ImageLevelLoadBalancerInference] Invalid or empty service_ips" << std::endl;
            return false;
        }
        
        service_ips_.clear();
        for (const auto& ip : ips) {
            if (ip.isString()) {
                service_ips_.push_back(ip.asString());
            }
        }
        
        // 加载其他配置
        port_ = config.get("port", 1180).asInt();
        enforce_sequential_output_ = config.get("enforce_sequential_output", true).asBool();
        max_concurrent_per_service_ = config.get("max_concurrent_per_service", 2).asInt();
        request_timeout_ms_ = config.get("request_timeout_ms", 8000).asInt();
        health_check_interval_ms_ = config.get("health_check_interval_ms", 30000).asInt();
        config_type_ = config.get("type", "load_balanced").asString();
        
        // 加载模型端点
        const Json::Value& endpoints = config["model_endpoints"];
        if (endpoints.isArray()) {
            model_endpoints_.clear();
            for (const auto& endpoint : endpoints) {
                if (endpoint.isString()) {
                    model_endpoints_.push_back(endpoint.asString());
                }
            }
        } else {
            // 使用默认模型端点
            model_endpoints_ = {"FaceDetection", "FaceKeypoints", "eye", "Dms_PhoneSmoking"};
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ImageLevelLoadBalancerInference] Configuration loading error: " << e.what() << std::endl;
        return false;
    }
}

bool ImageLevelLoadBalancerInference::validateConfiguration() const {
    if (service_ips_.empty()) {
        std::cerr << "[ImageLevelLoadBalancerInference] No service IPs configured" << std::endl;
        return false;
    }
    
    if (model_endpoints_.empty()) {
        std::cerr << "[ImageLevelLoadBalancerInference] No model endpoints configured" << std::endl;
        return false;
    }
    
    if (port_ <= 0 || port_ > 65535) {
        std::cerr << "[ImageLevelLoadBalancerInference] Invalid port: " << port_ << std::endl;
        return false;
    }
    
    if (request_timeout_ms_ <= 0) {
        std::cerr << "[ImageLevelLoadBalancerInference] Invalid timeout: " << request_timeout_ms_ << std::endl;
        return false;
    }
    
    return true;
}

void ImageLevelLoadBalancerInference::updateStatistics(bool success, double processing_time) {
    if (success) {
        successful_requests_.fetch_add(1);
    }
    total_processing_time_.fetch_add(processing_time);
}

std::vector<std::shared_ptr<NumArray>> ImageLevelLoadBalancerInference::convertImageProcessingResult(
    const ImageProcessingResult& result) {
    
    std::vector<std::shared_ptr<NumArray>> outputs;
    
    try {
        // 将所有模型的结果合并到输出中
        for (const auto& model_pair : result.model_results) {
            const std::string& model_name = model_pair.first;
            const InferenceResult& inference_result = model_pair.second;
            
            std::cout << "[ImageLevelLoadBalancerInference] Converting result from model: " 
                      << model_name << " (" << inference_result.tensors_size() << " tensors)" << std::endl;
            
            // 转换每个tensor
            for (int i = 0; i < inference_result.tensors_size(); ++i) {
                const auto& tensor = inference_result.tensors(i);
                
                // 构建shape
                std::vector<int> shape;
                for (int j = 0; j < tensor.shape_size(); ++j) {
                    shape.push_back(tensor.shape(j));
                }
                
                // 创建NumArray
                auto output = creat_numarray(shape, NumArray::DataType::FLOAT32);
                
                // 复制数据
                size_t data_size = tensor.data().size();
                if (data_size > 0) {
                    memcpy(output->data, tensor.data().data(), data_size);
                }
                
                outputs.push_back(output);
            }
        }
        
        std::cout << "[ImageLevelLoadBalancerInference] Converted " << outputs.size() 
                  << " output tensors" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ImageLevelLoadBalancerInference] Result conversion error: " << e.what() << std::endl;
    }
    
    return outputs;
}

void ImageLevelLoadBalancerInference::logPerformanceMetrics() const {
    auto now = std::chrono::steady_clock::now();
    auto uptime = std::chrono::duration<double>(now - start_time_).count();
    
    size_t total = total_requests_.load();
    size_t successful = successful_requests_.load();
    double total_time = total_processing_time_.load();
    
    std::cout << "\n[ImageLevelLoadBalancerInference] Performance Summary:" << std::endl;
    std::cout << "  Uptime: " << uptime << " seconds" << std::endl;
    std::cout << "  Total requests: " << total << std::endl;
    std::cout << "  Successful requests: " << successful << std::endl;
    std::cout << "  Success rate: " << (total > 0 ? (successful * 100.0 / total) : 0.0) << "%" << std::endl;
    std::cout << "  Average processing time: " << (successful > 0 ? (total_time / successful) : 0.0) << "ms" << std::endl;
    std::cout << "  Throughput: " << (uptime > 0 ? (successful / uptime) : 0.0) << " requests/second" << std::endl;
}

Json::Value ImageLevelLoadBalancerInference::getLoadBalancerStatus() const {
    if (load_balancer_) {
        return load_balancer_->getLoadBalancerStatus();
    }
    return Json::Value();
}

Json::Value ImageLevelLoadBalancerInference::getPerformanceStats() const {
    Json::Value stats;
    
    auto now = std::chrono::steady_clock::now();
    auto uptime = std::chrono::duration<double>(now - start_time_).count();
    
    size_t total = total_requests_.load();
    size_t successful = successful_requests_.load();
    double total_time = total_processing_time_.load();
    
    stats["uptime_seconds"] = uptime;
    stats["total_requests"] = static_cast<int>(total);
    stats["successful_requests"] = static_cast<int>(successful);
    stats["success_rate"] = total > 0 ? (successful * 100.0 / total) : 0.0;
    stats["average_processing_time_ms"] = successful > 0 ? (total_time / successful) : 0.0;
    stats["throughput_rps"] = uptime > 0 ? (successful / uptime) : 0.0;
    
    return stats;
}

} // namespace tongxing
