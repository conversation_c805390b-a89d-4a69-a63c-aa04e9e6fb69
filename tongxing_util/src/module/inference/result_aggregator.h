#ifndef _RESULT_AGGREGATOR_H_
#define _RESULT_AGGREGATOR_H_

#include <map>
#include <queue>
#include <set>
#include <string>
#include <mutex>
#include <chrono>
#include <memory>
#include <functional>
#include "inference_result.pb.h"
#include "json.h"

namespace tongxing {

// 组合推理结果结构
struct CombinedInferenceResult {
    uint64_t sequence_id;
    std::chrono::steady_clock::time_point timestamp;
    std::map<std::string, InferenceResult> model_results; // 模型名 -> 结果
    bool is_complete;
    bool has_timeout;
    std::set<std::string> missing_models;
    
    CombinedInferenceResult(uint64_t seq_id) 
        : sequence_id(seq_id), is_complete(false), has_timeout(false) {
        timestamp = std::chrono::steady_clock::now();
    }
};

// 单个图片的结果收集状态
struct ImageResultState {
    uint64_t sequence_id;
    std::chrono::steady_clock::time_point start_time;
    std::map<std::string, InferenceResult> collected_results;
    std::set<std::string> required_models;
    std::set<std::string> received_models;
    bool is_complete;
    bool is_timeout;
    
    ImageResultState(uint64_t seq_id, const std::set<std::string>& required)
        : sequence_id(seq_id), required_models(required), 
          is_complete(false), is_timeout(false) {
        start_time = std::chrono::steady_clock::now();
    }
    
    bool checkComplete() {
        is_complete = (received_models.size() >= required_models.size());
        return is_complete;
    }
    
    std::set<std::string> getMissingModels() const {
        std::set<std::string> missing;
        for (const auto& model : required_models) {
            if (received_models.find(model) == received_models.end()) {
                missing.insert(model);
            }
        }
        return missing;
    }
};

// 结果聚合器类
class ResultAggregator {
public:
    ResultAggregator();
    ~ResultAggregator();
    
    // 初始化聚合器
    bool initialize(const std::set<std::string>& required_models,
                   int timeout_ms = 5000,
                   int max_pending_results = 100);
    
    // 开始处理新图片（分配sequence_id）
    uint64_t startNewImage();
    
    // 添加模型结果
    bool addModelResult(uint64_t sequence_id, 
                       const std::string& model_name, 
                       const InferenceResult& result);
    
    // 尝试获取下一个完整的结果（按时序）
    bool getNextResult(CombinedInferenceResult& result);
    
    // 检查并处理超时
    void checkTimeouts();
    
    // 获取统计信息
    size_t getPendingCount() const;
    size_t getCompletedCount() const;
    size_t getTimeoutCount() const;
    
    // 设置结果回调（可选）
    void setResultCallback(std::function<void(const CombinedInferenceResult&)> callback);
    
    // 清理资源
    void cleanup();

private:
    // 配置参数
    std::set<std::string> required_models_;
    int timeout_ms_;
    int max_pending_results_;
    
    // 状态管理
    std::map<uint64_t, std::shared_ptr<ImageResultState>> pending_results_;
    std::queue<uint64_t> ready_queue_;
    std::map<uint64_t, CombinedInferenceResult> completed_results_;
    
    // 序列号管理
    std::atomic<uint64_t> next_sequence_id_;
    std::atomic<uint64_t> next_output_sequence_;
    
    // 线程安全
    mutable std::mutex results_mutex_;
    mutable std::mutex queue_mutex_;
    
    // 统计信息
    std::atomic<size_t> completed_count_;
    std::atomic<size_t> timeout_count_;
    
    // 回调函数
    std::function<void(const CombinedInferenceResult&)> result_callback_;
    
    // 私有方法
    void processCompletedResult(uint64_t sequence_id);
    void handleTimeout(uint64_t sequence_id);
    bool isSequenceReady(uint64_t sequence_id) const;
    void cleanupOldResults();
};

// 多模型推理管理器
class MultiModelInferenceManager {
public:
    MultiModelInferenceManager();
    ~MultiModelInferenceManager();
    
    // 初始化管理器
    bool initialize(const Json::Value& config);
    
    // 处理单张图片（发送到所有模型服务）
    uint64_t processImage(const std::vector<std::shared_ptr<NumArray>>& input);
    
    // 获取完整结果
    bool getResult(CombinedInferenceResult& result);
    
    // 获取状态信息
    Json::Value getStatus() const;

private:
    std::shared_ptr<ResultAggregator> aggregator_;
    std::map<std::string, std::shared_ptr<TXOAX4600HttpInference>> model_services_;
    std::set<std::string> required_models_;
    
    // 私有方法
    void sendToAllModels(uint64_t sequence_id, 
                        const std::vector<std::shared_ptr<NumArray>>& input);
    void onModelResult(uint64_t sequence_id, 
                      const std::string& model_name,
                      const std::vector<std::shared_ptr<NumArray>>& outputs);
};

} // namespace tongxing

#endif // _RESULT_AGGREGATOR_H_
