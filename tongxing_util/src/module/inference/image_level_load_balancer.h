#ifndef _IMAGE_LEVEL_LOAD_BALANCER_H_
#define _IMAGE_LEVEL_LOAD_BALANCER_H_

#include <vector>
#include <string>
#include <queue>
#include <map>
#include <mutex>
#include <atomic>
#include <chrono>
#include <memory>
#include <future>
#include "json.h"
#include "cc_numarray_tool.h"
#include "inference_result.pb.h"

namespace tongxing {

// 图片处理请求
struct ImageProcessingRequest {
    uint64_t sequence_id;
    std::vector<std::shared_ptr<NumArray>> input_data;
    std::chrono::steady_clock::time_point timestamp;
    std::string assigned_service_ip;
    
    ImageProcessingRequest(uint64_t seq_id, const std::vector<std::shared_ptr<NumArray>>& input)
        : sequence_id(seq_id), input_data(input) {
        timestamp = std::chrono::steady_clock::now();
    }
};

// 图片处理结果
struct ImageProcessingResult {
    uint64_t sequence_id;
    std::chrono::steady_clock::time_point timestamp;
    std::map<std::string, InferenceResult> model_results; // 模型名 -> 结果
    std::string service_ip;
    bool is_complete;
    bool has_error;
    std::string error_message;
    double total_processing_time_ms;
    
    ImageProcessingResult(uint64_t seq_id) 
        : sequence_id(seq_id), is_complete(false), has_error(false), 
          total_processing_time_ms(0.0) {
        timestamp = std::chrono::steady_clock::now();
    }
};

// 服务实例信息
struct ServiceInstance {
    std::string ip;
    int port;
    bool is_available;
    int current_load;
    int max_concurrent_requests;
    double avg_processing_time_ms;
    std::chrono::steady_clock::time_point last_health_check;
    int consecutive_failures;
    
    ServiceInstance(const std::string& ip, int port, int max_concurrent = 3)
        : ip(ip), port(port), is_available(true), current_load(0),
          max_concurrent_requests(max_concurrent), avg_processing_time_ms(0.0),
          consecutive_failures(0) {
        last_health_check = std::chrono::steady_clock::now();
    }
    
    std::string getBaseUrl() const {
        return "http://" + ip + ":" + std::to_string(port);
    }
    
    bool canAcceptRequest() const {
        return is_available && current_load < max_concurrent_requests;
    }
};

// 图片级别负载均衡器
class ImageLevelLoadBalancer {
public:
    ImageLevelLoadBalancer();
    ~ImageLevelLoadBalancer();
    
    // 初始化负载均衡器
    bool initialize(const std::vector<std::string>& service_ips, 
                   int port = 1180,
                   const std::vector<std::string>& model_endpoints = {});
    
    // 提交图片处理请求（返回sequence_id）
    uint64_t submitImageProcessing(const std::vector<std::shared_ptr<NumArray>>& input);
    
    // 获取下一个完成的结果（按时序）
    bool getNextResult(ImageProcessingResult& result);
    
    // 检查服务健康状态
    void performHealthCheck();
    
    // 获取负载均衡状态
    Json::Value getLoadBalancerStatus() const;
    
    // 获取统计信息
    size_t getPendingRequestCount() const;
    size_t getCompletedRequestCount() const;
    double getAverageProcessingTime() const;

private:
    // 配置参数
    std::vector<ServiceInstance> service_instances_;
    std::vector<std::string> model_endpoints_;
    int default_port_;
    
    // 请求管理
    std::map<uint64_t, ImageProcessingRequest> pending_requests_;
    std::map<uint64_t, std::future<ImageProcessingResult>> processing_futures_;
    std::map<uint64_t, ImageProcessingResult> completed_results_;
    
    // 序列号管理
    std::atomic<uint64_t> next_sequence_id_;
    std::atomic<uint64_t> next_output_sequence_;
    
    // 线程安全
    mutable std::mutex instances_mutex_;
    mutable std::mutex requests_mutex_;
    mutable std::mutex results_mutex_;
    
    // 统计信息
    std::atomic<size_t> completed_count_;
    std::atomic<double> total_processing_time_;
    
    // 私有方法
    std::string selectBestService();
    std::future<ImageProcessingResult> processImageOnService(
        const ImageProcessingRequest& request, 
        const std::string& service_ip);
    ImageProcessingResult executeAllModelsOnService(
        const ImageProcessingRequest& request,
        const std::string& service_ip);
    void updateServiceLoad(const std::string& service_ip, int delta);
    void updateServiceStats(const std::string& service_ip, 
                           bool success, 
                           double processing_time);
    bool isServiceHealthy(const ServiceInstance& instance) const;
    void cleanupCompletedRequests();
};

// 多模型处理器（在单个服务实例上执行所有模型）
class SingleServiceMultiModelProcessor {
public:
    SingleServiceMultiModelProcessor(const std::string& base_url,
                                   const std::vector<std::string>& endpoints);
    
    // 在单个服务上处理所有模型
    ImageProcessingResult processAllModels(const ImageProcessingRequest& request);

private:
    std::string base_url_;
    std::vector<std::string> model_endpoints_;
    
    // 私有方法
    InferenceResult callModelEndpoint(const std::string& endpoint,
                                    const std::vector<std::shared_ptr<NumArray>>& input);
    std::vector<unsigned char> convertInputToImageData(
        const std::vector<std::shared_ptr<NumArray>>& input);
    
    // CURL回调
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, void* userp);
};

} // namespace tongxing

#endif // _IMAGE_LEVEL_LOAD_BALANCER_H_
