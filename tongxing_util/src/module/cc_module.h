#ifndef __CC_MODULE_H__
#define __CC_MODULE_H__
#include "cc_assert.h"
#include <map>
#include <memory>
#include <vector>
#include "cc_module_register.h"
#include "cc_tensor.h"
#include "json.h"
#include <string>

namespace tongxing
{

  class CcModule
  {
  public:
    CcModule();
    virtual ~CcModule();
    virtual int init(const Json::Value &config);
    virtual int setInput(const std::vector<std::shared_ptr<NumArray>> &in);
    virtual int execute();
    virtual size_t getOutputNum();
    virtual std::shared_ptr<NumArray> getOutput(int index);

  private:
    std::shared_ptr<CcModule> last_module_ptr;
    long input_id;
    std::vector<long> output_id;
    std::vector<std::shared_ptr<NumArray>> output_;
    std::vector<std::shared_ptr<NumArray>> input_;
  };

  typedef CcModule *(*CreatCcModule_fun)(const Json::Value &config);

  class CcModuleRegister
  {
  public:
    static CcModuleRegister &instance();
    CcModuleRegister();
    void module_register(const std::string &key, CreatCcModule_fun fun);
    std::shared_ptr<CcModule> get_module(const std::string &key, const Json::Value &config);
    void del_function(const std::string &key);

  private:
    std::map<std::string, CreatCcModule_fun> fun_map;
  };
  std::shared_ptr<CcModule> get_cc_module(const Json::Value &config);
  std::shared_ptr<CcModule> get_cc_module(const std::string &class_name);
  class CcExecuteModuleRegister
  {
  public:
    CcExecuteModuleRegister(const std::string& key, CreatCcModule_fun fun)
    {
      std::cout<<"Register Module "<<key<<" "<<(void*)fun<<std::endl;
      // Json::Value config;
      // std::cout<<(void*)fun(config)->getOutputNum()<<std::endl;
      CcModuleRegister::instance().module_register(key, fun);
    } 

  };
} // namespace tongxing

#define __CONNECT__FLAG(f1, f2) __##f1##_##f2##__

#define REGISTER_CC_MODULE(ClassName, ClassType)             \
  __attribute__((optimize("O0"))) CcModule *creat_##ClassName(const Json::Value &config)     \
  {                                                          \
    CcModule *ptr = dynamic_cast<ClassType *>(new ClassType); \
    int ret=ptr->init(config); \
    cc_assert(ret==0);           \
    return ptr;                                              \
  }                                                          \
  __attribute__((constructor, used)) static void register_module_##ClassName(void){\
      CcModuleRegister::instance().module_register(#ClassName, creat_##ClassName);\
  }

#endif
