#ifndef __CC_EXTEND_H__
#define __CC_EXTEND_H__
#include "cc_module.h"
#include "json.h"
#include <vector>
namespace tongxing {
    class CcExtend: public CcModule
    {
        public:
            int init(const Json::Value& config);
            int setInput(const std::vector<std::shared_ptr<NumArray> >& in);
            int execute();
            size_t getOutputNum();
            std::shared_ptr<NumArray> getOutput(int index);
        private:
            std::vector<std::shared_ptr<NumArray> > input_;
            std::vector<std::shared_ptr<NumArray> > output_;
            int dim_;
            int extend_num_;
            bool plane_ = true;

    };



}



#endif