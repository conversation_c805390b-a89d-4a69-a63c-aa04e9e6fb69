#include "cc_extend.h"
#include "cc_numarray_tool.h"
#include <string.h>
#include <opencv2/opencv.hpp>
namespace tongxing
{

    int CcExtend::init(const Json::Value &config)
    {
        dim_=config["dim"].asInt();
        extend_num_=config["extend_num"].asInt();
        if (config["plane"].isBool())
        {
            plane_ = config["plane"].asBool();
        }
        
        return 0;
    }
    int CcExtend::setInput(const std::vector<std::shared_ptr<NumArray>> &in){
        input_=in;
        return 0;
    }
    int CcExtend::execute(){
        if (plane_ == false)
        {   
            std::vector<int> out_shape = input_[0]->shape; 
            out_shape.resize(input_[0]->shape.size());
            out_shape[0] = input_[0]->shape[0];
            out_shape[1] = input_[0]->shape[2];
            out_shape[2] = input_[0]->shape[3];
            out_shape[3] = input_[0]->shape[1]+extend_num_;
            
            // cv::Mat img_in(112,112,CV_32FC1,input_[0]->data);
            // cv::imwrite("./face_rec_input_gray.jpg",img_in);
            std::shared_ptr<NumArray> rgb_plane = creat_numarray(out_shape, input_[0]->type);

            
            float *p_rgb = (float*)rgb_plane->data;
            float *p_gray = (float*)input_[0]->data;
            int img_h = input_[0]->shape[2];
            int img_w = input_[0]->shape[3];
            int img_size = img_h * img_w;
            for (int i = 0; i < img_size; ++i) {
                p_rgb[i*3] = p_gray[i];
                p_rgb[i*3+1] = p_gray[i];
                p_rgb[i*3+2] = p_gray[i];
            
            }
            // output->getTensor<unsigned char>()->printShape();
            std::vector<std::shared_ptr<NumArray> > outputs;
            outputs.push_back(rgb_plane);
            output_=outputs;
            
        }else{
            std::shared_ptr<NumArray>  output=numarray_extend(input_[0],dim_,extend_num_,true);
            // output->getTensor<unsigned char>()->printShape();
            std::vector<std::shared_ptr<NumArray> > outputs;
            outputs.push_back(output);
            output_=outputs;
        }
        return 0;
    }
    size_t CcExtend::getOutputNum(){
        return output_.size();
    }
    std::shared_ptr<NumArray> CcExtend::getOutput(int index){
        return output_[index];

    }
    REGISTER_CC_MODULE(Extend, CcExtend)
}