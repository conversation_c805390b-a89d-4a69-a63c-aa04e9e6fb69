#include <iostream>
#include <vector>
#include <string>
#include <chrono>
#include <thread>
#include <map>
#include <curl/curl.h>

// 简化的测试，不依赖复杂的框架
class MinimalLoadBalancer {
private:
    std::vector<std::string> service_ips_;
    int port_;
    size_t current_index_;
    
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, void* userp) {
        ((std::string*)userp)->append((char*)contents, size * nmemb);
        return size * nmemb;
    }
    
public:
    MinimalLoadBalancer(const std::vector<std::string>& ips, int port) 
        : service_ips_(ips), port_(port), current_index_(0) {
        curl_global_init(CURL_GLOBAL_DEFAULT);
    }
    
    ~MinimalLoadBalancer() {
        curl_global_cleanup();
    }
    
    std::string selectNextService() {
        if (service_ips_.empty()) return "";
        
        std::string selected = service_ips_[current_index_];
        current_index_ = (current_index_ + 1) % service_ips_.size();
        return selected;
    }
    
    bool testService(const std::string& ip) {
        CURL* curl = curl_easy_init();
        if (!curl) return false;
        
        std::string url = "http://" + ip + ":" + std::to_string(port_) + "/";
        std::string response;
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 3L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 2L);
        
        CURLcode res = curl_easy_perform(curl);
        long http_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
        
        curl_easy_cleanup(curl);
        
        return (res == CURLE_OK && http_code > 0);
    }
    
    bool sendTestRequest(const std::string& ip, const std::string& endpoint) {
        CURL* curl = curl_easy_init();
        if (!curl) return false;
        
        std::string url = "http://" + ip + ":" + std::to_string(port_) + "/" + endpoint;
        std::string response;
        
        // 创建简单的测试数据
        std::string test_data(1024, 'A'); // 1KB的测试数据
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, test_data.c_str());
        curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, test_data.size());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        
        struct curl_slist* headers = nullptr;
        headers = curl_slist_append(headers, "Content-Type: application/octet-stream");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        
        CURLcode res = curl_easy_perform(curl);
        long http_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
        
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        
        bool success = (res == CURLE_OK && http_code == 200);
        if (success) {
            std::cout << "✅ 成功调用 " << ip << "/" << endpoint 
                      << " (响应大小: " << response.size() << " bytes)" << std::endl;
        } else {
            std::cout << "❌ 调用失败 " << ip << "/" << endpoint 
                      << " (CURL: " << res << ", HTTP: " << http_code << ")" << std::endl;
        }
        
        return success;
    }
};

void print_header(const std::string& title) {
    std::cout << "\n" << std::string(50, '=') << std::endl;
    std::cout << "  " << title << std::endl;
    std::cout << std::string(50, '=') << std::endl;
}

bool test_service_connectivity() {
    print_header("服务连通性测试");
    
    std::vector<std::string> test_ips = {"***********", "***********"};
    MinimalLoadBalancer balancer(test_ips, 1180);
    
    bool any_available = false;
    
    for (const auto& ip : test_ips) {
        std::cout << "测试服务: " << ip << ":1180" << std::endl;
        
        if (balancer.testService(ip)) {
            std::cout << "✅ 服务 " << ip << " 可用" << std::endl;
            any_available = true;
        } else {
            std::cout << "❌ 服务 " << ip << " 不可用" << std::endl;
        }
    }
    
    return any_available;
}

bool test_load_balancing() {
    print_header("负载均衡测试");
    
    std::vector<std::string> test_ips = {"***********", "***********"};
    MinimalLoadBalancer balancer(test_ips, 1180);
    
    // 测试轮询分配
    std::map<std::string, int> usage_count;
    
    std::cout << "测试轮询分配..." << std::endl;
    for (int i = 0; i < 10; ++i) {
        std::string selected = balancer.selectNextService();
        usage_count[selected]++;
        std::cout << "请求 " << (i + 1) << " -> " << selected << std::endl;
    }
    
    std::cout << "\n负载分布统计:" << std::endl;
    for (const auto& pair : usage_count) {
        std::cout << "  " << pair.first << ": " << pair.second << " 次" << std::endl;
    }
    
    // 检查分布是否相对均匀
    bool balanced = true;
    if (usage_count.size() == 2) {
        auto it = usage_count.begin();
        int count1 = it->second;
        ++it;
        int count2 = it->second;
        
        if (std::abs(count1 - count2) > 2) {
            balanced = false;
        }
    }
    
    std::cout << "负载均衡效果: " << (balanced ? "良好" : "需要改进") << std::endl;
    
    return true;
}

bool test_model_endpoints() {
    print_header("模型端点测试");
    
    std::vector<std::string> test_ips = {"***********", "***********"};
    std::vector<std::string> endpoints = {"FaceDetection", "FaceKeypoints", "eye", "Dms_PhoneSmoking"};
    
    MinimalLoadBalancer balancer(test_ips, 1180);
    
    bool any_success = false;
    
    for (const auto& ip : test_ips) {
        std::cout << "\n测试服务 " << ip << " 的模型端点:" << std::endl;
        
        // 先检查服务是否可用
        if (!balancer.testService(ip)) {
            std::cout << "⚠️  服务 " << ip << " 不可用，跳过端点测试" << std::endl;
            continue;
        }
        
        for (const auto& endpoint : endpoints) {
            std::cout << "  测试端点: " << endpoint << std::endl;
            
            auto start_time = std::chrono::high_resolution_clock::now();
            bool success = balancer.sendTestRequest(ip, endpoint);
            auto end_time = std::chrono::high_resolution_clock::now();
            
            auto duration = std::chrono::duration<double, std::milli>(end_time - start_time).count();
            
            if (success) {
                std::cout << "    ✅ 成功 (耗时: " << duration << "ms)" << std::endl;
                any_success = true;
            } else {
                std::cout << "    ❌ 失败 (耗时: " << duration << "ms)" << std::endl;
            }
            
            // 短暂延迟避免过于频繁的请求
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
    }
    
    return any_success;
}

bool test_sequential_processing() {
    print_header("时序处理测试");
    
    std::vector<std::string> test_ips = {"***********", "***********"};
    MinimalLoadBalancer balancer(test_ips, 1180);
    
    // 模拟图片级别的负载均衡
    const int num_images = 6;
    std::vector<std::pair<int, std::string>> assignments; // (image_id, service_ip)
    
    std::cout << "模拟 " << num_images << " 张图片的分配:" << std::endl;
    
    for (int i = 1; i <= num_images; ++i) {
        std::string assigned_service = balancer.selectNextService();
        assignments.emplace_back(i, assigned_service);
        std::cout << "图片 " << i << " -> 服务 " << assigned_service << std::endl;
    }
    
    // 模拟处理时间和结果收集
    std::cout << "\n模拟处理和结果收集:" << std::endl;
    
    std::map<std::string, int> service_usage;
    
    for (const auto& assignment : assignments) {
        int image_id = assignment.first;
        const std::string& service_ip = assignment.second;
        
        service_usage[service_ip]++;
        
        // 模拟处理时间
        auto start_time = std::chrono::high_resolution_clock::now();
        std::this_thread::sleep_for(std::chrono::milliseconds(100 + (image_id % 3) * 50));
        auto end_time = std::chrono::high_resolution_clock::now();
        
        auto processing_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
        
        std::cout << "图片 " << image_id << " 在服务 " << service_ip 
                  << " 上处理完成 (耗时: " << processing_time << "ms)" << std::endl;
    }
    
    std::cout << "\n服务使用统计:" << std::endl;
    for (const auto& pair : service_usage) {
        double percentage = (pair.second * 100.0) / num_images;
        std::cout << "  " << pair.first << ": " << pair.second 
                  << " 张图片 (" << percentage << "%)" << std::endl;
    }
    
    return true;
}

int main() {
    std::cout << "最小化负载均衡器测试" << std::endl;
    std::cout << "测试目标: *********** 和 ***********" << std::endl;
    std::cout << std::string(50, '=') << std::endl;
    
    bool all_passed = true;
    
    try {
        // 1. 服务连通性测试
        if (!test_service_connectivity()) {
            std::cout << "⚠️  没有可用的服务，某些测试可能失败" << std::endl;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 2. 负载均衡逻辑测试
        if (!test_load_balancing()) {
            all_passed = false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 3. 模型端点测试
        if (!test_model_endpoints()) {
            std::cout << "⚠️  模型端点测试未完全成功，可能是服务配置问题" << std::endl;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 4. 时序处理测试
        if (!test_sequential_processing()) {
            all_passed = false;
        }
        
        // 最终结果
        print_header("测试结果总结");
        
        if (all_passed) {
            std::cout << "🎉 核心功能测试通过!" << std::endl;
            std::cout << "\n验证的功能:" << std::endl;
            std::cout << "✅ 服务连通性检查" << std::endl;
            std::cout << "✅ 轮询负载均衡逻辑" << std::endl;
            std::cout << "✅ 图片级别分配模拟" << std::endl;
            std::cout << "✅ 时序处理流程" << std::endl;
        } else {
            std::cout << "❌ 部分测试失败" << std::endl;
        }
        
        std::cout << "\n注意事项:" << std::endl;
        std::cout << "- 这是简化的连通性和逻辑测试" << std::endl;
        std::cout << "- 实际的模型推理需要完整的框架支持" << std::endl;
        std::cout << "- 确保OAX4600服务在目标IP上正常运行" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试异常: " << e.what() << std::endl;
        return 1;
    }
    
    return all_passed ? 0 : 1;
}
