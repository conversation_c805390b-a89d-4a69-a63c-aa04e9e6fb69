#include <iostream>
#include <vector>
#include <chrono>
#include <thread>
#include <future>
#include "model_service_pool.h"

using namespace tongxing;

void test_basic_functionality() {
    std::cout << "\n=== Testing Basic Functionality ===" << std::endl;
    
    ModelServicePool pool;
    if (!pool.initialize("ip_pool.json")) {
        std::cerr << "Failed to initialize service pool" << std::endl;
        return;
    }
    
    std::cout << "Available services: " << pool.getAvailableServiceCount() << std::endl;
    
    // 创建测试数据
    std::vector<unsigned char> test_data(1024, 0x42); // 1KB测试数据
    
    // 发送同步请求
    auto result = pool.sendSyncRequest("FaceKeypoints", test_data, "image/jpeg");
    
    std::cout << "Sync request result: " << (result.success ? "SUCCESS" : "FAILED") << std::endl;
    if (!result.success) {
        std::cout << "Error: " << result.error_message << std::endl;
    }
    std::cout << "Response time: " << result.response_time_ms << "ms" << std::endl;
}

void test_concurrent_requests() {
    std::cout << "\n=== Testing Concurrent Requests ===" << std::endl;
    
    ModelServicePool pool;
    if (!pool.initialize("ip_pool.json")) {
        std::cerr << "Failed to initialize service pool" << std::endl;
        return;
    }
    
    const int num_requests = 10;
    std::vector<std::future<RequestResult>> futures;
    std::vector<unsigned char> test_data(512, 0x55); // 512字节测试数据
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 发送多个异步请求
    for (int i = 0; i < num_requests; ++i) {
        futures.push_back(pool.sendAsyncRequest("FaceKeypoints", test_data, "image/jpeg"));
    }
    
    // 等待所有请求完成
    int success_count = 0;
    double total_response_time = 0.0;
    
    for (auto& future : futures) {
        auto result = future.get();
        if (result.success) {
            success_count++;
        }
        total_response_time += result.response_time_ms;
        std::cout << "Request result: " << (result.success ? "SUCCESS" : "FAILED") 
                  << " (" << result.response_time_ms << "ms)" << std::endl;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    std::cout << "\nConcurrent test results:" << std::endl;
    std::cout << "Total requests: " << num_requests << std::endl;
    std::cout << "Successful requests: " << success_count << std::endl;
    std::cout << "Total time: " << total_time << "ms" << std::endl;
    std::cout << "Average response time: " << (total_response_time / num_requests) << "ms" << std::endl;
    std::cout << "Throughput: " << (num_requests * 1000.0 / total_time) << " requests/second" << std::endl;
}

void test_service_status() {
    std::cout << "\n=== Testing Service Status ===" << std::endl;
    
    ModelServicePool pool;
    if (!pool.initialize("ip_pool.json")) {
        std::cerr << "Failed to initialize service pool" << std::endl;
        return;
    }
    
    // 执行健康检查
    pool.performHealthCheck();
    
    // 获取服务状态
    auto services = pool.getServiceStatus();
    std::cout << "Service status report:" << std::endl;
    for (const auto& service : services) {
        std::cout << "Service " << service.ip << ":" << service.port 
                  << " - Status: ";
        switch (service.status) {
            case ServiceStatus::AVAILABLE:
                std::cout << "AVAILABLE";
                break;
            case ServiceStatus::BUSY:
                std::cout << "BUSY";
                break;
            case ServiceStatus::UNAVAILABLE:
                std::cout << "UNAVAILABLE";
                break;
        }
        std::cout << " - Failures: " << service.consecutive_failures
                  << " - Avg Response: " << service.avg_response_time_ms << "ms" << std::endl;
    }
}

void test_load_balancing() {
    std::cout << "\n=== Testing Load Balancing ===" << std::endl;
    
    ModelServicePool pool;
    if (!pool.initialize("ip_pool.json")) {
        std::cerr << "Failed to initialize service pool" << std::endl;
        return;
    }
    
    std::vector<unsigned char> test_data(256, 0x77);
    std::unordered_map<std::string, int> service_usage;
    
    // 发送多个请求并统计使用的服务
    for (int i = 0; i < 20; ++i) {
        auto result = pool.sendSyncRequest("FaceKeypoints", test_data, "image/jpeg");
        if (result.success) {
            // 从URL中提取IP地址
            size_t pos = result.service_url.find("://") + 3;
            size_t end_pos = result.service_url.find(":", pos);
            if (end_pos != std::string::npos) {
                std::string ip = result.service_url.substr(pos, end_pos - pos);
                service_usage[ip]++;
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    std::cout << "Load balancing results:" << std::endl;
    for (const auto& pair : service_usage) {
        std::cout << "Service " << pair.first << ": " << pair.second << " requests" << std::endl;
    }
}

int main() {
    std::cout << "Model Service Pool Test Suite" << std::endl;
    std::cout << "=============================" << std::endl;
    
    try {
        test_basic_functionality();
        test_concurrent_requests();
        test_service_status();
        test_load_balancing();
        
        std::cout << "\n=== All Tests Completed ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
