#include <stdio.h>
#include <csignal>
#include <fstream>
#include <iostream>
#include "calmcar_dms_process.h"
#include "cc_media_server.h"
#include "libovt.h"
#include "md5.h"
#include "opencv2/opencv.hpp"
#include "tx_dms_sdk.h"
extern "C" {
int cnn_init(void);
}
#define WIDTH 1280
#define HEIGHT 800
#define DEBUG_WITH_CAM 0
/*
 * Sensor
 */
int vs_id = 5;
void sensor_init(void) {
    int ret;

    ret = ovt_datapath_init(0);
    if (ret) {
        fprintf(stderr, "ovt_datapath_init(0) err %d\n", ret);
        return;
    }
    ret = ovt_datapath_start();
    assert(!ret);

    ret = ovt_vs_init(vs_id);
    assert(!ret);

    ret = ovt_vs_set_resolution(vs_id, (WIDTH << 16) | HEIGHT);
    assert(!ret);

    ret = ovt_vs_start(vs_id);
    assert(!ret);
}

void sensor_exit(void) {
    ovt_vs_stop(vs_id);
    ovt_vs_exit(vs_id);
    ovt_datapath_stop();
    ovt_datapath_exit();
}

std::atomic_bool isRunning(true);
void signalHandler(int signal) {
    if (signal == SIGINT) {
        std::cout << "Caught Ctrl + C, exiting gracefully..." << std::endl;
        sensor_exit();
        isRunning.store(false);
    }
}

// 构建DMS回调函数
long last_ts = 0;
TXDmsResult dms_result;

tongxing::CcDmsProcess handle;

static long get_timestamp() {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    long now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    return now_ts;
}

static int activate_test() {
    std::string ov4600_str;
    const char* filePath = "/proc/uid";
    std::ifstream file(filePath);

    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filePath << std::endl;
        return -1;
    }
    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();
    ov4600_str = content;

    std::string proid = "byd";
    std::string did = ov4600_str;
    // key是固定的，对应于特定的算法
    // 1. adas算法：96k018515hk；
    // 2. 脱敏算法：96k018522hk;
    // 3. dms算法：96k018516hk;
    std::string data_key = "96k018516hk";
    std::string uuid_str = data_key + did;
    std::string proid_str = "r7" + proid;
    // std::cout<< "uuid_str:" << uuid_str <<std::endl;
    std::string md5_result = MD5(uuid_str).toStr();
    // std::cout<< "md5_result:" << md5_result <<std::endl;
    std::string result = MD5(md5_result + proid_str).toStr();
    int activate_code_len = 32;
    int status = TXDmsActivate(result.c_str(), activate_code_len);
    std::cout << "[DMS ACTIVATE]: SELF TEST ALGORITHM ACTIVATE STATUS:" << 
        (status != 0 ? "FAIL" : "SUCCESS")  << " !!!" << std::endl;

    return status;
}

int main(int argc, char** argv) {
    // 注册信号处理函数
    std::signal(SIGINT, signalHandler);
    // TXDmsSetLogLevel(LEVEL_DEBUG);
    cnn_init();
    sensor_init();
    long img_index = 0;
    std::cout << "dms verson: " << TXDmsGetVersion() << std::endl;
    std::cout << "dms really verson: " << TXDmsGetRealVersion() << std::endl;
    // long hDms = TXDmsCreate(NULL, "cache/");
    // if (hDms == 0) {
    //     std::cout << "creat dms fail" << std::endl;
    //     return -1;
    // } else {
    //     std::cout << "creat dms success" << std::endl;
    // }

    int iRet = handle.Init(NULL, "cache/");
    if (iRet != 0) {
        std::cout << "creat dms fail" << std::endl;
        return -1;
    } else {
        std::cout << "creat dms success" << std::endl;
    }
    //  std::cout << "--------main------- create " << std::endl;
    TXPoint2i left_top_point;
    TXPoint2i right_bottom_point;
    left_top_point.x = 300;
    // left_top_point.x = 500;  //海鸥参数
    left_top_point.y = 0;
    right_bottom_point.x = 1280;
    right_bottom_point.y = 800;

    // 激活test
    int status = activate_test();

    TXImageInfo image_;
    image_.dataType = TXInputFormat::GRAY;
    image_.height = 800;
    image_.width = 1280;
    image_.stride = 1280;
    image_.dataLen = 1280 * 800;

    while (isRunning.load()) {
        // 检查是否需要退出
        if (!isRunning.load()) {
            break;
        }

        ovt_frame_t* frame = ovt_vs_get_frame(vs_id, 200);
        if (!frame) {
            fprintf(stderr, "ERROR: vs_get_frame(%d)\n", vs_id);
            continue;
        }
        image_.data = (char*)frame->addr;

        // std::cout << " ---------while--------: "<< frame->size<<" "<<frame->size1 << std::endl;
        TXCarInfo carInfo = {0};
        carInfo.speed = 80;
        carInfo.gear = TXGearPition::FORWARD;
        carInfo.steer_whl_snsr_rad = 0;
        carInfo.turn_light = TXTurnSignal::TURN_OFF;
        carInfo.driver_door_status = TXDriverDoorStatus::DOOR_CLOSE;
        carInfo.driver_seat_status = TXDriverSeatStatus::SEAT_STATIC;
        // carInfo.camera_fault = 0;
        // carInfo.can_fault = 0;
        carInfo.mask = (TX_CAR_INFO_MASK_SPEED | TX_CAR_INFO_MASK_STEER_WHL_SNSR |
                        TX_CAR_INFO_MASK_GEARPOSITION | TX_CAR_INFO_MASK_TURN_LIGHT |
                        TX_CAR_DRIVER_DOOR | TX_CAR_DRIVER_SEAT);
        handle.updateCarInfo(&carInfo);
        // TXDmsUpdataCarInfo(hDms, &carInfo);
        // carInfo.mask = TX_CAR_INFO_MASK_STEER_WHL_SNSR;
        // TXDmsUpdataCarInfo(hDms, &carInfo);
        // carInfo.mask = TX_CAR_INFO_MASK_GEARPOSITION;
        // TXDmsUpdataCarInfo(hDms, &carInfo);
        // carInfo.mask = TX_CAR_INFO_MASK_TURN_LIGHT;
        // TXDmsUpdataCarInfo(hDms, &carInfo);

        double start_ts = get_timestamp();
        // TXDmsSetInput(hDms, &image_, &dms_result);
        handle.SetInput(&image_, &dms_result);
        double end_ts = get_timestamp();
        printf("TXDmsSetInput ts:%f\n", (float)(end_ts - start_ts));
        // if(dms_result.dms_status>0&&dms_result.dms_status<6){
        //     printf("%f :: dms_result.dms_status:%d\n",start_ts,dms_result.dms_status);
        // }
        // TXDmsUpdataCarInfo(hDms, &carInfo);

        // cv::Mat show(cv::Size(1280,720),CV_8UC1,image_.data);
        // cv::imwrite("save.jpg",show);
        ovt_vs_remove_frame(vs_id, frame);
        // for(int i=0;i<10;i++){
        //     cv::circle(show,cv::Point(g_result.face_info.landmarks[i].x,g_result.face_info.landmarks[i].y),3,cv::Scalar(0));
        // }
        // cv::rectangle(show,cv::Rect(cv::Point(g_result.face_info.xmin,g_result.face_info.ymin),cv::Point(g_result.face_info.xmax,g_result.face_info.ymax)),cv::Scalar(255));
        // cv::Mat show(cv::Size(1280,720),CV_8UC1,image_.data);
        // cv::imwrite("save.jpg",show);
        // usleep(100000);
    }
    sensor_exit();
    return 0;
}