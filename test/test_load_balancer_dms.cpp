#include <iostream>
#include <vector>
#include <chrono>
#include <thread>
#include <opencv2/opencv.hpp>
#include "cc_dms_process.h"
#include "cc_numarray_tool.h"

using namespace tongxing;

// 创建测试图片
cv::Mat create_test_image(int image_id, int width = 640, int height = 480) {
    cv::Mat image(height, width, CV_8UC3);
    
    // 创建有特征的测试图片
    cv::Scalar color(image_id * 30 % 255, image_id * 50 % 255, image_id * 70 % 255);
    image.setTo(color);
    
    // 添加一些图案
    cv::circle(image, cv::Point(width/2, height/2), 50 + image_id * 10, cv::Scalar(255, 255, 255), 2);
    cv::putText(image, "IMG_" + std::to_string(image_id), cv::Point(50, 50), 
                cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 0, 0), 2);
    
    return image;
}

// 将OpenCV Mat转换为NumArray
std::shared_ptr<NumArray> mat_to_numarray(const cv::Mat& mat) {
    // 转换为RGB格式并调整维度为NCHW
    cv::Mat rgb_mat;
    cv::cvtColor(mat, rgb_mat, cv::COLOR_BGR2RGB);
    
    int height = rgb_mat.rows;
    int width = rgb_mat.cols;
    int channels = rgb_mat.channels();
    
    auto array = creat_numarray({1, channels, height, width}, NumArray::DataType::UINT8);
    
    // 转换HWC到CHW格式
    for (int c = 0; c < channels; ++c) {
        for (int h = 0; h < height; ++h) {
            for (int w = 0; w < width; ++w) {
                int src_idx = h * width * channels + w * channels + c;
                int dst_idx = c * height * width + h * width + w;
                array->data[dst_idx] = rgb_mat.data[src_idx];
            }
        }
    }
    
    return array;
}

void print_test_header(const std::string& test_name) {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "  " << test_name << std::endl;
    std::cout << std::string(60, '=') << std::endl;
}

bool test_load_balancer_initialization() {
    print_test_header("Load Balancer Initialization Test");
    
    try {
        CcDmsProcess dms_process;
        
        // 使用负载均衡配置初始化
        std::string config_path = "resourc/oax4600_http/dms_config_load_balancer.json";
        
        std::cout << "初始化DMS处理器，配置文件: " << config_path << std::endl;
        
        int ret = dms_process.InitSdk(config_path);
        if (ret != 0) {
            std::cerr << "❌ DMS初始化失败，返回码: " << ret << std::endl;
            return false;
        }
        
        std::cout << "✅ DMS处理器初始化成功" << std::endl;
        
        // 清理
        dms_process.DeinitSdk();
        std::cout << "✅ DMS处理器清理完成" << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 初始化测试异常: " << e.what() << std::endl;
        return false;
    }
}

bool test_single_image_processing() {
    print_test_header("Single Image Processing Test");
    
    try {
        CcDmsProcess dms_process;
        
        std::string config_path = "resourc/oax4600_http/dms_config_load_balancer.json";
        
        int ret = dms_process.InitSdk(config_path);
        if (ret != 0) {
            std::cerr << "❌ DMS初始化失败" << std::endl;
            return false;
        }
        
        std::cout << "✅ DMS初始化成功，开始处理单张图片" << std::endl;
        
        // 创建测试图片
        cv::Mat test_image = create_test_image(1);
        auto input_array = mat_to_numarray(test_image);
        
        std::cout << "📤 处理图片，尺寸: " << test_image.cols << "x" << test_image.rows << std::endl;
        
        // 处理图片
        auto start_time = std::chrono::high_resolution_clock::now();
        
        std::vector<std::shared_ptr<NumArray>> outputs;
        ret = dms_process.Process({input_array}, outputs);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto processing_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
        
        if (ret == 0) {
            std::cout << "✅ 图片处理成功，耗时: " << processing_time << "ms" << std::endl;
            std::cout << "📊 输出tensor数量: " << outputs.size() << std::endl;
            
            // 显示输出信息
            for (size_t i = 0; i < outputs.size(); ++i) {
                auto& output = outputs[i];
                std::cout << "  输出 " << i << ": shape [";
                for (size_t j = 0; j < output->shape.size(); ++j) {
                    std::cout << output->shape[j];
                    if (j < output->shape.size() - 1) std::cout << ", ";
                }
                std::cout << "]" << std::endl;
            }
        } else {
            std::cerr << "❌ 图片处理失败，返回码: " << ret << std::endl;
            dms_process.DeinitSdk();
            return false;
        }
        
        dms_process.DeinitSdk();
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 单图片处理测试异常: " << e.what() << std::endl;
        return false;
    }
}

bool test_multiple_images_sequential() {
    print_test_header("Multiple Images Sequential Processing Test");
    
    try {
        CcDmsProcess dms_process;
        
        std::string config_path = "resourc/oax4600_http/dms_config_load_balancer.json";
        
        int ret = dms_process.InitSdk(config_path);
        if (ret != 0) {
            std::cerr << "❌ DMS初始化失败" << std::endl;
            return false;
        }
        
        const int num_images = 5;
        std::vector<double> processing_times;
        
        std::cout << "✅ DMS初始化成功，开始处理 " << num_images << " 张图片" << std::endl;
        
        auto overall_start = std::chrono::high_resolution_clock::now();
        
        for (int i = 1; i <= num_images; ++i) {
            std::cout << "\n📤 处理图片 " << i << "/" << num_images << std::endl;
            
            // 创建测试图片
            cv::Mat test_image = create_test_image(i);
            auto input_array = mat_to_numarray(test_image);
            
            auto start_time = std::chrono::high_resolution_clock::now();
            
            std::vector<std::shared_ptr<NumArray>> outputs;
            ret = dms_process.Process({input_array}, outputs);
            
            auto end_time = std::chrono::high_resolution_clock::now();
            auto processing_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
            processing_times.push_back(processing_time);
            
            if (ret == 0) {
                std::cout << "✅ 图片 " << i << " 处理成功，耗时: " << processing_time 
                          << "ms，输出: " << outputs.size() << " 个tensor" << std::endl;
            } else {
                std::cerr << "❌ 图片 " << i << " 处理失败，返回码: " << ret << std::endl;
                dms_process.DeinitSdk();
                return false;
            }
            
            // 短暂间隔
            std::this_thread::sleep_for(std::chrono::milliseconds(300));
        }
        
        auto overall_end = std::chrono::high_resolution_clock::now();
        auto total_time = std::chrono::duration<double, std::milli>(overall_end - overall_start).count();
        
        // 性能分析
        double avg_time = std::accumulate(processing_times.begin(), processing_times.end(), 0.0) / processing_times.size();
        double min_time = *std::min_element(processing_times.begin(), processing_times.end());
        double max_time = *std::max_element(processing_times.begin(), processing_times.end());
        
        std::cout << "\n📊 性能统计:" << std::endl;
        std::cout << "  总耗时: " << total_time << "ms" << std::endl;
        std::cout << "  平均处理时间: " << avg_time << "ms" << std::endl;
        std::cout << "  最快处理时间: " << min_time << "ms" << std::endl;
        std::cout << "  最慢处理时间: " << max_time << "ms" << std::endl;
        std::cout << "  吞吐量: " << (num_images * 1000.0 / total_time) << " 图片/秒" << std::endl;
        
        dms_process.DeinitSdk();
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 多图片处理测试异常: " << e.what() << std::endl;
        return false;
    }
}

bool test_load_balancing_behavior() {
    print_test_header("Load Balancing Behavior Test");
    
    try {
        CcDmsProcess dms_process;
        
        std::string config_path = "resourc/oax4600_http/dms_config_load_balancer.json";
        
        int ret = dms_process.InitSdk(config_path);
        if (ret != 0) {
            std::cerr << "❌ DMS初始化失败" << std::endl;
            return false;
        }
        
        const int num_images = 8;
        std::cout << "✅ DMS初始化成功，测试负载均衡行为（" << num_images << " 张图片）" << std::endl;
        
        // 快速提交多张图片，观察负载均衡效果
        std::vector<std::future<std::pair<int, double>>> futures;
        
        auto overall_start = std::chrono::high_resolution_clock::now();
        
        for (int i = 1; i <= num_images; ++i) {
            std::cout << "📤 提交图片 " << i << " 进行异步处理" << std::endl;
            
            // 创建测试图片
            cv::Mat test_image = create_test_image(i);
            auto input_array = mat_to_numarray(test_image);
            
            // 异步处理
            auto future = std::async(std::launch::async, [&dms_process, input_array, i]() -> std::pair<int, double> {
                auto start_time = std::chrono::high_resolution_clock::now();
                
                std::vector<std::shared_ptr<NumArray>> outputs;
                int ret = dms_process.Process({input_array}, outputs);
                
                auto end_time = std::chrono::high_resolution_clock::now();
                auto processing_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
                
                return std::make_pair(ret, processing_time);
            });
            
            futures.push_back(std::move(future));
            
            // 短暂间隔模拟连续图片输入
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 收集结果
        std::cout << "\n📥 收集处理结果:" << std::endl;
        int successful_count = 0;
        std::vector<double> processing_times;
        
        for (int i = 0; i < num_images; ++i) {
            auto result = futures[i].get();
            int ret = result.first;
            double processing_time = result.second;
            
            if (ret == 0) {
                successful_count++;
                processing_times.push_back(processing_time);
                std::cout << "✅ 图片 " << (i + 1) << " 处理成功，耗时: " << processing_time << "ms" << std::endl;
            } else {
                std::cout << "❌ 图片 " << (i + 1) << " 处理失败，返回码: " << ret << std::endl;
            }
        }
        
        auto overall_end = std::chrono::high_resolution_clock::now();
        auto total_time = std::chrono::duration<double, std::milli>(overall_end - overall_start).count();
        
        std::cout << "\n📊 负载均衡测试结果:" << std::endl;
        std::cout << "  成功处理: " << successful_count << "/" << num_images << " 张图片" << std::endl;
        std::cout << "  成功率: " << (successful_count * 100.0 / num_images) << "%" << std::endl;
        std::cout << "  总耗时: " << total_time << "ms" << std::endl;
        
        if (!processing_times.empty()) {
            double avg_time = std::accumulate(processing_times.begin(), processing_times.end(), 0.0) / processing_times.size();
            std::cout << "  平均处理时间: " << avg_time << "ms" << std::endl;
            std::cout << "  并发吞吐量: " << (successful_count * 1000.0 / total_time) << " 图片/秒" << std::endl;
        }
        
        dms_process.DeinitSdk();
        return successful_count > 0;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 负载均衡测试异常: " << e.what() << std::endl;
        return false;
    }
}

int main() {
    std::cout << "DMS Load Balancer Integration Test" << std::endl;
    std::cout << "使用现有DMS框架测试负载均衡器" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    
    bool all_passed = true;
    
    try {
        // 1. 初始化测试
        if (!test_load_balancer_initialization()) {
            all_passed = false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 2. 单图片处理测试
        if (!test_single_image_processing()) {
            all_passed = false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 3. 多图片顺序处理测试
        if (!test_multiple_images_sequential()) {
            all_passed = false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 4. 负载均衡行为测试
        if (!test_load_balancing_behavior()) {
            all_passed = false;
        }
        
        // 最终结果
        print_test_header("测试结果总结");
        
        if (all_passed) {
            std::cout << "🎉 所有测试通过!" << std::endl;
            std::cout << "\n验证的功能:" << std::endl;
            std::cout << "✅ 负载均衡器模块正确集成到DMS框架" << std::endl;
            std::cout << "✅ 单图片处理功能正常" << std::endl;
            std::cout << "✅ 多图片顺序处理保持时序" << std::endl;
            std::cout << "✅ 负载均衡机制有效工作" << std::endl;
            std::cout << "✅ 与现有DMS框架完全兼容" << std::endl;
        } else {
            std::cout << "❌ 部分测试失败!" << std::endl;
            std::cout << "请检查:" << std::endl;
            std::cout << "1. 配置文件路径是否正确" << std::endl;
            std::cout << "2. OAX4600服务是否在192.168.7.1和8.1上运行" << std::endl;
            std::cout << "3. 网络连接是否正常" << std::endl;
            std::cout << "4. 依赖库是否正确安装" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试程序异常: " << e.what() << std::endl;
        return 1;
    }
    
    return all_passed ? 0 : 1;
}
