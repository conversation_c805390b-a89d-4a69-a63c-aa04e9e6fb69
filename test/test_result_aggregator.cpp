#include <iostream>
#include <thread>
#include <chrono>
#include <random>
#include "result_aggregator.h"

using namespace tongxing;

// 创建模拟的推理结果
InferenceResult createMockResult(const std::string& model_name, int sequence_id) {
    InferenceResult result;
    
    // 添加一个模拟的tensor
    Tensor* tensor = result.add_tensors();
    tensor->add_shape(1);
    tensor->add_shape(10);  // 假设10个输出
    
    // 填充模拟数据
    for (int i = 0; i < 10; ++i) {
        tensor->add_data(static_cast<float>(sequence_id * 100 + i));
    }
    
    return result;
}

void test_basic_aggregation() {
    std::cout << "\n=== Testing Basic Result Aggregation ===" << std::endl;
    
    ResultAggregator aggregator;
    std::set<std::string> required_models = {"FaceDetection", "FaceKeypoints", "eye"};
    
    if (!aggregator.initialize(required_models, 3000)) {
        std::cerr << "Failed to initialize aggregator" << std::endl;
        return;
    }
    
    // 开始处理第一张图片
    uint64_t seq1 = aggregator.startNewImage();
    std::cout << "Started image 1, sequence_id: " << seq1 << std::endl;
    
    // 添加各个模型的结果
    aggregator.addModelResult(seq1, "FaceDetection", createMockResult("FaceDetection", seq1));
    aggregator.addModelResult(seq1, "FaceKeypoints", createMockResult("FaceKeypoints", seq1));
    aggregator.addModelResult(seq1, "eye", createMockResult("eye", seq1));
    
    // 尝试获取结果
    CombinedInferenceResult combined_result(0);
    if (aggregator.getNextResult(combined_result)) {
        std::cout << "Got combined result for sequence: " << combined_result.sequence_id << std::endl;
        std::cout << "Complete: " << combined_result.is_complete << std::endl;
        std::cout << "Model results count: " << combined_result.model_results.size() << std::endl;
    } else {
        std::cout << "No result available yet" << std::endl;
    }
    
    std::cout << "Pending count: " << aggregator.getPendingCount() << std::endl;
    std::cout << "Completed count: " << aggregator.getCompletedCount() << std::endl;
}

void test_sequential_processing() {
    std::cout << "\n=== Testing Sequential Processing ===" << std::endl;
    
    ResultAggregator aggregator;
    std::set<std::string> required_models = {"FaceDetection", "FaceKeypoints"};
    
    if (!aggregator.initialize(required_models, 2000)) {
        std::cerr << "Failed to initialize aggregator" << std::endl;
        return;
    }
    
    // 开始处理多张图片
    std::vector<uint64_t> sequences;
    for (int i = 0; i < 5; ++i) {
        uint64_t seq = aggregator.startNewImage();
        sequences.push_back(seq);
        std::cout << "Started image " << (i + 1) << ", sequence_id: " << seq << std::endl;
    }
    
    // 模拟乱序返回结果
    std::random_device rd;
    std::mt19937 gen(rd());
    
    // 先添加第3、1、4、2、5张图片的FaceDetection结果
    std::vector<int> order = {2, 0, 3, 1, 4};
    for (int idx : order) {
        uint64_t seq = sequences[idx];
        aggregator.addModelResult(seq, "FaceDetection", createMockResult("FaceDetection", seq));
        std::cout << "Added FaceDetection result for sequence: " << seq << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // 再添加FaceKeypoints结果（不同的顺序）
    order = {1, 3, 0, 4, 2};
    for (int idx : order) {
        uint64_t seq = sequences[idx];
        aggregator.addModelResult(seq, "FaceKeypoints", createMockResult("FaceKeypoints", seq));
        std::cout << "Added FaceKeypoints result for sequence: " << seq << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // 按顺序获取结果
    std::cout << "\nRetrieving results in order:" << std::endl;
    for (int i = 0; i < 5; ++i) {
        CombinedInferenceResult result(0);
        if (aggregator.getNextResult(result)) {
            std::cout << "Got result for sequence: " << result.sequence_id 
                      << " (expected: " << sequences[i] << ")" << std::endl;
            if (result.sequence_id != sequences[i]) {
                std::cerr << "ERROR: Results not in correct order!" << std::endl;
            }
        } else {
            std::cout << "No result available for position " << i << std::endl;
        }
    }
}

void test_timeout_handling() {
    std::cout << "\n=== Testing Timeout Handling ===" << std::endl;
    
    ResultAggregator aggregator;
    std::set<std::string> required_models = {"FaceDetection", "FaceKeypoints", "eye"};
    
    if (!aggregator.initialize(required_models, 1000)) { // 1秒超时
        std::cerr << "Failed to initialize aggregator" << std::endl;
        return;
    }
    
    // 开始处理图片
    uint64_t seq1 = aggregator.startNewImage();
    uint64_t seq2 = aggregator.startNewImage();
    
    // 只为第一张图片添加部分结果
    aggregator.addModelResult(seq1, "FaceDetection", createMockResult("FaceDetection", seq1));
    aggregator.addModelResult(seq1, "FaceKeypoints", createMockResult("FaceKeypoints", seq1));
    // 故意不添加eye结果，让它超时
    
    // 为第二张图片添加完整结果
    aggregator.addModelResult(seq2, "FaceDetection", createMockResult("FaceDetection", seq2));
    aggregator.addModelResult(seq2, "FaceKeypoints", createMockResult("FaceKeypoints", seq2));
    aggregator.addModelResult(seq2, "eye", createMockResult("eye", seq2));
    
    std::cout << "Waiting for timeout..." << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(1500));
    
    // 检查超时
    aggregator.checkTimeouts();
    
    // 尝试获取结果
    CombinedInferenceResult result(0);
    if (aggregator.getNextResult(result)) {
        std::cout << "Got result for sequence: " << result.sequence_id << std::endl;
        std::cout << "Complete: " << result.is_complete << std::endl;
        std::cout << "Has timeout: " << result.has_timeout << std::endl;
        std::cout << "Missing models count: " << result.missing_models.size() << std::endl;
        for (const auto& model : result.missing_models) {
            std::cout << "Missing model: " << model << std::endl;
        }
    }
    
    // 获取第二个结果
    if (aggregator.getNextResult(result)) {
        std::cout << "Got second result for sequence: " << result.sequence_id << std::endl;
        std::cout << "Complete: " << result.is_complete << std::endl;
    }
    
    std::cout << "Timeout count: " << aggregator.getTimeoutCount() << std::endl;
}

void test_high_throughput() {
    std::cout << "\n=== Testing High Throughput Processing ===" << std::endl;
    
    ResultAggregator aggregator;
    std::set<std::string> required_models = {"model1", "model2"};
    
    if (!aggregator.initialize(required_models, 5000)) {
        std::cerr << "Failed to initialize aggregator" << std::endl;
        return;
    }
    
    const int num_images = 20;
    std::vector<uint64_t> sequences;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 快速启动多个图片处理
    for (int i = 0; i < num_images; ++i) {
        uint64_t seq = aggregator.startNewImage();
        sequences.push_back(seq);
    }
    
    // 模拟并发添加结果
    std::thread t1([&]() {
        for (auto seq : sequences) {
            aggregator.addModelResult(seq, "model1", createMockResult("model1", seq));
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    });
    
    std::thread t2([&]() {
        for (auto seq : sequences) {
            aggregator.addModelResult(seq, "model2", createMockResult("model2", seq));
            std::this_thread::sleep_for(std::chrono::milliseconds(15));
        }
    });
    
    // 并发获取结果
    int results_received = 0;
    std::thread t3([&]() {
        while (results_received < num_images) {
            CombinedInferenceResult result(0);
            if (aggregator.getNextResult(result)) {
                results_received++;
                std::cout << "Received result " << results_received << " for sequence: " 
                          << result.sequence_id << std::endl;
            } else {
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }
        }
    });
    
    t1.join();
    t2.join();
    t3.join();
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    std::cout << "High throughput test completed:" << std::endl;
    std::cout << "Processed " << num_images << " images in " << total_time << "ms" << std::endl;
    std::cout << "Throughput: " << (num_images * 1000.0 / total_time) << " images/second" << std::endl;
    std::cout << "Final completed count: " << aggregator.getCompletedCount() << std::endl;
}

int main() {
    std::cout << "Result Aggregator Test Suite" << std::endl;
    std::cout << "============================" << std::endl;
    
    try {
        test_basic_aggregation();
        test_sequential_processing();
        test_timeout_handling();
        test_high_throughput();
        
        std::cout << "\n=== All Tests Completed ===" << std::endl;
        std::cout << "Key features verified:" << std::endl;
        std::cout << "1. Multi-model result aggregation" << std::endl;
        std::cout << "2. Sequential output ordering" << std::endl;
        std::cout << "3. Timeout handling for missing results" << std::endl;
        std::cout << "4. High throughput concurrent processing" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
