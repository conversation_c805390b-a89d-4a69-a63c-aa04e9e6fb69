#include <iostream>
#include <string>
#include <memory>
#include "cc_module.h"
#include "json.h"

using namespace tongxing;

void print_test_header(const std::string& test_name) {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "  " << test_name << std::endl;
    std::cout << std::string(60, '=') << std::endl;
}

bool test_module_registration() {
    print_test_header("Load Balancer Module Registration Test");
    
    try {
        // 测试模块是否正确注册
        std::cout << "测试负载均衡器模块注册..." << std::endl;
        
        // 创建一个简单的配置
        Json::Value config;
        config["service_ips"] = Json::Value(Json::arrayValue);
        config["service_ips"].append("***********");
        config["service_ips"].append("***********");
        config["port"] = 1180;
        config["model_endpoints"] = Json::Value(Json::arrayValue);
        config["model_endpoints"].append("FaceDetection");
        config["model_endpoints"].append("FaceKeypoints");
        config["enforce_sequential_output"] = true;
        config["max_concurrent_per_service"] = 2;
        config["request_timeout_ms"] = 8000;
        config["health_check_interval_ms"] = 30000;
        
        // 尝试创建负载均衡器模块
        Json::Value module_config;
        module_config["class_name"] = "image_level_load_balancer_inference";
        auto module = get_cc_module(module_config);
        if (!module) {
            std::cerr << "❌ 无法创建负载均衡器模块" << std::endl;
            return false;
        }
        
        std::cout << "✅ 负载均衡器模块创建成功" << std::endl;
        
        // 测试初始化
        std::cout << "测试模块初始化..." << std::endl;
        int ret = module->init(config);
        if (ret != 0) {
            std::cerr << "❌ 模块初始化失败，返回码: " << ret << std::endl;
            return false;
        }
        
        std::cout << "✅ 模块初始化成功" << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 模块注册测试异常: " << e.what() << std::endl;
        return false;
    }
}

bool test_configuration_validation() {
    print_test_header("Configuration Validation Test");
    
    try {
        Json::Value module_config;
        module_config["class_name"] = "image_level_load_balancer_inference";
        auto module = get_cc_module(module_config);
        if (!module) {
            std::cerr << "❌ 无法创建模块" << std::endl;
            return false;
        }
        
        // 测试1: 空配置
        std::cout << "测试空配置..." << std::endl;
        Json::Value empty_config;
        int ret = module->init(empty_config);
        if (ret == 0) {
            std::cerr << "❌ 空配置应该失败但却成功了" << std::endl;
            return false;
        }
        std::cout << "✅ 空配置正确拒绝" << std::endl;
        
        // 测试2: 缺少service_ips
        std::cout << "测试缺少service_ips的配置..." << std::endl;
        Json::Value incomplete_config;
        incomplete_config["port"] = 1180;
        ret = module->init(incomplete_config);
        if (ret == 0) {
            std::cerr << "❌ 缺少service_ips的配置应该失败但却成功了" << std::endl;
            return false;
        }
        std::cout << "✅ 缺少service_ips的配置正确拒绝" << std::endl;
        
        // 测试3: 正确配置
        std::cout << "测试正确配置..." << std::endl;
        Json::Value valid_config;
        valid_config["service_ips"] = Json::Value(Json::arrayValue);
        valid_config["service_ips"].append("***********");
        valid_config["service_ips"].append("***********");
        valid_config["port"] = 1180;
        valid_config["model_endpoints"] = Json::Value(Json::arrayValue);
        valid_config["model_endpoints"].append("FaceDetection");
        valid_config["enforce_sequential_output"] = true;
        
        ret = module->init(valid_config);
        if (ret != 0) {
            std::cerr << "❌ 正确配置初始化失败，返回码: " << ret << std::endl;
            return false;
        }
        std::cout << "✅ 正确配置初始化成功" << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 配置验证测试异常: " << e.what() << std::endl;
        return false;
    }
}

bool test_module_interface() {
    print_test_header("Module Interface Test");
    
    try {
        Json::Value module_config;
        module_config["class_name"] = "image_level_load_balancer_inference";
        auto module = get_cc_module(module_config);
        if (!module) {
            std::cerr << "❌ 无法创建模块" << std::endl;
            return false;
        }
        
        // 初始化模块
        Json::Value config;
        config["service_ips"] = Json::Value(Json::arrayValue);
        config["service_ips"].append("***********");
        config["port"] = 1180;
        config["model_endpoints"] = Json::Value(Json::arrayValue);
        config["model_endpoints"].append("FaceDetection");
        config["enforce_sequential_output"] = true;
        
        int ret = module->init(config);
        if (ret != 0) {
            std::cerr << "❌ 模块初始化失败" << std::endl;
            return false;
        }
        
        // 测试接口方法
        std::cout << "测试getOutputNum接口..." << std::endl;
        size_t output_num = module->getOutputNum();
        std::cout << "✅ getOutputNum返回: " << output_num << std::endl;
        
        std::cout << "测试getOutput接口..." << std::endl;
        auto output = module->getOutput(0);
        if (output) {
            std::cout << "✅ getOutput(0)返回了有效指针" << std::endl;
        } else {
            std::cout << "ℹ️  getOutput(0)返回nullptr（预期，因为没有处理数据）" << std::endl;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 模块接口测试异常: " << e.what() << std::endl;
        return false;
    }
}

bool test_existing_modules() {
    print_test_header("Existing Modules Test");
    
    try {
        // 测试现有的HTTP推理模块是否仍然工作
        std::cout << "测试现有oax4600_http_inference模块..." << std::endl;
        Json::Value http_config;
        http_config["class_name"] = "oax4600_http_inference";
        auto http_module = get_cc_module(http_config);
        if (!http_module) {
            std::cerr << "❌ 无法创建oax4600_http_inference模块" << std::endl;
            return false;
        }
        std::cout << "✅ oax4600_http_inference模块创建成功" << std::endl;
        
        // 测试其他常见模块
        std::vector<std::string> test_modules = {
            "cc_resize2d",
            "cc_normalization",
            "cc_face_bbox_decoder"
        };
        
        for (const auto& module_name : test_modules) {
            std::cout << "测试模块: " << module_name << std::endl;
            auto test_module = get_cc_module(module_name);
            if (!test_module) {
                std::cerr << "❌ 无法创建模块: " << module_name << std::endl;
                return false;
            }
            std::cout << "✅ 模块 " << module_name << " 创建成功" << std::endl;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 现有模块测试异常: " << e.what() << std::endl;
        return false;
    }
}

int main() {
    std::cout << "Load Balancer Integration Test" << std::endl;
    std::cout << "验证负载均衡器模块是否正确集成到DMS框架中" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    
    bool all_passed = true;
    
    try {
        // 1. 模块注册测试
        if (!test_module_registration()) {
            all_passed = false;
        }
        
        // 2. 配置验证测试
        if (!test_configuration_validation()) {
            all_passed = false;
        }
        
        // 3. 模块接口测试
        if (!test_module_interface()) {
            all_passed = false;
        }
        
        // 4. 现有模块测试
        if (!test_existing_modules()) {
            all_passed = false;
        }
        
        // 最终结果
        print_test_header("测试结果总结");
        
        if (all_passed) {
            std::cout << "🎉 所有集成测试通过!" << std::endl;
            std::cout << "\n验证的功能:" << std::endl;
            std::cout << "✅ 负载均衡器模块正确注册到系统" << std::endl;
            std::cout << "✅ 模块配置验证机制正常工作" << std::endl;
            std::cout << "✅ 模块接口符合CcModule规范" << std::endl;
            std::cout << "✅ 现有模块未受影响" << std::endl;
            std::cout << "✅ 与现有DMS框架完全兼容" << std::endl;
            
            std::cout << "\n下一步:" << std::endl;
            std::cout << "1. 在配置文件中使用 'image_level_load_balancer_inference' 作为class_name" << std::endl;
            std::cout << "2. 确保OAX4600服务在***********和***********上运行" << std::endl;
            std::cout << "3. 使用现有的DMS测试工具进行端到端测试" << std::endl;
        } else {
            std::cout << "❌ 部分集成测试失败!" << std::endl;
            std::cout << "请检查编译日志和错误信息" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试程序异常: " << e.what() << std::endl;
        return 1;
    }
    
    return all_passed ? 0 : 1;
}
