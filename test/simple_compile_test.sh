#!/bin/bash

echo "=========================================="
echo "  Simple Load Balancer Test Compiler"
echo "=========================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查编译器
if ! command -v g++ &> /dev/null; then
    echo -e "${RED}错误: g++ 编译器未找到${NC}"
    exit 1
fi

echo -e "${GREEN}找到 g++ 编译器${NC}"

# 设置编译参数
COMPILE_FLAGS="-std=c++17 -Wall -Wextra -O2"
INCLUDE_DIRS="-I../tongxing_util/src/module/inference -I../tongxing_util/src -I../thirdparty/protobuf/include -I../thirdparty/curl/include"
LINK_LIBS="-lcurl -lprotobuf -ljsoncpp -lpthread"

echo -e "${YELLOW}开始编译...${NC}"

# 直接编译单个测试文件
echo "编译简单测试程序..."
g++ $COMPILE_FLAGS $INCLUDE_DIRS \
    ../tongxing_util/src/module/inference/image_level_load_balancer.cpp \
    test_two_service_load_balancer.cpp \
    $LINK_LIBS \
    -o test_simple_load_balancer

if [ $? -eq 0 ]; then
    echo -e "${GREEN}编译成功！${NC}"
    
    # 检查服务可用性
    echo -e "${YELLOW}检查服务可用性...${NC}"
    
    check_service() {
        local ip=$1
        local port=$2
        echo "检查服务 $ip:$port..."
        
        if timeout 3 curl -s "http://$ip:$port/" > /dev/null 2>&1; then
            echo -e "${GREEN}✓ 服务 $ip:$port 可用${NC}"
            return 0
        else
            echo -e "${RED}✗ 服务 $ip:$port 不可用${NC}"
            return 1
        fi
    }
    
    SERVICE_7_1_OK=false
    SERVICE_8_1_OK=false
    
    if check_service "***********" "1180"; then
        SERVICE_7_1_OK=true
    fi
    
    if check_service "***********" "1180"; then
        SERVICE_8_1_OK=true
    fi
    
    # 运行测试
    if [ "$SERVICE_7_1_OK" = true ] && [ "$SERVICE_8_1_OK" = true ]; then
        echo -e "${GREEN}两个服务都可用，运行完整测试${NC}"
        echo "=========================================="
        echo "  开始负载均衡器测试"
        echo "=========================================="
        ./test_simple_load_balancer
        TEST_RESULT=$?
        
        if [ $TEST_RESULT -eq 0 ]; then
            echo -e "${GREEN}🎉 测试通过！${NC}"
        else
            echo -e "${RED}❌ 测试失败${NC}"
        fi
        
    elif [ "$SERVICE_7_1_OK" = true ] || [ "$SERVICE_8_1_OK" = true ]; then
        echo -e "${YELLOW}只有一个服务可用，负载均衡测试将受限${NC}"
        echo "仍然运行测试..."
        ./test_simple_load_balancer
        TEST_RESULT=$?
        
    else
        echo -e "${RED}没有可用的服务实例${NC}"
        echo "请确保至少有一个OAX4600服务在以下地址运行："
        echo "  - ***********:1180"
        echo "  - ***********:1180"
        echo ""
        echo "可执行文件已生成: test_simple_load_balancer"
        echo "服务启动后可手动运行测试"
        exit 1
    fi
    
else
    echo -e "${RED}编译失败${NC}"
    echo "可能的原因："
    echo "1. 缺少必要的开发库 (libcurl-dev, libprotobuf-dev, libjsoncpp-dev)"
    echo "2. 头文件路径不正确"
    echo "3. 依赖库未正确安装"
    exit 1
fi

exit $TEST_RESULT
