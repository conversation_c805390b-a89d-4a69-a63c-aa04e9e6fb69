#include <iostream>
#include <vector>
#include <chrono>
#include <thread>
#include <fstream>
#include "image_level_load_balancer_inference.h"
#include "cc_numarray_tool.h"
#include "json.h"

using namespace tongxing;

// 创建测试图片数据
std::shared_ptr<NumArray> create_test_image(int image_id, int width = 224, int height = 224) {
    auto array = creat_numarray({1, 3, height, width}, NumArray::DataType::UINT8);
    
    // 填充测试数据
    size_t total_size = 1 * 3 * height * width;
    for (size_t i = 0; i < total_size; ++i) {
        array->data[i] = static_cast<unsigned char>((image_id * 37 + i) % 256);
    }
    
    return array;
}

// 加载配置文件
Json::Value loadConfig(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Cannot open config file: " << config_file << std::endl;
        return Json::Value();
    }
    
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errs;
    
    if (!Json::parseFromStream(builder, file, &root, &errs)) {
        std::cerr << "JSON parse error: " << errs << std::endl;
        return Json::Value();
    }
    
    return root;
}

void print_test_header(const std::string& test_name) {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "  " << test_name << std::endl;
    std::cout << std::string(60, '=') << std::endl;
}

bool test_basic_inference() {
    print_test_header("Basic Load Balancer Inference Test");
    
    // 创建推理实例
    ImageLevelLoadBalancerInference inference;
    
    // 创建配置
    Json::Value config;
    config["type"] = "load_balanced";
    config["service_ips"] = Json::Value(Json::arrayValue);
    config["service_ips"].append("***********");
    config["service_ips"].append("***********");
    config["port"] = 1180;
    config["model_endpoints"] = Json::Value(Json::arrayValue);
    config["model_endpoints"].append("FaceDetection");
    config["model_endpoints"].append("FaceKeypoints");
    config["model_endpoints"].append("eye");
    config["model_endpoints"].append("Dms_PhoneSmoking");
    config["enforce_sequential_output"] = true;
    config["max_concurrent_per_service"] = 2;
    config["request_timeout_ms"] = 10000;
    
    // 初始化
    if (inference.init(config) != 0) {
        std::cerr << "❌ Failed to initialize inference" << std::endl;
        return false;
    }
    
    std::cout << "✅ Inference initialized successfully" << std::endl;
    
    // 测试单张图片推理
    std::vector<std::shared_ptr<NumArray>> input = {create_test_image(1)};
    std::vector<std::shared_ptr<NumArray>> outputs;
    
    std::cout << "📤 Processing single image..." << std::endl;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    int result = inference.inference(input, outputs);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto processing_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    if (result == 0) {
        std::cout << "✅ Single image processed successfully in " << processing_time << "ms" << std::endl;
        std::cout << "📊 Output tensors: " << outputs.size() << std::endl;
        
        // 显示输出tensor信息
        for (size_t i = 0; i < outputs.size(); ++i) {
            auto& output = outputs[i];
            std::cout << "  Tensor " << i << ": shape [";
            for (size_t j = 0; j < output->shape.size(); ++j) {
                std::cout << output->shape[j];
                if (j < output->shape.size() - 1) std::cout << ", ";
            }
            std::cout << "]" << std::endl;
        }
    } else {
        std::cerr << "❌ Single image processing failed" << std::endl;
        return false;
    }
    
    // 显示负载均衡器状态
    auto lb_status = inference.getLoadBalancerStatus();
    std::cout << "\n📊 Load Balancer Status:" << std::endl;
    std::cout << "  Pending: " << lb_status["pending_requests"].asInt() << std::endl;
    std::cout << "  Completed: " << lb_status["completed_requests"].asInt() << std::endl;
    
    // 清理
    inference.deinit();
    
    std::cout << "✅ Basic inference test PASSED" << std::endl;
    return true;
}

bool test_multiple_images() {
    print_test_header("Multiple Images Sequential Processing Test");
    
    ImageLevelLoadBalancerInference inference;
    
    // 配置
    Json::Value config;
    config["type"] = "load_balanced";
    config["service_ips"] = Json::Value(Json::arrayValue);
    config["service_ips"].append("***********");
    config["service_ips"].append("***********");
    config["port"] = 1180;
    config["model_endpoints"] = Json::Value(Json::arrayValue);
    config["model_endpoints"].append("FaceDetection");
    config["model_endpoints"].append("FaceKeypoints");
    config["model_endpoints"].append("eye");
    config["model_endpoints"].append("Dms_PhoneSmoking");
    config["enforce_sequential_output"] = true;
    config["request_timeout_ms"] = 12000;
    
    if (inference.init(config) != 0) {
        std::cerr << "❌ Failed to initialize inference" << std::endl;
        return false;
    }
    
    // 处理多张图片
    const int num_images = 5;
    std::vector<double> processing_times;
    
    std::cout << "📤 Processing " << num_images << " images sequentially..." << std::endl;
    
    auto overall_start = std::chrono::high_resolution_clock::now();
    
    for (int i = 1; i <= num_images; ++i) {
        std::vector<std::shared_ptr<NumArray>> input = {create_test_image(i)};
        std::vector<std::shared_ptr<NumArray>> outputs;
        
        auto start_time = std::chrono::high_resolution_clock::now();
        int result = inference.inference(input, outputs);
        auto end_time = std::chrono::high_resolution_clock::now();
        
        auto processing_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
        processing_times.push_back(processing_time);
        
        if (result == 0) {
            std::cout << "✅ Image " << i << " processed in " << processing_time 
                      << "ms (" << outputs.size() << " outputs)" << std::endl;
        } else {
            std::cerr << "❌ Image " << i << " processing failed" << std::endl;
            return false;
        }
        
        // 短暂间隔
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    auto overall_end = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration<double, std::milli>(overall_end - overall_start).count();
    
    // 分析性能
    double avg_time = std::accumulate(processing_times.begin(), processing_times.end(), 0.0) / processing_times.size();
    double min_time = *std::min_element(processing_times.begin(), processing_times.end());
    double max_time = *std::max_element(processing_times.begin(), processing_times.end());
    
    std::cout << "\n📊 Performance Analysis:" << std::endl;
    std::cout << "  Total time: " << total_time << "ms" << std::endl;
    std::cout << "  Average processing time: " << avg_time << "ms" << std::endl;
    std::cout << "  Min processing time: " << min_time << "ms" << std::endl;
    std::cout << "  Max processing time: " << max_time << "ms" << std::endl;
    std::cout << "  Throughput: " << (num_images * 1000.0 / total_time) << " images/second" << std::endl;
    
    // 显示最终统计
    auto perf_stats = inference.getPerformanceStats();
    std::cout << "\n📊 Final Performance Stats:" << std::endl;
    std::cout << "  Success rate: " << perf_stats["success_rate"].asDouble() << "%" << std::endl;
    std::cout << "  Throughput: " << perf_stats["throughput_rps"].asDouble() << " requests/second" << std::endl;
    
    auto lb_status = inference.getLoadBalancerStatus();
    std::cout << "\n📊 Load Balancer Final Status:" << std::endl;
    std::cout << "  Total completed: " << lb_status["completed_requests"].asInt() << std::endl;
    std::cout << "  Average time: " << lb_status["average_processing_time_ms"].asDouble() << "ms" << std::endl;
    
    // 显示服务使用情况
    const auto& services = lb_status["services"];
    for (const auto& service : services) {
        std::cout << "  Service " << service["ip"].asString() 
                  << " - Available: " << (service["available"].asBool() ? "YES" : "NO")
                  << ", Avg Time: " << service["avg_processing_time_ms"].asDouble() << "ms" << std::endl;
    }
    
    inference.deinit();
    
    std::cout << "✅ Multiple images test PASSED" << std::endl;
    return true;
}

bool test_with_config_file() {
    print_test_header("Config File Based Test");
    
    // 尝试加载配置文件
    Json::Value root = loadConfig("resourc/oax4600_http/dms_config_load_balancer.json");
    if (root.isNull()) {
        std::cout << "⚠️  Config file not found, skipping this test" << std::endl;
        return true;
    }
    
    // 提取负载均衡器配置
    const Json::Value& lb_config = root["config"]["load_balancer_config"];
    if (lb_config.isNull()) {
        std::cerr << "❌ No load_balancer_config found in config file" << std::endl;
        return false;
    }
    
    ImageLevelLoadBalancerInference inference;
    
    if (inference.init(lb_config) != 0) {
        std::cerr << "❌ Failed to initialize with config file" << std::endl;
        return false;
    }
    
    std::cout << "✅ Initialized with config file successfully" << std::endl;
    
    // 测试一张图片
    std::vector<std::shared_ptr<NumArray>> input = {create_test_image(99)};
    std::vector<std::shared_ptr<NumArray>> outputs;
    
    int result = inference.inference(input, outputs);
    
    if (result == 0) {
        std::cout << "✅ Config file based processing successful" << std::endl;
    } else {
        std::cerr << "❌ Config file based processing failed" << std::endl;
        return false;
    }
    
    inference.deinit();
    
    std::cout << "✅ Config file test PASSED" << std::endl;
    return true;
}

int main() {
    std::cout << "Load Balancer Inference Test Suite" << std::endl;
    std::cout << "Testing ImageLevelLoadBalancerInference class" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    
    bool all_passed = true;
    
    try {
        if (!test_basic_inference()) {
            all_passed = false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        if (!test_multiple_images()) {
            all_passed = false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        if (!test_with_config_file()) {
            all_passed = false;
        }
        
        // 最终结果
        print_test_header("Test Results Summary");
        
        if (all_passed) {
            std::cout << "🎉 ALL TESTS PASSED!" << std::endl;
            std::cout << "\nKey features verified:" << std::endl;
            std::cout << "✅ Load balancer inference integration" << std::endl;
            std::cout << "✅ Multiple images sequential processing" << std::endl;
            std::cout << "✅ Configuration file support" << std::endl;
            std::cout << "✅ Performance monitoring and statistics" << std::endl;
            std::cout << "✅ Proper resource management" << std::endl;
        } else {
            std::cout << "❌ SOME TESTS FAILED!" << std::endl;
            std::cout << "Please check the error messages above." << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return all_passed ? 0 : 1;
}
