#include <iostream>
#include <vector>
#include <thread>
#include <chrono>
#include "image_level_load_balancer.h"
#include "cc_numarray_tool.h"

using namespace tongxing;

// 创建模拟的图片数据
std::shared_ptr<NumArray> create_test_image(int sequence_id) {
    auto array = creat_numarray({1, 3, 224, 224}, NumArray::DataType::UINT8);
    
    // 填充测试数据，使用sequence_id作为特征
    size_t total_size = 1 * 3 * 224 * 224;
    for (size_t i = 0; i < total_size; ++i) {
        array->data[i] = static_cast<unsigned char>((sequence_id * 100 + i) % 256);
    }
    
    return array;
}

void test_basic_load_balancing() {
    std::cout << "\n=== Testing Basic Load Balancing ===" << std::endl;
    
    ImageLevelLoadBalancer balancer;
    
    // 初始化多个服务实例
    std::vector<std::string> service_ips = {
        "***********", "***********", "***********"
    };
    
    std::vector<std::string> model_endpoints = {
        "FaceDetection", "FaceKeypoints", "eye"
    };
    
    if (!balancer.initialize(service_ips, 1180, model_endpoints)) {
        std::cerr << "Failed to initialize load balancer" << std::endl;
        return;
    }
    
    // 提交几个图片处理请求
    std::vector<uint64_t> sequence_ids;
    for (int i = 0; i < 5; ++i) {
        std::vector<std::shared_ptr<NumArray>> input = {create_test_image(i + 1)};
        uint64_t seq_id = balancer.submitImageProcessing(input);
        
        if (seq_id > 0) {
            sequence_ids.push_back(seq_id);
            std::cout << "Submitted image " << (i + 1) << ", sequence_id: " << seq_id << std::endl;
        } else {
            std::cout << "Failed to submit image " << (i + 1) << std::endl;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    // 等待并获取结果
    std::cout << "\nWaiting for results..." << std::endl;
    int results_received = 0;
    auto start_time = std::chrono::steady_clock::now();
    
    while (results_received < sequence_ids.size()) {
        ImageProcessingResult result(0);
        if (balancer.getNextResult(result)) {
            results_received++;
            std::cout << "Received result " << results_received 
                      << " for sequence: " << result.sequence_id
                      << " from service: " << result.service_ip
                      << " (processing time: " << result.total_processing_time_ms << "ms)"
                      << " (complete: " << result.is_complete << ")" << std::endl;
            
            // 显示模型结果数量
            std::cout << "  Model results: " << result.model_results.size() << std::endl;
            for (const auto& pair : result.model_results) {
                std::cout << "    " << pair.first << ": " 
                          << pair.second.tensors_size() << " tensors" << std::endl;
            }
        } else {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            // 检查超时
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - start_time).count();
            if (elapsed > 30) {
                std::cout << "Timeout waiting for results" << std::endl;
                break;
            }
        }
    }
    
    // 显示负载均衡状态
    auto status = balancer.getLoadBalancerStatus();
    std::cout << "\nLoad Balancer Status:" << std::endl;
    std::cout << "Pending: " << status["pending_requests"].asInt() << std::endl;
    std::cout << "Completed: " << status["completed_requests"].asInt() << std::endl;
    std::cout << "Average time: " << status["average_processing_time_ms"].asDouble() << "ms" << std::endl;
}

void test_sequential_output() {
    std::cout << "\n=== Testing Sequential Output ===" << std::endl;
    
    ImageLevelLoadBalancer balancer;
    
    std::vector<std::string> service_ips = {
        "***********", "***********"
    };
    
    if (!balancer.initialize(service_ips)) {
        std::cerr << "Failed to initialize load balancer" << std::endl;
        return;
    }
    
    // 快速提交多个请求
    std::vector<uint64_t> expected_sequence;
    const int num_images = 8;
    
    std::cout << "Submitting " << num_images << " images quickly..." << std::endl;
    for (int i = 0; i < num_images; ++i) {
        std::vector<std::shared_ptr<NumArray>> input = {create_test_image(i + 1)};
        uint64_t seq_id = balancer.submitImageProcessing(input);
        expected_sequence.push_back(seq_id);
        std::cout << "Submitted image " << (i + 1) << ", sequence_id: " << seq_id << std::endl;
    }
    
    // 验证结果按序返回
    std::cout << "\nVerifying sequential output..." << std::endl;
    for (int i = 0; i < num_images; ++i) {
        ImageProcessingResult result(0);
        bool got_result = false;
        
        // 等待期望的序列结果
        for (int retry = 0; retry < 100; ++retry) {
            if (balancer.getNextResult(result)) {
                got_result = true;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        if (got_result) {
            std::cout << "Result " << (i + 1) << ": sequence_id=" << result.sequence_id 
                      << " (expected=" << expected_sequence[i] << ")";
            
            if (result.sequence_id == expected_sequence[i]) {
                std::cout << " ✓ CORRECT ORDER" << std::endl;
            } else {
                std::cout << " ✗ WRONG ORDER!" << std::endl;
            }
        } else {
            std::cout << "Failed to get result " << (i + 1) << std::endl;
        }
    }
}

void test_load_distribution() {
    std::cout << "\n=== Testing Load Distribution ===" << std::endl;
    
    ImageLevelLoadBalancer balancer;
    
    std::vector<std::string> service_ips = {
        "***********", "***********", "***********", "************"
    };
    
    if (!balancer.initialize(service_ips)) {
        std::cerr << "Failed to initialize load balancer" << std::endl;
        return;
    }
    
    // 提交大量请求测试负载分布
    const int num_requests = 20;
    std::map<std::string, int> service_usage;
    
    std::cout << "Submitting " << num_requests << " requests..." << std::endl;
    
    // 快速提交请求
    std::vector<uint64_t> sequences;
    for (int i = 0; i < num_requests; ++i) {
        std::vector<std::shared_ptr<NumArray>> input = {create_test_image(i + 1)};
        uint64_t seq_id = balancer.submitImageProcessing(input);
        sequences.push_back(seq_id);
    }
    
    // 收集结果并统计服务使用情况
    int results_received = 0;
    while (results_received < num_requests) {
        ImageProcessingResult result(0);
        if (balancer.getNextResult(result)) {
            results_received++;
            service_usage[result.service_ip]++;
            std::cout << "Result " << results_received << " from " << result.service_ip << std::endl;
        } else {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
    
    // 显示负载分布
    std::cout << "\nLoad Distribution Results:" << std::endl;
    for (const auto& pair : service_usage) {
        double percentage = (pair.second * 100.0) / num_requests;
        std::cout << "Service " << pair.first << ": " << pair.second 
                  << " requests (" << percentage << "%)" << std::endl;
    }
    
    // 检查分布是否相对均匀
    double expected_per_service = num_requests / static_cast<double>(service_ips.size());
    bool balanced = true;
    for (const auto& pair : service_usage) {
        double deviation = std::abs(pair.second - expected_per_service) / expected_per_service;
        if (deviation > 0.5) { // 允许50%的偏差
            balanced = false;
            break;
        }
    }
    
    std::cout << "Load balancing: " << (balanced ? "GOOD" : "NEEDS IMPROVEMENT") << std::endl;
}

void test_performance_metrics() {
    std::cout << "\n=== Testing Performance Metrics ===" << std::endl;
    
    ImageLevelLoadBalancer balancer;
    
    std::vector<std::string> service_ips = {"************"};
    
    if (!balancer.initialize(service_ips)) {
        std::cerr << "Failed to initialize load balancer" << std::endl;
        return;
    }
    
    const int num_requests = 5;
    auto overall_start = std::chrono::high_resolution_clock::now();
    
    // 提交请求
    for (int i = 0; i < num_requests; ++i) {
        std::vector<std::shared_ptr<NumArray>> input = {create_test_image(i + 1)};
        balancer.submitImageProcessing(input);
    }
    
    // 收集结果和性能指标
    std::vector<double> processing_times;
    for (int i = 0; i < num_requests; ++i) {
        ImageProcessingResult result(0);
        bool got_result = false;
        
        for (int retry = 0; retry < 100; ++retry) {
            if (balancer.getNextResult(result)) {
                got_result = true;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        if (got_result) {
            processing_times.push_back(result.total_processing_time_ms);
            std::cout << "Request " << (i + 1) << " processing time: " 
                      << result.total_processing_time_ms << "ms" << std::endl;
        }
    }
    
    auto overall_end = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration<double, std::milli>(overall_end - overall_start).count();
    
    // 计算统计信息
    if (!processing_times.empty()) {
        double avg_time = std::accumulate(processing_times.begin(), processing_times.end(), 0.0) / processing_times.size();
        double min_time = *std::min_element(processing_times.begin(), processing_times.end());
        double max_time = *std::max_element(processing_times.begin(), processing_times.end());
        
        std::cout << "\nPerformance Summary:" << std::endl;
        std::cout << "Total requests: " << num_requests << std::endl;
        std::cout << "Total time: " << total_time << "ms" << std::endl;
        std::cout << "Average processing time: " << avg_time << "ms" << std::endl;
        std::cout << "Min processing time: " << min_time << "ms" << std::endl;
        std::cout << "Max processing time: " << max_time << "ms" << std::endl;
        std::cout << "Throughput: " << (num_requests * 1000.0 / total_time) << " images/second" << std::endl;
    }
}

int main() {
    std::cout << "Image Level Load Balancer Test Suite" << std::endl;
    std::cout << "=====================================" << std::endl;
    
    try {
        test_basic_load_balancing();
        test_sequential_output();
        test_load_distribution();
        test_performance_metrics();
        
        std::cout << "\n=== All Tests Completed ===" << std::endl;
        std::cout << "Key features verified:" << std::endl;
        std::cout << "1. Image-level load balancing across multiple services" << std::endl;
        std::cout << "2. All models for one image processed on same service" << std::endl;
        std::cout << "3. Sequential output ordering maintained" << std::endl;
        std::cout << "4. Load distribution and performance monitoring" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
