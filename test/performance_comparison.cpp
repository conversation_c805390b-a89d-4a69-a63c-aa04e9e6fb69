#include <iostream>
#include <vector>
#include <chrono>
#include <future>
#include "model_service_pool.h"
#include "tx_oax4600_http_inference.h"

using namespace tongxing;

// 模拟图片数据
std::vector<unsigned char> create_test_image_data(size_t size = 1024 * 100) { // 100KB
    std::vector<unsigned char> data(size);
    for (size_t i = 0; i < size; ++i) {
        data[i] = static_cast<unsigned char>(i % 256);
    }
    return data;
}

void test_legacy_mode_performance() {
    std::cout << "\n=== Testing Legacy Mode Performance ===" << std::endl;
    
    // 创建传统模式的FileUploader
    FileUploader uploader("http://192.168.11.1:1180/FaceKeypoints", "http://192.168.11.1:1180/tar");
    
    const int num_requests = 10;
    auto test_data = create_test_image_data();
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_requests; ++i) {
        // 模拟NumArray输入
        std::vector<std::shared_ptr<NumArray>> inputs;
        std::vector<std::shared_ptr<NumArray>> outputs;
        
        // 创建模拟的NumArray
        auto input = creat_numarray({1, 3, 224, 224}, NumArray::DataType::UINT8);
        memcpy(input->data, test_data.data(), std::min(test_data.size(), (size_t)(1 * 3 * 224 * 224)));
        inputs.push_back(input);
        
        auto request_start = std::chrono::high_resolution_clock::now();
        uploader.upload_image_data(inputs, outputs);
        auto request_end = std::chrono::high_resolution_clock::now();
        
        auto request_time = std::chrono::duration<double, std::milli>(request_end - request_start).count();
        std::cout << "Legacy request " << (i + 1) << ": " << request_time << "ms" << std::endl;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    std::cout << "Legacy mode results:" << std::endl;
    std::cout << "Total time: " << total_time << "ms" << std::endl;
    std::cout << "Average time per request: " << (total_time / num_requests) << "ms" << std::endl;
    std::cout << "Throughput: " << (num_requests * 1000.0 / total_time) << " requests/second" << std::endl;
}

void test_service_pool_performance() {
    std::cout << "\n=== Testing Service Pool Performance ===" << std::endl;
    
    ModelServicePool pool;
    if (!pool.initialize("ip_pool.json")) {
        std::cerr << "Failed to initialize service pool" << std::endl;
        return;
    }
    
    const int num_requests = 10;
    auto test_data = create_test_image_data();
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 并发发送请求
    std::vector<std::future<RequestResult>> futures;
    for (int i = 0; i < num_requests; ++i) {
        futures.push_back(pool.sendAsyncRequest("FaceKeypoints", test_data, "image/jpeg"));
    }
    
    // 等待所有请求完成
    for (int i = 0; i < num_requests; ++i) {
        auto result = futures[i].get();
        std::cout << "Pool request " << (i + 1) << ": " 
                  << (result.success ? "SUCCESS" : "FAILED") 
                  << " (" << result.response_time_ms << "ms)" << std::endl;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    std::cout << "Service pool results:" << std::endl;
    std::cout << "Total time: " << total_time << "ms" << std::endl;
    std::cout << "Average time per request: " << (total_time / num_requests) << "ms" << std::endl;
    std::cout << "Throughput: " << (num_requests * 1000.0 / total_time) << " requests/second" << std::endl;
}

void test_mixed_workload() {
    std::cout << "\n=== Testing Mixed Workload Performance ===" << std::endl;
    
    ModelServicePool pool;
    if (!pool.initialize("ip_pool.json")) {
        std::cerr << "Failed to initialize service pool" << std::endl;
        return;
    }
    
    const int num_batches = 5;
    const int requests_per_batch = 7; // 匹配服务池大小
    auto test_data = create_test_image_data();
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int batch = 0; batch < num_batches; ++batch) {
        std::cout << "Processing batch " << (batch + 1) << "..." << std::endl;
        
        std::vector<std::future<RequestResult>> futures;
        auto batch_start = std::chrono::high_resolution_clock::now();
        
        // 发送一批并发请求
        for (int i = 0; i < requests_per_batch; ++i) {
            futures.push_back(pool.sendAsyncRequest("FaceKeypoints", test_data, "image/jpeg"));
        }
        
        // 等待批次完成
        int success_count = 0;
        for (auto& future : futures) {
            auto result = future.get();
            if (result.success) success_count++;
        }
        
        auto batch_end = std::chrono::high_resolution_clock::now();
        auto batch_time = std::chrono::duration<double, std::milli>(batch_end - batch_start).count();
        
        std::cout << "Batch " << (batch + 1) << " completed: " 
                  << success_count << "/" << requests_per_batch 
                  << " successful in " << batch_time << "ms" << std::endl;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    int total_requests = num_batches * requests_per_batch;
    
    std::cout << "Mixed workload results:" << std::endl;
    std::cout << "Total requests: " << total_requests << std::endl;
    std::cout << "Total time: " << total_time << "ms" << std::endl;
    std::cout << "Average time per request: " << (total_time / total_requests) << "ms" << std::endl;
    std::cout << "Throughput: " << (total_requests * 1000.0 / total_time) << " requests/second" << std::endl;
}

int main() {
    std::cout << "Model Service Performance Comparison" << std::endl;
    std::cout << "====================================" << std::endl;
    
    try {
        test_legacy_mode_performance();
        test_service_pool_performance();
        test_mixed_workload();
        
        std::cout << "\n=== Performance Comparison Completed ===" << std::endl;
        std::cout << "Note: Service pool should show better throughput for concurrent requests" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Performance test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
