#include <iostream>
#include <vector>
#include <chrono>
#include <thread>
#include "async_image_sender.h"
#include "cc_numarray_tool.h"

using namespace tongxing;

// 创建模拟的NumArray数据
std::shared_ptr<NumArray> create_test_numarray(int batch, int channel, int height, int width) {
    auto array = creat_numarray({batch, channel, height, width}, NumArray::DataType::UINT8);
    
    // 填充测试数据
    size_t total_size = batch * channel * height * width;
    for (size_t i = 0; i < total_size; ++i) {
        array->data[i] = static_cast<unsigned char>(i % 256);
    }
    
    return array;
}

void test_basic_async_sending() {
    std::cout << "\n=== Testing Basic Async Sending ===" << std::endl;
    
    AsyncImageSender sender;
    if (!sender.initialize(50, 1)) {
        std::cerr << "Failed to initialize async sender" << std::endl;
        return;
    }
    
    // 创建测试数据
    std::vector<std::shared_ptr<NumArray>> inputs;
    inputs.push_back(create_test_numarray(1, 3, 224, 224));
    
    std::string test_url = "http://192.168.11.1:1180/FaceKeypoints";
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 发送多个异步请求
    const int num_requests = 10;
    for (int i = 0; i < num_requests; ++i) {
        bool success = sender.sendImageAsync(inputs, test_url);
        std::cout << "Request " << (i + 1) << " queued: " << (success ? "SUCCESS" : "FAILED") << std::endl;
        
        // 模拟x86端的其他处理工作
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    std::cout << "All requests queued in: " << total_time << "ms" << std::endl;
    std::cout << "Average queuing time: " << (total_time / num_requests) << "ms per request" << std::endl;
    
    // 等待一段时间让后台处理完成
    std::cout << "Waiting for background processing..." << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // 显示统计信息
    std::cout << "Final statistics:" << std::endl;
    std::cout << "Queue size: " << sender.getQueueSize() << std::endl;
    std::cout << "Processed: " << sender.getProcessedCount() << std::endl;
    std::cout << "Failed: " << sender.getFailedCount() << std::endl;
    std::cout << "Average send time: " << sender.getAverageSendTime() << "ms" << std::endl;
    
    sender.shutdown();
}

void test_high_throughput_sending() {
    std::cout << "\n=== Testing High Throughput Sending ===" << std::endl;
    
    AsyncImageSender sender;
    if (!sender.initialize(200, 3)) { // 更大的队列，3个发送线程
        std::cerr << "Failed to initialize async sender" << std::endl;
        return;
    }
    
    std::vector<std::shared_ptr<NumArray>> inputs;
    inputs.push_back(create_test_numarray(1, 3, 224, 224));
    
    std::string test_url = "http://192.168.11.1:1180/FaceKeypoints";
    
    const int num_requests = 50;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 快速发送大量请求
    int queued_count = 0;
    for (int i = 0; i < num_requests; ++i) {
        if (sender.sendImageAsync(inputs, test_url)) {
            queued_count++;
        }
        
        // 模拟高频率的图片处理
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    std::cout << "High throughput test results:" << std::endl;
    std::cout << "Requests queued: " << queued_count << "/" << num_requests << std::endl;
    std::cout << "Total queuing time: " << total_time << "ms" << std::endl;
    std::cout << "Throughput: " << (queued_count * 1000.0 / total_time) << " requests/second" << std::endl;
    
    // 等待处理完成
    std::cout << "Waiting for processing to complete..." << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(10));
    
    std::cout << "Final statistics:" << std::endl;
    std::cout << "Queue size: " << sender.getQueueSize() << std::endl;
    std::cout << "Processed: " << sender.getProcessedCount() << std::endl;
    std::cout << "Failed: " << sender.getFailedCount() << std::endl;
    std::cout << "Average send time: " << sender.getAverageSendTime() << "ms" << std::endl;
    
    sender.shutdown();
}

void test_callback_functionality() {
    std::cout << "\n=== Testing Callback Functionality ===" << std::endl;
    
    AsyncImageSender sender;
    if (!sender.initialize(20, 1)) {
        std::cerr << "Failed to initialize async sender" << std::endl;
        return;
    }
    
    std::vector<std::shared_ptr<NumArray>> inputs;
    inputs.push_back(create_test_numarray(1, 3, 224, 224));
    
    std::string test_url = "http://192.168.11.1:1180/FaceKeypoints";
    
    std::atomic<int> callback_count(0);
    std::atomic<int> success_count(0);
    
    // 发送带回调的请求
    const int num_requests = 5;
    for (int i = 0; i < num_requests; ++i) {
        auto callback = [&callback_count, &success_count, i](bool success, const std::string& response) {
            callback_count.fetch_add(1);
            if (success) {
                success_count.fetch_add(1);
                std::cout << "Callback " << i << ": SUCCESS (response size: " << response.size() << ")" << std::endl;
            } else {
                std::cout << "Callback " << i << ": FAILED" << std::endl;
            }
        };
        
        bool queued = sender.sendImageAsyncWithCallback(inputs, test_url, callback);
        std::cout << "Request " << i << " with callback queued: " << (queued ? "SUCCESS" : "FAILED") << std::endl;
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // 等待回调完成
    std::cout << "Waiting for callbacks..." << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    std::cout << "Callback test results:" << std::endl;
    std::cout << "Callbacks received: " << callback_count.load() << "/" << num_requests << std::endl;
    std::cout << "Successful callbacks: " << success_count.load() << std::endl;
    
    sender.shutdown();
}

void test_non_blocking_behavior() {
    std::cout << "\n=== Testing Non-blocking Behavior ===" << std::endl;
    
    AsyncImageSender sender;
    if (!sender.initialize(10, 1)) {
        std::cerr << "Failed to initialize async sender" << std::endl;
        return;
    }
    
    std::vector<std::shared_ptr<NumArray>> inputs;
    inputs.push_back(create_test_numarray(1, 3, 224, 224));
    
    std::string test_url = "http://192.168.11.1:1180/FaceKeypoints";
    
    // 测试非阻塞行为
    const int num_requests = 20;
    std::vector<std::chrono::steady_clock::time_point> send_times;
    
    for (int i = 0; i < num_requests; ++i) {
        auto start = std::chrono::steady_clock::now();
        bool success = sender.sendImageAsync(inputs, test_url);
        auto end = std::chrono::steady_clock::now();
        
        auto duration = std::chrono::duration<double, std::micro>(end - start).count();
        send_times.push_back(end);
        
        std::cout << "Request " << i << " send time: " << duration << " microseconds" 
                  << " (queued: " << (success ? "YES" : "NO") << ")" << std::endl;
    }
    
    // 计算发送间隔
    double total_interval = 0.0;
    for (size_t i = 1; i < send_times.size(); ++i) {
        auto interval = std::chrono::duration<double, std::milli>(send_times[i] - send_times[i-1]).count();
        total_interval += interval;
    }
    
    std::cout << "Non-blocking test results:" << std::endl;
    std::cout << "Average interval between sends: " << (total_interval / (send_times.size() - 1)) << "ms" << std::endl;
    std::cout << "This should be very small, proving non-blocking behavior" << std::endl;
    
    sender.shutdown();
}

int main() {
    std::cout << "Async Image Sender Test Suite" << std::endl;
    std::cout << "=============================" << std::endl;
    
    try {
        test_basic_async_sending();
        test_high_throughput_sending();
        test_callback_functionality();
        test_non_blocking_behavior();
        
        std::cout << "\n=== All Async Tests Completed ===" << std::endl;
        std::cout << "Key benefits demonstrated:" << std::endl;
        std::cout << "1. Non-blocking sends (x86 doesn't wait for OAX4600 response)" << std::endl;
        std::cout << "2. High throughput queuing" << std::endl;
        std::cout << "3. Background processing" << std::endl;
        std::cout << "4. Optional result callbacks" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
