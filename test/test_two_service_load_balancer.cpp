#include <iostream>
#include <vector>
#include <thread>
#include <chrono>
#include <map>
#include <iomanip>
#include "image_level_load_balancer.h"
#include "cc_numarray_tool.h"

using namespace tongxing;

// 创建测试图片数据
std::shared_ptr<NumArray> create_test_image(int image_id) {
    auto array = creat_numarray({1, 3, 224, 224}, NumArray::DataType::UINT8);
    
    // 使用image_id创建特征数据
    size_t total_size = 1 * 3 * 224 * 224;
    for (size_t i = 0; i < total_size; ++i) {
        array->data[i] = static_cast<unsigned char>((image_id * 50 + i) % 256);
    }
    
    return array;
}

void print_test_header(const std::string& test_name) {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "  " << test_name << std::endl;
    std::cout << std::string(60, '=') << std::endl;
}

void print_service_status(const ImageLevelLoadBalancer& balancer) {
    auto status = balancer.getLoadBalancerStatus();
    
    std::cout << "\n--- Service Status ---" << std::endl;
    std::cout << "Pending: " << status["pending_requests"].asInt() << std::endl;
    std::cout << "Completed: " << status["completed_requests"].asInt() << std::endl;
    std::cout << "Avg Time: " << std::fixed << std::setprecision(2) 
              << status["average_processing_time_ms"].asDouble() << "ms" << std::endl;
    
    const auto& services = status["services"];
    for (const auto& service : services) {
        std::cout << "Service " << service["ip"].asString() 
                  << " - Available: " << (service["available"].asBool() ? "YES" : "NO")
                  << ", Load: " << service["current_load"].asInt()
                  << ", Avg Time: " << std::fixed << std::setprecision(2)
                  << service["avg_processing_time_ms"].asDouble() << "ms" << std::endl;
    }
}

bool test_basic_functionality() {
    print_test_header("Basic Functionality Test");
    
    ImageLevelLoadBalancer balancer;
    
    // 初始化两个服务
    std::vector<std::string> service_ips = {"***********", "***********"};
    std::vector<std::string> models = {"FaceDetection", "FaceKeypoints", "eye", "Dms_PhoneSmoking"};
    
    if (!balancer.initialize(service_ips, 1180, models)) {
        std::cerr << "❌ Failed to initialize load balancer" << std::endl;
        return false;
    }
    
    std::cout << "✅ Load balancer initialized with 2 services" << std::endl;
    
    // 提交3张图片
    std::vector<uint64_t> sequences;
    for (int i = 1; i <= 3; ++i) {
        std::vector<std::shared_ptr<NumArray>> input = {create_test_image(i)};
        uint64_t seq_id = balancer.submitImageProcessing(input);
        
        if (seq_id > 0) {
            sequences.push_back(seq_id);
            std::cout << "📤 Submitted image " << i << ", sequence_id: " << seq_id << std::endl;
        } else {
            std::cerr << "❌ Failed to submit image " << i << std::endl;
            return false;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(300));
    }
    
    // 收集结果
    std::cout << "\n📥 Collecting results..." << std::endl;
    std::map<std::string, int> service_usage;
    
    for (size_t i = 0; i < sequences.size(); ++i) {
        ImageProcessingResult result(0);
        bool got_result = false;
        
        // 等待结果（最多30秒）
        for (int retry = 0; retry < 300; ++retry) {
            if (balancer.getNextResult(result)) {
                got_result = true;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        if (got_result) {
            service_usage[result.service_ip]++;
            
            std::cout << "✅ Result " << (i + 1) 
                      << " - Sequence: " << result.sequence_id
                      << " (expected: " << sequences[i] << ")"
                      << " - Service: " << result.service_ip
                      << " - Time: " << std::fixed << std::setprecision(2) 
                      << result.total_processing_time_ms << "ms"
                      << " - Models: " << result.model_results.size() << std::endl;
            
            // 验证序列顺序
            if (result.sequence_id != sequences[i]) {
                std::cerr << "❌ SEQUENCE ORDER ERROR!" << std::endl;
                return false;
            }
            
            // 验证模型完整性
            if (result.model_results.size() != models.size()) {
                std::cerr << "❌ INCOMPLETE MODEL RESULTS!" << std::endl;
                return false;
            }
            
        } else {
            std::cerr << "❌ Timeout waiting for result " << (i + 1) << std::endl;
            return false;
        }
    }
    
    // 验证负载分布
    std::cout << "\n📊 Load Distribution:" << std::endl;
    for (const auto& pair : service_usage) {
        std::cout << "  " << pair.first << ": " << pair.second << " images" << std::endl;
    }
    
    bool both_services_used = (service_usage.size() == 2);
    if (both_services_used) {
        std::cout << "✅ Both services were utilized" << std::endl;
    } else {
        std::cout << "⚠️  Only one service was used (may be normal for small test)" << std::endl;
    }
    
    print_service_status(balancer);
    
    std::cout << "✅ Basic functionality test PASSED" << std::endl;
    return true;
}

bool test_sequential_output() {
    print_test_header("Sequential Output Test");
    
    ImageLevelLoadBalancer balancer;
    
    std::vector<std::string> service_ips = {"***********", "***********"};
    
    if (!balancer.initialize(service_ips)) {
        std::cerr << "❌ Failed to initialize load balancer" << std::endl;
        return false;
    }
    
    // 快速提交6张图片
    const int num_images = 6;
    std::vector<uint64_t> expected_sequences;
    
    std::cout << "📤 Rapidly submitting " << num_images << " images..." << std::endl;
    for (int i = 1; i <= num_images; ++i) {
        std::vector<std::shared_ptr<NumArray>> input = {create_test_image(i)};
        uint64_t seq_id = balancer.submitImageProcessing(input);
        expected_sequences.push_back(seq_id);
        std::cout << "  Image " << i << " -> sequence_id: " << seq_id << std::endl;
    }
    
    // 验证结果严格按序返回
    std::cout << "\n📥 Verifying sequential output..." << std::endl;
    std::map<std::string, int> service_distribution;
    
    for (int i = 0; i < num_images; ++i) {
        ImageProcessingResult result(0);
        bool got_result = false;
        
        for (int retry = 0; retry < 500; ++retry) {
            if (balancer.getNextResult(result)) {
                got_result = true;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        if (got_result) {
            service_distribution[result.service_ip]++;
            
            std::cout << "  Result " << (i + 1) 
                      << " - Got: " << result.sequence_id
                      << " - Expected: " << expected_sequences[i]
                      << " - Service: " << result.service_ip;
            
            if (result.sequence_id == expected_sequences[i]) {
                std::cout << " ✅" << std::endl;
            } else {
                std::cout << " ❌ WRONG ORDER!" << std::endl;
                return false;
            }
        } else {
            std::cerr << "❌ Timeout waiting for result " << (i + 1) << std::endl;
            return false;
        }
    }
    
    std::cout << "\n📊 Service Distribution:" << std::endl;
    for (const auto& pair : service_distribution) {
        double percentage = (pair.second * 100.0) / num_images;
        std::cout << "  " << pair.first << ": " << pair.second 
                  << " images (" << std::fixed << std::setprecision(1) 
                  << percentage << "%)" << std::endl;
    }
    
    print_service_status(balancer);
    
    std::cout << "✅ Sequential output test PASSED" << std::endl;
    return true;
}

bool test_load_balancing_efficiency() {
    print_test_header("Load Balancing Efficiency Test");
    
    ImageLevelLoadBalancer balancer;
    
    std::vector<std::string> service_ips = {"***********", "***********"};
    
    if (!balancer.initialize(service_ips)) {
        std::cerr << "❌ Failed to initialize load balancer" << std::endl;
        return false;
    }
    
    // 提交较多图片测试负载均衡
    const int num_images = 10;
    std::vector<uint64_t> sequences;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    std::cout << "📤 Submitting " << num_images << " images for load balancing test..." << std::endl;
    for (int i = 1; i <= num_images; ++i) {
        std::vector<std::shared_ptr<NumArray>> input = {create_test_image(i)};
        uint64_t seq_id = balancer.submitImageProcessing(input);
        sequences.push_back(seq_id);
        
        // 短暂间隔以观察负载均衡
        std::this_thread::sleep_for(std::chrono::milliseconds(150));
    }
    
    // 收集所有结果
    std::map<std::string, int> service_usage;
    std::vector<double> processing_times;
    
    std::cout << "\n📥 Collecting results..." << std::endl;
    for (int i = 0; i < num_images; ++i) {
        ImageProcessingResult result(0);
        bool got_result = false;
        
        for (int retry = 0; retry < 600; ++retry) {
            if (balancer.getNextResult(result)) {
                got_result = true;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        if (got_result) {
            service_usage[result.service_ip]++;
            processing_times.push_back(result.total_processing_time_ms);
            
            std::cout << "  " << (i + 1) << ". Seq:" << result.sequence_id 
                      << " Service:" << result.service_ip
                      << " Time:" << std::fixed << std::setprecision(0) 
                      << result.total_processing_time_ms << "ms" << std::endl;
        } else {
            std::cerr << "❌ Timeout for result " << (i + 1) << std::endl;
            return false;
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    // 分析结果
    std::cout << "\n📊 Performance Analysis:" << std::endl;
    std::cout << "Total time: " << std::fixed << std::setprecision(2) << total_time << "ms" << std::endl;
    std::cout << "Throughput: " << std::fixed << std::setprecision(2) 
              << (num_images * 1000.0 / total_time) << " images/second" << std::endl;
    
    if (!processing_times.empty()) {
        double avg_time = std::accumulate(processing_times.begin(), processing_times.end(), 0.0) / processing_times.size();
        double min_time = *std::min_element(processing_times.begin(), processing_times.end());
        double max_time = *std::max_element(processing_times.begin(), processing_times.end());
        
        std::cout << "Avg processing time: " << std::fixed << std::setprecision(2) << avg_time << "ms" << std::endl;
        std::cout << "Min processing time: " << std::fixed << std::setprecision(2) << min_time << "ms" << std::endl;
        std::cout << "Max processing time: " << std::fixed << std::setprecision(2) << max_time << "ms" << std::endl;
    }
    
    std::cout << "\n📊 Load Distribution:" << std::endl;
    for (const auto& pair : service_usage) {
        double percentage = (pair.second * 100.0) / num_images;
        std::cout << "  " << pair.first << ": " << pair.second 
                  << " images (" << std::fixed << std::setprecision(1) 
                  << percentage << "%)" << std::endl;
    }
    
    // 验证负载均衡效果
    bool good_balance = true;
    if (service_usage.size() == 2) {
        auto it = service_usage.begin();
        int usage1 = it->second;
        ++it;
        int usage2 = it->second;
        
        double imbalance = std::abs(usage1 - usage2) / static_cast<double>(std::max(usage1, usage2));
        if (imbalance > 0.5) { // 允许50%的不平衡
            good_balance = false;
        }
    }
    
    if (good_balance) {
        std::cout << "✅ Load balancing is effective" << std::endl;
    } else {
        std::cout << "⚠️  Load balancing could be improved" << std::endl;
    }
    
    print_service_status(balancer);
    
    std::cout << "✅ Load balancing efficiency test PASSED" << std::endl;
    return true;
}

int main() {
    std::cout << "Two-Service Load Balancer Test Suite" << std::endl;
    std::cout << "Testing with *********** and ***********" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    
    bool all_passed = true;
    
    try {
        // 运行测试套件
        if (!test_basic_functionality()) {
            all_passed = false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        if (!test_sequential_output()) {
            all_passed = false;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        if (!test_load_balancing_efficiency()) {
            all_passed = false;
        }
        
        // 最终结果
        print_test_header("Test Results Summary");
        
        if (all_passed) {
            std::cout << "🎉 ALL TESTS PASSED!" << std::endl;
            std::cout << "\nKey achievements verified:" << std::endl;
            std::cout << "✅ Image-level load balancing works correctly" << std::endl;
            std::cout << "✅ All models for one image processed on same service" << std::endl;
            std::cout << "✅ Sequential output ordering maintained" << std::endl;
            std::cout << "✅ Load distribution across both services" << std::endl;
            std::cout << "✅ Performance improvement with multiple services" << std::endl;
        } else {
            std::cout << "❌ SOME TESTS FAILED!" << std::endl;
            std::cout << "Please check the error messages above." << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return all_passed ? 0 : 1;
}
