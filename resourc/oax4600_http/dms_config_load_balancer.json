{"class_name": "Functional", "config": {"name": "dms_with_load_balancer", "load_balancer_config": {"enabled": true, "service_ips": ["***********", "***********"], "port": 1180, "model_endpoints": ["FaceDetection", "FaceKeypoints", "eye", "Dms_PhoneSmoking"], "max_concurrent_per_service": 2, "request_timeout_ms": 8000}, "layers": [{"class_name": "InputLayer", "config": {"dtype": "uint8_t", "sparse": false, "ragged": false, "name": "input_1"}, "name": "input_1", "inbound_nodes": []}, {"class_name": "CropResizeNorm2D", "config": {"output_width": 160, "output_height": 96, "keep_ratio": true, "flag_crop": false, "flag_norm": false, "flag_input_nchw": true, "flag_output_nchw": true, "flag_static_mode": true, "mean": [0], "std": [255.0]}, "name": "faceDet_pre_normal", "inbound_nodes": [["input_1", 0, 0, {}]]}, {"class_name": "image_level_load_balancer_inference", "config": {"type": "load_balanced", "service_ips": ["***********", "***********"], "port": 1180, "model_endpoints": ["FaceDetection", "FaceKeypoints", "eye", "Dms_PhoneSmoking"], "enforce_sequential_output": true, "max_concurrent_per_service": 2, "request_timeout_ms": 8000, "health_check_interval_ms": 30000}, "name": "multi_model_load_balanced_infer", "inbound_nodes": [["faceDet_pre_normal", 0, 0, {}]]}, {"class_name": "OutputLayer", "config": {"name": "combined_output"}, "name": "combined_output", "inbound_nodes": [["multi_model_load_balanced_infer", 0, 0, {}]]}]}, "test_scenarios": {"basic_load_balancing": {"description": "基础负载均衡测试", "num_images": 5, "expected_behavior": "图片分配到7.1和8.1，结果按序输出"}, "sequential_processing": {"description": "时序处理测试", "num_images": 8, "expected_behavior": "快速提交多张图片，验证严格按序输出"}, "load_distribution": {"description": "负载分布测试", "num_images": 12, "expected_behavior": "验证两个服务实例的负载分布"}}, "fallback_config": {"description": "如果负载均衡器不可用，回退到原始配置", "layers": [{"class_name": "oax4600_http_inference", "config": {"type": "inside", "http_url": "http://***********:1180/FaceDetection", "model_url": "http://***********:1180/tar"}, "name": "faceDet_infer_fallback"}, {"class_name": "oax4600_http_inference", "config": {"type": "inside", "http_url": "http://***********:1180/FaceKeypoints", "model_url": "http://***********:1180/tar"}, "name": "facekeypoint_infer_fallback"}, {"class_name": "oax4600_http_inference", "config": {"type": "inside", "http_url": "http://***********:1180/eye", "model_url": "http://***********:1180/tar"}, "name": "eye_infer_fallback"}, {"class_name": "oax4600_http_inference", "config": {"type": "inside", "http_url": "http://***********:1180/Dms_PhoneSmoking", "model_url": "http://***********:1180/tar"}, "name": "phone_infer_fallback"}]}, "monitoring": {"enable_performance_logging": true, "log_level": "INFO", "metrics_to_track": ["processing_time_per_image", "load_distribution", "sequential_output_compliance", "service_availability", "throughput_improvement"]}, "validation_rules": {"sequential_output": {"description": "验证结果按sequence_id顺序输出", "tolerance": 0, "critical": true}, "load_balancing": {"description": "验证负载在多个服务间分布", "min_services_used": 2, "max_imbalance_ratio": 0.3}, "model_completeness": {"description": "验证每张图片所有模型结果完整", "required_models": ["FaceDetection", "FaceKeypoints", "eye", "Dms_PhoneSmoking"], "critical": true}, "performance": {"description": "验证性能提升", "min_throughput_improvement": 1.3, "max_acceptable_latency_ms": 10000}}}