cmake_minimum_required(VERSION 3.9)
project(DMS)

if(DEFINED CMAKE_TOOLCHAIN_FILE)
set(LIBRARY_OUTPUT_PATH_ROOT ${CMAKE_BINARY_DIR} CACHE PATH "root for library output, set this to change where android libs are compiled to")
get_filename_component(CMAKE_TOOLCHAIN_FILE_NAME ${CMAKE_TOOLCHAIN_FILE} NAME)
find_file(CMAKE_TOOLCHAIN_FILE ${CMAKE_TOOLCHAIN_FILE_NAME} PATHS ${CMAKE_SOURCE_DIR} NO_DEFAULT_PATH)
message(STATUS "CMAKE_TOOLCHAIN_FILE = ${CMAKE_TOOLCHAIN_FILE}")
endif()


# -fsanitize=address  内存越界
# -fsanitize=leak   内存泄漏
# -g  调试模式
SET ( CMAKE_CXX_FLAGS "-std=c++17 -O3   -Wall -fpermissive -fPIC   -ffunction-sections -fdata-sections  -ffast-math  ${CMAKE_CXX_FLAGS} " )
SET ( CMAKE_C_FLAGS " -fpermissive -O3 -Wall -fPIC    -ffunction-sections -fdata-sections  -ffast-math  ${CMAKE_C_FLAGS} ")

# SET(CMAKE_BUILD_TYPE "Debug")
# SET(CMAKE_BUILD_TYPE "Release")


#这里区分车型编译对应车型版本，默认元up,否则是海鸥（EQ）
# if(${BYD_YUAN_UP} STREQUAL ON)
#     add_definitions(-DBYD_YUAN_UP)
# endif()

IF(CAR_BUILD_TYPE  STREQUAL "BYD_SC3E")
    add_definitions(-DBYD_SC3E)
    execute_process(COMMAND python3 ../config/creat_distraction_param_cpp_file.py ../config/sc3e)
    message(STATUS "CAR_BUILD_TYPE  : ${CAR_BUILD_TYPE}")
ELSEIF (CAR_BUILD_TYPE  STREQUAL "BYD_EQ")
    add_definitions(-DBYD_EQ)
    execute_process(COMMAND python3 ../config/creat_distraction_param_cpp_file.py ../config/eq)
    message(STATUS "CAR_BUILD_TYPE  : ${CAR_BUILD_TYPE}")
ELSEIF (CAR_BUILD_TYPE  STREQUAL "BYD_EQ_R")
    add_definitions(-DBYD_EQ_R)
    execute_process(COMMAND python3 ../config/creat_distraction_param_cpp_file.py ../config/eq_r)
    message(STATUS "CAR_BUILD_TYPE  : ${CAR_BUILD_TYPE}")
ELSEIF (CAR_BUILD_TYPE  STREQUAL "BYD_HA6")
    add_definitions(-DBYD_HA6)
    execute_process(COMMAND python3 ../config/creat_distraction_param_cpp_file.py ../config/ha6)
    message(STATUS "CAR_BUILD_TYPE  : ${CAR_BUILD_TYPE}")
ELSEIF (CAR_BUILD_TYPE  STREQUAL "BYD_SA2")
    add_definitions(-DBYD_SA2)
    execute_process(COMMAND python3 ../config/creat_distraction_param_cpp_file.py ../config/sa2)
    message(STATUS "CAR_BUILD_TYPE  : ${CAR_BUILD_TYPE}")
ELSEIF (CAR_BUILD_TYPE  STREQUAL "BYD_HKH_L")
    add_definitions(-DBYD_HKH_L)
    execute_process(COMMAND python3 ../config/creat_distraction_param_cpp_file.py ../config/hkh_l)
    message(STATUS "CAR_BUILD_TYPE  : ${CAR_BUILD_TYPE}")
ELSEIF (CAR_BUILD_TYPE  STREQUAL "BYD_SC2EDE")
    add_definitions(-DBYD_SC2EDE)
    execute_process(COMMAND python3 ../config/creat_distraction_param_cpp_file.py ../config/sc2ede)
    message(STATUS "CAR_BUILD_TYPE  : ${CAR_BUILD_TYPE}")
    ELSEIF (CAR_BUILD_TYPE  STREQUAL "BYD_HKH_R")
    add_definitions(-DBYD_HKH_R)
    execute_process(COMMAND python3 ../config/creat_distraction_param_cpp_file.py ../config/hkh_r)
    message(STATUS "CAR_BUILD_TYPE  : ${CAR_BUILD_TYPE}")
ELSEIF (CAR_BUILD_TYPE  STREQUAL "BYD_SC3EFE")
    add_definitions(-DBYD_SC3EFE)
    execute_process(COMMAND python3 ../config/creat_distraction_param_cpp_file.py ../config/sc3efe)
ELSEIF (CAR_BUILD_TYPE  STREQUAL "BYD_SC3EFE_R")
    add_definitions(-DBYD_SC3EFE_R)
    execute_process(COMMAND python3 ../config/creat_distraction_param_cpp_file.py ../config/sc3efe)
ELSE()
    message(STATUS "CAR_BUILD_TYPE  : ${CAR_BUILD_TYPE}")
    message(FATAL_ERROR "CAR_BUILD_TYPE ONLY SUPPORT SC3E EQ EQ_R SA2 HA6 SC3EFE SC3EFE_R Or HKH_L SC2EDE")
ENDIF()

if(${INTERNAL_TEST_TOOL_MODE} STREQUAL ON)
    add_definitions(-DINTERNAL_TEST_TOOL_MODE)
endif()

if(${BYD_SOP} STREQUAL ON)
    add_definitions(-DBYD_SOP)
endif()

message(STATUS "CURRENT BUILD TYPE : ${CMAKE_BUILD_TYPE}")
message(STATUS "CURRENT INTEGRATION_TEST_MODE  : ${INTEGRATION_TEST_MODE}")
# message(STATUS "CURRENT BYD_YUAN_UP  : ${BYD_YUAN_UP}")
message(STATUS "CURRENT CAR_BUILD_TYPE  : ${CAR_BUILD_TYPE}")
message(STATUS "MODEL_TEST_MODE : ${MODEL_TEST_MODE}")
message(STATUS "INTERNAL_TEST_TOOL_MODE : ${INTERNAL_TEST_TOOL_MODE}") 
message(STATUS "HTTP_INFERENCE : ${HTTP_INFERENCE}")
message(STATUS "USE_ACTIVATE : ${USE_ACTIVATE}")
message(STATUS "BYD_SOP : ${BYD_SOP}")

IF (CMAKE_BUILD_TYPE MATCHES "Release")
    SET( CMAKE_CXX_FLAGS " -D__RELEASE__ ${CMAKE_CXX_FLAGS} " )
    SET( CMAKE_C_FLAGS " -D__RELEASE__ ${CMAKE_C_FLAGS} ")
ELSEIF (CMAKE_BUILD_TYPE MATCHES "Debug")
    SET( CMAKE_CXX_FLAGS "-g3  -D__DEBUG__  ${CMAKE_CXX_FLAGS} " )
    SET( CMAKE_C_FLAGS "-g3   -D__DEBUG__ ${CMAKE_C_FLAGS} ")
ELSE()
    message(FATAL_ERROR "CMAKE_BUILD_TYPE ONLY SUPPORT Debug Or Release")
ENDIF()

if(${MEM_ISSUE_DEBUG} STREQUAL ON)
    add_definitions(-DMEM_ISSUE_DEBUG)
    SET( CMAKE_CXX_FLAGS " -fsanitize=address -fsanitize=leak -g ${CMAKE_CXX_FLAGS} " )
    SET( CMAKE_C_FLAGS " -fsanitize=address -fsanitize=leak -g ${CMAKE_C_FLAGS} ") 
endif()

if(Linux_oax4600_aarch64)
    SET( CMAKE_CXX_FLAGS "-DUSE_OAX4600 ${CMAKE_CXX_FLAGS} " )
    SET( CMAKE_C_FLAGS "-DUSE_OAX4600 ${CMAKE_C_FLAGS} ")
endif()

#SET (LIBYUV_PATH thirdparty/libyuv)
#message("LIBYUV_PATH:"  ${LIBYUV_PATH}/libyuv.cmake)
#include(${LIBYUV_PATH}/libyuv.cmake)
#include_directories(
#  ${LIBYUV_INCLUDE}
#) include(${SDK_PATH}/sdk.cmake)
if(X9HP)
 include(${SDK_PATH}/sdk.cmake)
endif()


# IF (CMAKE_BUILD_TYPE MATCHES "Release")
if(Linux-x86_64)
    include_directories(thirdparty/curl/include)
    link_directories(thirdparty/curl/Linux-x86_64/lib)
    SET(CURL_LIB libcurl.so)
    add_definitions(-DX86_64)
elseif(Android_r18b OR Android_r25b)
    include_directories(thirdparty/curl/include)
    link_directories(thirdparty/curl/Android_r18b/${ANDROID_ABI}/lib)
    link_directories(thirdparty/curl/Android_r18b/${ANDROID_ABI}/lib_static)
    SET(CURL_LIB libcurl.a)
endif()
# ENDIF()



if(Linux-x86_64)
    # find_package(OpenCV)
    # SET(OPENCV4_LIB ${OpenCV_LIBRARIES})
    link_directories(thirdparty/zlmk/Linux-x86_64/lib)
    link_directories(thirdparty/protobuf/Linux-x86_64/lib)
    include_directories(thirdparty/zlmk/include)
    include_directories(thirdparty/protobuf/include)
    SET(ZLMK_LIB libmk_api.a libzlmediakit.a libzltoolkit.a  libmov.a libmpeg.a libflv.a libsrt.a )
    SET(PROTOBUF_LIB libprotobuf.a)

    include_directories(thirdparty/opencv/Linux-x86_64/include/opencv4)
    link_directories(thirdparty/opencv/Linux-x86_64/lib_static)
    SET(OPENCV4_LIB 
    libopencv_imgcodecs.a
    libopencv_calib3d.a
    libopencv_features2d.a
    libopencv_flann.a
    libopencv_imgproc.a
    libopencv_videoio.a
    libopencv_highgui.a
    libopencv_core.a
    liblibjpeg-turbo.a
    liblibpng.a
    liblibtiff.a
    libade.a
    libittnotify.a
    libquirc.a
    libIlmImf.a
    libzlib.a
    liblibopenjp2.a
    liblibwebp.a
   )
elseif(Android_r18b)
    SET( CMAKE_CXX_FLAGS " -DANDROID ${CMAKE_CXX_FLAGS} " )
    SET( CMAKE_C_FLAGS " -DANDROID ${CMAKE_C_FLAGS} ")
    SET(CUTIL_PATH thirdparty/cutil)
    link_directories(${CUTIL_PATH}/${ANDROID_ABI}/lib)
    message("libcutils:"  ${CUTIL_PATH}/${ANDROID_ABI}/lib)
    # SET(CUTIL_LIB 
        # libcutils.so
    # )

    add_definitions(-DWITH_PHONE_SMOKING_DET)
  
   

    include_directories(thirdparty/opencv/Android_r18b/include)
    link_directories(thirdparty/opencv/Android_r18b/${ANDROID_ABI}/lib)
    link_directories(thirdparty/opencv/Android_r18b/${ANDROID_ABI}/lib_static)
    SET(OPENCV4_LIB 
        libopencv_calib3d.so
        libopencv_core.so
        libopencv_features2d.so
        libopencv_flann.so
        libopencv_imgcodecs.so
        libopencv_imgproc.so
        libopencv_videoio.so
        libopencv_highgui.so
    )

    SET(OPENCV4_LIB_STATIC 
    libopencv_calib3d.a
    libopencv_core.a
    libopencv_features2d.a
    libopencv_flann.a
    libopencv_imgcodecs.a
    libopencv_imgproc.a
    libopencv_videoio.a
    liblibjpeg-turbo.a
    liblibopenjp2.a
    liblibpng.a
    liblibtiff.a
    liblibwebp.a
    libIlmImf.a
    # libade.a
    # libopencv_highgui.a
    libtegra_hal.a
    libittnotify.a


    )
elseif(Android_r25b)
    include_directories(thirdparty/opencv/Android_r25b/native/jni/include)
    link_directories(thirdparty/opencv/Android_r25b/native/libs/arm64-v8a)

    SET(OPENCV4_LIB 
     libopencv_java4.so
    )
elseif(Linux_ov_aarch32)
    include_directories(thirdparty/opencv/Linux_ov_aarch32/include)
    link_directories(thirdparty/opencv/Linux_ov_aarch32/lib)
    SET(OPENCV4_LIB_STATIC 
        libopencv_calib3d.a
        libopencv_features2d.a
        libopencv_flann.a
        libopencv_imgproc.a
        libopencv_imgcodecs.a
        libopencv_videoio.a
        libopencv_highgui.a
        libopencv_core.a
        liblibjpeg-turbo.a
        liblibopenjp2.a
        liblibpng.a
        liblibtiff.a
        liblibwebp.a
        libade.a
        libittnotify.a
        libzlib.a
        libquirc.a
        libtegra_hal.a
    )
elseif(Linux_amba_aarch64)
    include_directories(thirdparty/opencv/Linux_amba_aarch64/include)
    link_directories(thirdparty/opencv/Linux_amba_aarch64/lib)
    SET(OPENCV4_LIB_STATIC 
    libopencv_calib3d.a
    libopencv_features2d.a
    libopencv_flann.a
    libopencv_imgcodecs.a
    libopencv_imgproc.a
    libopencv_videoio.a
    libopencv_core.a
    liblibjpeg-turbo.a
    liblibopenjp2.a
    liblibpng.a
    liblibtiff.a
    liblibwebp.a
    libade.a
    # libopencv_highgui.a
    libtegra_hal.a
    libittnotify.a
    libzlib.a
    )
elseif(Linux_oax4600_aarch64)

    link_directories(thirdparty/zlmk/Linux_oax4600_aarch64/lib)
    link_directories(thirdparty/protobuf/Linux_oax4600_aarch64/lib)
    include_directories(thirdparty/zlmk/include)
    include_directories(thirdparty/protobuf/include)
    SET(ZLMK_LIB libmk_api.a libzlmediakit.a libzltoolkit.a  libmov.a libmpeg.a libflv.a libsrt.a )
    SET(PROTOBUF_LIB libprotobuf.a)

    include_directories(thirdparty/opencv/Linux_oax4600_aarch64/include/opencv4)
    link_directories(thirdparty/opencv/Linux_oax4600_aarch64/lib)
    SET(OPENCV4_LIB_STATIC 
    libopencv_imgcodecs.a
    libopencv_imgproc.a
    libopencv_calib3d.a
    libopencv_features2d.a
    libopencv_flann.a
    libopencv_videoio.a
    libopencv_core.a
    liblibjpeg-turbo.a
    liblibopenjp2.a
    # liblibpng.a
    # liblibtiff.a
    liblibwebp.a
    libade.a
    # libopencv_highgui.a
    libtegra_hal.a
    # libittnotify.a
    libzlib.a
    libIlmImf.a
    libquirc.a
    libzlib.a
    )
else()
    SET(OPENCV4_PATH thirdparty/opencv/${COMPILER_NAME})
    SET(OPENCV4_LIB 
    libopencv_calib3d.so
    libopencv_core.so
    libopencv_features2d.so
    libopencv_flann.so
    libopencv_imgcodecs.so
    libopencv_imgproc.so
    # libopencv_video.so
    # libopencv_videoio.so
    # libopencv_dnn.so
    # libopencv_gapi.so
    # libopencv_highgui.so
    # libopencv_ml.so
    # libopencv_objdetect.so
    # libopencv_photo.so
    # libopencv_stitching.so
    libz.so
    libpng16.so
    libtiff.so
    libjpeg.so
    )
endif()

if(USE_MNN )
    set(MNN_PATH thirdparty/MNN/${COMPILER_NAME})
    set(MNN_LIB MNN)
    message("MNN_PATH:" ${MNN_PATH})
endif()

if(USE_AMBA)
execute_process(COMMAND python3 ../resourc/creat_resourc_cpp_file.py ../resourc/amba)
elseif(Linux_oax4600_aarch64)

if(${MODEL_TEST_MODE} STREQUAL ON)
    message("model test!")
    execute_process(COMMAND python3 ../resourc/creat_resourc_cpp_file.py ../resourc/oax4600_test)
else()
    execute_process(COMMAND python3 ../resourc/creat_resourc_cpp_file.py ../resourc/oax4600)
endif()

elseif(ONLY_LANDMARKS)
execute_process(COMMAND python3 ../resourc/creat_resourc_cpp_file.py ../resourc/only_lanemasks_mnn)
elseif(USE_MNN)
    if(${HTTP_INFERENCE} STREQUAL ON)
        execute_process(COMMAND python3 ../resourc/creat_resourc_cpp_file.py ../resourc/oax4600_http)
    else()
        execute_process(COMMAND python3 ../resourc/creat_resourc_cpp_file.py ../resourc/mnn)
    endif()
elseif(X9HP)
execute_process(COMMAND python3 ../resourc/creat_resourc_cpp_file.py ../resourc/tvm)
else()
execute_process(COMMAND python3 ../resourc/creat_resourc_cpp_file.py)
endif()

if(${HTTP_INFERENCE} STREQUAL ON)
    execute_process(COMMAND  protoc-3.20.1.0 --proto_path ../tongxing_util/proto/ --cpp_out ./  ../tongxing_util/proto/inference_result.proto)    
endif()
execute_process(COMMAND  protoc-3.20.1.0 --proto_path ../tongxing_util/proto/ --cpp_out ./  ../tongxing_util/proto/tongxing_dms.proto)

if(USE_AMBA)
    set(AMBA_SDK_PATH thirdparty/amba_sdk)
    set(AMBA_SDK_LIB 
        eazyai
        nnctrl
        cavalry_mem
        vproc
        )
    include_directories(${AMBA_SDK_PATH}/include
    ${AMBA_SDK_PATH}/include/cavalry)
    
    link_directories(${AMBA_SDK_PATH}/lib)
endif()

if(${INTERNAL_TEST_TOOL_MODE} STREQUAL ON)
SET(INTERFACE_SRC
    src/interface/tx_dms_sdk.cpp
    src/process/calmcar_dms_process_test.cpp
    src/process/dms_process.cpp
    src/warming/dms_calculate_warning_byd.cpp
    src/warming/dms_distraction_warning_byd.cpp
    src/warming/dms_out_warning.cpp
    src/warming/dms_out_warning.cpp
)
else()
SET(INTERFACE_SRC
    src/interface/tx_dms_sdk.cpp
    src/process/calmcar_dms_process.cpp
    src/process/dms_process.cpp
    src/warming/dms_calculate_warning_byd.cpp
    src/warming/dms_distraction_warning_byd.cpp
    src/warming/dms_out_warning.cpp
    src/warming/dms_out_warning.cpp
)
endif()

SET(MemoryManager_SRC
    tongxing_util/src/memory_manager/cc_memory_pool.c
    tongxing_util/src/memory_manager/cc_small_memory_pool.c
    tongxing_util/src/memory_manager/cc_medium_memory_pool.c
    tongxing_util/src/memory_manager/cc_big_memory_pool.c
    tongxing_util/src/memory_manager/cc_memory_manager.cpp
    tongxing_util/src/memory_manager/cc_tiny_mempool.cpp
    
)

SET(UTIL_SRC 
    tongxing_util/src/util/cc_blob_data.cpp
    tongxing_util/src/util/cc_math_tool.cpp
    tongxing_util/src/util/cc_video_frame.cpp
    tongxing_util/src/util/cc_tensor.cpp
    tongxing_util/src/util/cc_npy_loader.cpp
    tongxing_util/src/util/file_utils.cpp
    tongxing_util/src/util/jsoncpp.cpp
    tongxing_util/src/util/image_utils.cpp
    tongxing_util/src/util/cc_numarray_tool.cpp
    tongxing_util/src/util/cc_bool_time_windows.cpp
    tongxing_util/src/util/cc_status_hold.cpp
    tongxing_util/src/util/cc_mouth_opening.cpp
    tongxing_util/src/util/cc_timestamp.cpp
    tongxing_util/src/util/cc_version.cpp
    tongxing_util/src/util/time_profile.cpp
    tongxing_util/src/util/time_function.cpp
    tongxing_util/src/util/eyes_pos_transform.cpp
    tongxing_util/src/util/HeadPoseCalibrator.cpp
    )

SET(LOG_SYSTEM 
    tongxing_util/src/log/CalmCarLog.cpp
    )

SET(MODULE_POSTPROCESS_SRC 
    # tongxing_util/src/module/postprocess/bbox_decode/cc_yolo_bbox_decoder.cpp
    tongxing_util/src/module/postprocess/bbox_decode/cc_face_bbox_decoder.cpp
    tongxing_util/src/module/postprocess/keypoints_decode/cc_heatmap_keypoints_decoder.cpp
    tongxing_util/src/module/postprocess/seg_decode/cc_seg_decoder.cpp
    tongxing_util/src/module/postprocess/seg_decode/cc_seg_decoder_mat.cpp
)

SET(MODULE_PREPROCESS_SRC 
    tongxing_util/src/module/preprocess/cc_pad2d.cpp
    tongxing_util/src/module/preprocess/cc_resize2d.cpp
    tongxing_util/src/module/preprocess/cc_normalization.cpp
    tongxing_util/src/module/preprocess/cc_corp2d.cpp
    tongxing_util/src/module/preprocess/cc_corp_resize_norm_2d.cpp

)
SET(MODULE_UTIL_SRC
    tongxing_util/src/module/util/cc_coordinate_scaling2d.cpp
    tongxing_util/src/module/util/cc_shape.cpp
    tongxing_util/src/module/util/cc_roi_nms.cpp
    tongxing_util/src/module/util/cc_square_roi.cpp
    tongxing_util/src/module/util/cc_coordinate_scaling_offset2d.cpp
    tongxing_util/src/module/util/cc_sampling.cpp
    tongxing_util/src/module/util/cc_solve_pnp_angle.cpp
    tongxing_util/src/module/util/cc_image_align.cpp
    tongxing_util/src/module/util/cc_extend.cpp
    tongxing_util/src/module/occlusion_detector.cpp
    tongxing_util/src/module/cc_check_image.cpp
    tongxing_util/src/module/util/cc_const.cpp
    tongxing_util/src/module/svm_model.cpp
   )
   SET(WARMING_SRC
    src/warming/dms_calculate_warning_byd.cpp
    src/warming/dms_out_warning.cpp
    src/warming/dms_warming.cpp
   )
if(USE_MNN)
    if(${HTTP_INFERENCE} STREQUAL ON)
    SET(MODULE_INFERENCE_SRC
        ${MODULE_INFERENCE_SRC}
        tongxing_util/src/module/inference/tx_oax4600_http_inference.cpp
    )
    else()
    SET(MODULE_INFERENCE_SRC
        ${MODULE_INFERENCE_SRC}
        tongxing_util/src/module/inference/cc_mnn_model_inference.cpp
    )
    endif()
endif()
if(X9HP)
SET(MODULE_INFERENCE_SRC
    ${MODULE_INFERENCE_SRC}
     tongxing_util/src/module/inference/cc_tvm_model_inference.cpp
   )
SET(MODULE_PREPROCESS_SRC
   ${MODULE_PREPROCESS_SRC}
   tongxing_util/src/module/preprocess/cc_image_g2d_preprocessor.cpp
  )
SET(MODULE_PREPROCESS_SRC
  ${MODULE_PREPROCESS_SRC}
  tongxing_util/src/module/preprocess/cc_image_g2d_preprocessor.cpp
 ) 
endif()




if(USE_AMBA)
SET(MODULE_UTIL_SRC
    ${MODULE_UTIL_SRC}
    tongxing_util/src/module/util/cc_xc_square_roi.cpp
    tongxing_util/src/module/util/cc_xc_coordinate_scaling2d.cpp
   )
endif()

if(Linux_oax4600_aarch64)

SET(MODULE_INFERENCE_SRC
    ${MODULE_INFERENCE_SRC}
    tongxing_util/src/module/inference/tx_oax4600_inference.cpp
   )
SET(MODULE_INFERENCE_SRC
   ${MODULE_INFERENCE_SRC}
   thirdparty/OAX4600/isp/isp.c
#    thirdparty/OAX4600/isp/dbg_cmd.c
  )
endif()

SET(MODULE
    tongxing_util/src/module/cc_module.cpp
    tongxing_util/src/module/cc_module_grop.cpp
    ${MODULE_POSTPROCESS_SRC}
    ${MODULE_PREPROCESS_SRC}
    ${MODULE_INFERENCE_SRC}
    ${MODULE_UTIL_SRC}
    ${WARMING_SRC}
)
# if(Android_r18b)
# SET(INTERFACE_SRC
#     ${INTERFACE_SRC}
#     src/interface/com_tx_dms_sdk.cpp)
# endif()

IF (USE_ACTIVATE )

SET(ACTIVATE
    src/activate/activate.cpp
    # src/activate/CHttpClient.cpp
    src/activate/md5.cpp
    )
    SET( CMAKE_CXX_FLAGS "-D__USE_ACTIVATE__ ${CMAKE_CXX_FLAGS} " )
    SET( CMAKE_C_FLAGS "-D__USE_ACTIVATE__ ${CMAKE_C_FLAGS} ")
ENDIF()
SET(RESOURC_SRC
    ${CMAKE_CURRENT_BINARY_DIR}/resourc.cpp
    ${CMAKE_CURRENT_BINARY_DIR}/distraction_param.cpp
)

if(${HTTP_INFERENCE} STREQUAL ON)
SET(TONGXING_DMS_PD_SRC ${CMAKE_CURRENT_BINARY_DIR}/inference_result.pb.cc)
endif()
SET(TONGXING_DMS_PD_SRC ${TONGXING_DMS_PD_SRC} ${CMAKE_CURRENT_BINARY_DIR}/tongxing_dms.pb.cc)

SET(MEDIA_SERVER_SRC
    tongxing_util/src/media_server/cc_media_server.cpp
    tongxing_util/src/media_server/tx_media_server_interface.cpp
    ${TONGXING_DMS_PD_SRC}
    )

SET(DMS_SRC
    ${LIBYUV_SRC}
    ${INTERFACE_SRC}
    ${MODULE}
    ${UTIL_SRC}
    ${LOG_SYSTEM}
    ${MemoryManager_SRC}
    ${RESOURC_SRC}
    ${ACTIVATE}
    ${MODULE_UTIL_SRC}
    tongxing_util/src/media_server/tx_media_server.c
)

include_directories(
  ${CMAKE_CURRENT_BINARY_DIR}/
  ${OPENCV4_PATH}/include
  ${MNN_PATH}/include
  thirdparty/NumCpp
  src/interface
  src/process
  tongxing_util/src/module
  tongxing_util/src/util
  tongxing_util/src/log
  tongxing_util/src/memory_manager
  tongxing_util/src/media_server
  src/warming
  src/activate
  test
)

if(Linux-x86_64 OR Linux_aarch32 OR Linux_amba_aarch64 OR Linux_oax4600_aarch64)
    link_directories(
    ${OPENCV4_PATH}/lib
    ${MNN_PATH}/lib
    )
endif()

if(Android_r18b)
    link_directories(
    ${OPENCV4_PATH}/${ANDROID_ABI}/lib_static
    ${MNN_PATH}/${ANDROID_ABI}/lib_static
    )
endif()

if(X9HP)
    link_directories(
    ${SDK_LIB_PATH}
    ${OPENCV4_PATH}/${ANDROID_ABI}/lib_static
    )
endif()

if(Linux_oax4600_aarch64)
    include_directories(thirdparty/OAX4600/libcnnapi/include
                        thirdparty/OAX4600/libovt/include
                        thirdparty/OAX4600/isp)
    link_directories(thirdparty/OAX4600/libcnnapi/lib
                    thirdparty/OAX4600/libovt/lib)
    SET(OAX4600_LIB 
    libcnn.so
    libovt.so
    )
endif()

if(Linux-x86_64 OR Linux_TH518)
    if(${HTTP_INFERENCE} STREQUAL ON)
    add_library(tx_dms SHARED   ${DMS_SRC} ${TONGXING_DMS_PD_SRC})
    else()
    add_library(tx_dms SHARED   ${DMS_SRC})
    endif()
    target_link_libraries(tx_dms ${MNN_LIB}    ${OPENCV4_LIB} ${CURL_LIB} ${PROTOBUF_LIB} rt dl pthread -Wl,-wrap,cvAlloc -Wl,-wrap,cvFree_)

    add_library(tx_media_server SHARED   
            ${MEDIA_SERVER_SRC}
            ${TONGXING_DMS_PD_SRC}
    )
    target_link_libraries(tx_media_server   ${OPENCV4_LIB} ${ZLMK_LIB} ${PROTOBUF_LIB} )
    
elseif(Linux_aarch32)
    add_library(tx_dms SHARED   ${DMS_SRC})
    target_link_libraries(tx_dms ${MNN_LIB}    ${OPENCV4_LIB_STATIC}  rt dl pthread -Wl,-wrap,cvAlloc -Wl,-wrap,cvFree_)
elseif(Linux_amba_aarch64)
    add_library(tx_dms SHARED   ${DMS_SRC})
    target_link_libraries(tx_dms ${MNN_LIB} ${AMBA_SDK_LIB}   ${OPENCV4_LIB_STATIC}  rt dl pthread -Wl,-wrap,cvAlloc -Wl,-wrap,cvFree_)
elseif(Linux_oax4600_aarch64)
        add_library(tx_dms SHARED   ${DMS_SRC})
        target_link_libraries(tx_dms ${MNN_LIB}    ${OPENCV4_LIB_STATIC}  ${OAX4600_LIB} -lm   rt dl pthread )
        add_library(tx_media_server SHARED   
            ${MEDIA_SERVER_SRC}
            ${TONGXING_DMS_PD_SRC}
        )
        target_link_libraries(tx_media_server   ${PROTOBUF_LIB} ${OPENCV4_LIB_STATIC} ${ZLMK_LIB} )

endif()

if(Android_r18b)
    add_library(tx_dms SHARED   ${DMS_SRC})
    IF(ANDROID_ABI STREQUAL "arm64-v8a")
        target_link_libraries(tx_dms -Wl,--start-group ${MNN_LIB}  ${OPENCV4_LIB_STATIC} ${CUTIL_LIB} ${CURL_LIB} libtegra_hal.a libittnotify.a  -Wl,--end-group dl log z -Wl,-wrap,cvAlloc -Wl,-wrap,cvFree_)
    ELSEIF(ANDROID_ABI STREQUAL "armeabi-v7a")
        target_link_libraries(tx_dms -Wl,--start-group ${MNN_LIB}  ${OPENCV4_LIB_STATIC} ${CUTIL_LIB} ${CURL_LIB} libittnotify.a libcpufeatures.a  -Wl,--end-group dl log z -Wl,-wrap,cvAlloc -Wl,-wrap,cvFree_)
    endif()
endif()
   
if(X9HP)
add_library(calmcar_dms SHARED   ${DMS_SRC}  )
target_link_libraries(calmcar_dms ${SDK_LIB}   ${OPENCV4_LIB} ${CURL_LIB} stdc++  dl z )
endif()



# IF (CMAKE_BUILD_TYPE MATCHES "Debug")
# add_executable(test_module_grop  test/test_module_grop.cpp)
# target_link_libraries(test_module_grop  tx_dms   ${OPENCV4_LIB_STATIC})
# ENDIF()

# add_executable(test_calmcar_dms  test/test_calmcar_dms.cpp)
# target_link_libraries(test_calmcar_dms  tx_dms   ${OPENCV4_LIB_STATIC})
 if(USE_AMBA)
    add_executable(test_TX_dms2  
    test/cc_cam.cpp
    test/test_TX_dms2.cpp)
    target_link_libraries(test_TX_dms2  tx_dms ${AMBA_SDK_LIB}  ${OPENCV4_LIB_STATIC})

    add_executable(test_TX_dms3  
    test/cc_cam.cpp
    test/test_TX_dms3.cpp)
    target_link_libraries(test_TX_dms3  tx_dms ${AMBA_SDK_LIB}  ${OPENCV4_LIB_STATIC})


    add_executable(test_warm  
    test/test_zkhy_warm.cpp
    src/warming/dms_warm_data.cpp
    tongxing_util/src/log/CalmCarLog.cpp
    src/warming/dms_warming_zkhy_fatigue.cpp
    src/warming/dms_warming_zkhy_unsafe.cpp)
    target_link_libraries(test_warm  tx_dms  ${OPENCV4_LIB_STATIC})
elseif(X9HP)
	add_executable(test_calmcar_dms_ddaw  test/test_calmcar_dms_ddaw.cpp)
	target_link_libraries(test_calmcar_dms_ddaw  calmcar_dms   ${OPENCV4_LIB})

	add_executable(dms_sdk_auto  
	test/dms_sdk_auto.cpp
	calmcar_util/src/util/jsoncpp.cpp)
	target_link_libraries(dms_sdk_auto  calmcar_dms   ${OPENCV4_LIB})

	add_executable(dms_warning_auto  
	test/test_warning_tool.cpp
	calmcar_util/src/util/cc_timestamp.cpp
	src/warming/dms_calculate_warning_byd.cpp
	calmcar_util/src/util/jsoncpp.cpp)
	target_link_libraries(dms_sdk_auto  calmcar_dms   ${OPENCV4_LIB})
elseif(Linux_oax4600_aarch64)
    add_executable(test_tx_dms  test/oax4600/test_tongxing_dms_oax4600.cpp test/oax4600/cnn_mem.c test/oax4600/cnn.c)
    target_link_libraries(test_tx_dms  tx_dms dl ${OPENCV4_LIB_STATIC} ${OAX4600_LIB} ${ZLMK_LIB} ${PROTOBUF_LIB})

    add_executable(test_ddaw_tool test/oax4600/test_ddaw_tool.cpp  test/oax4600/cnn_mem.c test/oax4600/cnn.c)
    target_link_libraries(test_ddaw_tool  tx_dms dl  ${OAX4600_LIB})

    add_executable(test_oax4600_image_tool test/oax4600/test_tongxing_dms_oax4600_image_tool.cpp  test/oax4600/cnn_mem.c test/oax4600/cnn.c)
    target_link_libraries(test_oax4600_image_tool  tx_dms dl  ${OAX4600_LIB})
elseif(Linux-x86_64)
    if(${HTTP_INFERENCE} STREQUAL ON)
        add_executable(test_tongxing_dms_oax4600_http_image_tool  test/test_tongxing_dms_oax4600_http_image_tool.cpp )
        target_link_libraries(test_tongxing_dms_oax4600_http_image_tool  tx_dms dl )

        add_executable(test_tongxing_dms_oax4600_http  test/test_tongxing_dms_oax4600_http.cpp )
        target_link_libraries(test_tongxing_dms_oax4600_http  tx_dms dl )
    endif()
    add_executable(test_tx_dms  test/test_tongxing_dms.cpp ) 
    target_link_libraries(test_tx_dms  tx_dms dl)
    add_executable(test_tx_dms_tmp  test/test_tongxing_dms_tmp.cpp ${MEDIA_SERVER_SRC})
    target_link_libraries(test_tx_dms_tmp  tx_dms dl ${ZLMK_LIB} ${PROTOBUF_LIB})

    add_executable(test_dms_internal_postmortem  test/test_dms_internal_postmortem.cpp ${MEDIA_SERVER_SRC})
    target_link_libraries(test_dms_internal_postmortem  tx_dms dl ${ZLMK_LIB} ${PROTOBUF_LIB})
    # add_executable(test_calmcar_dms3  test/test_calmcar_dms3.c)
    # target_link_libraries(test_calmcar_dms3  calmcar_dms)

    # add_executable(test_ddaw_tool test/oax4600/test_ddaw_tool.cpp  )
    # target_link_libraries(test_ddaw_tool  tx_dms dl )

    # add_executable(test_tx_dms_internal_util  test/test_tongxing_dms_internal_util.cpp  tongxing_util/src/util/jsoncpp.cpp)
    # target_link_libraries(test_tx_dms_internal_util  tx_dms dl ${OPENCV4_LIB_STATIC} )

    # if(${INTERNAL_TEST_TOOL_MODE} STREQUAL ON)
    #   add_executable(test_tx_dms_internal_test_tool test/test_tongxing_dms_internal_test_tool.cpp  tongxing_util/src/util/jsoncpp.cpp )
    #   target_link_libraries(test_tx_dms_internal_test_tool tx_dms  dl ${OPENCV4_LIB} )
    # endif()
    add_executable(test_image_dir_tool test/test_image_dir_tool.cpp)
    target_link_libraries(test_image_dir_tool  tx_dms dl ${PROTOBUF_LIB})

endif()