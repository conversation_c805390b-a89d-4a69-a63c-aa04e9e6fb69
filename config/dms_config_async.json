{"models": [{"class_name": "oax4600_http_inference", "config": {"type": "inside", "use_async_mode": true, "use_service_pool": false, "http_url": "http://************:1180/FaceKeypoints", "model_url": "http://************:1180/tar"}, "name": "facekeypoint_infer_async", "description": "异步模式的人脸关键点检测，x86端发送后立即返回，不等待OAX4600响应"}, {"class_name": "oax4600_http_inference", "config": {"type": "inside", "use_async_mode": false, "use_service_pool": true, "http_url": "http://***********:1180/FaceDetection", "model_url": "http://***********:1180/tar"}, "name": "facedetection_infer_pool", "description": "服务池模式的人脸检测，用于测试环境的并发处理"}, {"class_name": "oax4600_http_inference", "config": {"type": "inside", "use_async_mode": false, "use_service_pool": false, "http_url": "http://************:1180/eye", "model_url": "http://************:1180/tar"}, "name": "eye_infer_legacy", "description": "传统同步模式的眼部检测，保持向后兼容"}], "mode_descriptions": {"async_mode": {"description": "异步发送模式 - x86端非阻塞发送", "use_case": "生产环境，需要x86端高吞吐量处理", "benefits": ["x86端立即返回，不等待OAX4600响应", "支持连续发送多张图片", "后台队列管理发送任务", "可选的结果回调机制"], "config": {"use_async_mode": true, "use_service_pool": false}}, "service_pool_mode": {"description": "服务池模式 - 多服务并发处理", "use_case": "测试环境，多个OAX4600实例可用", "benefits": ["并发处理多个请求", "负载均衡和故障转移", "提高整体吞吐量", "健康检查和监控"], "config": {"use_async_mode": false, "use_service_pool": true}}, "legacy_mode": {"description": "传统同步模式 - 向后兼容", "use_case": "现有生产环境，保持稳定", "benefits": ["完全向后兼容", "简单可靠", "易于调试", "无额外依赖"], "config": {"use_async_mode": false, "use_service_pool": false}}}, "deployment_recommendations": {"production": {"recommended_mode": "async_mode", "reason": "最大化x86端性能，避免等待OAX4600响应", "config_tips": ["设置合适的队列大小避免内存溢出", "根据网络状况调整发送线程数", "监控队列状态和发送成功率"]}, "testing": {"recommended_mode": "service_pool_mode", "reason": "测试多服务并发性能和负载均衡", "config_tips": ["确保所有IP地址的服务可用", "配置合适的超时和重试参数", "监控各服务实例的负载"]}, "development": {"recommended_mode": "legacy_mode", "reason": "简单调试，容易排查问题", "config_tips": ["使用单一IP地址", "启用详细日志", "关注响应时间和错误率"]}}}