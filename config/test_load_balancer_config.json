{"test_configuration": {"available_services": ["***********", "***********"], "port": 1180, "model_endpoints": ["FaceDetection", "FaceKeypoints", "eye", "Dms_PhoneSmoking"], "test_settings": {"max_concurrent_per_service": 2, "request_timeout_ms": 8000, "health_check_interval_ms": 30000}}, "test_scenarios": {"basic_test": {"description": "基础功能测试 - 验证负载均衡和时序输出", "num_images": 6, "submit_interval_ms": 500, "expected_behavior": "图片1,3,5分配给7.1，图片2,4,6分配给8.1，但输出严格按1,2,3,4,5,6顺序"}, "load_test": {"description": "负载测试 - 验证并发处理能力", "num_images": 10, "submit_interval_ms": 100, "expected_behavior": "快速提交，验证负载分布和处理能力"}, "sequential_test": {"description": "时序测试 - 验证结果按序输出", "num_images": 8, "submit_interval_ms": 200, "expected_behavior": "即使处理时间不同，结果必须按提交顺序输出"}}, "validation_criteria": {"load_balancing": {"description": "负载均衡验证", "criteria": ["两个服务实例都应该被使用", "负载分布相对均匀（允许±30%偏差）", "服务选择基于当前负载"]}, "sequential_output": {"description": "时序输出验证", "criteria": ["结果必须严格按sequence_id顺序输出", "不允许跳序或乱序", "即使某个服务处理较慢也要等待"]}, "model_processing": {"description": "模型处理验证", "criteria": ["每张图片的所有模型都在同一个服务上处理", "所有配置的模型都应该返回结果", "结果格式正确（protobuf格式）"]}, "error_handling": {"description": "错误处理验证", "criteria": ["服务不可用时自动切换", "超时请求正确处理", "错误信息清晰明确"]}}, "monitoring_metrics": {"performance": ["平均处理时间", "最大处理时间", "最小处理时间", "整体吞吐量"], "reliability": ["成功率", "超时率", "错误率", "服务可用性"], "load_balancing": ["服务使用分布", "负载均衡效果", "服务切换次数"]}, "expected_results": {"with_two_services": {"throughput_improvement": "相比单服务应有1.5-2倍提升", "load_distribution": "7.1和8.1使用率应相对均衡", "sequential_guarantee": "100%按序输出", "model_completeness": "每张图片所有模型结果完整"}}}