{"multi_model_inference": {"enabled": true, "required_models": ["FaceDetection", "FaceKeypoints", "eye", "Dms_PhoneSmoking"], "timeout_ms": 5000, "max_pending_results": 50, "enable_partial_results": true}, "model_services": {"FaceDetection": {"class_name": "oax4600_http_inference", "config": {"type": "inside", "http_url": "http://192.168.7.1:1180/FaceDetection", "model_url": "http://192.168.7.1:1180/tar", "priority": 1, "timeout_ms": 3000}}, "FaceKeypoints": {"class_name": "oax4600_http_inference", "config": {"type": "inside", "http_url": "http://192.168.8.1:1180/FaceKeypoints", "model_url": "http://192.168.8.1:1180/tar", "priority": 2, "timeout_ms": 4000}}, "eye": {"class_name": "oax4600_http_inference", "config": {"type": "inside", "http_url": "http://192.168.9.1:1180/eye", "model_url": "http://192.168.9.1:1180/tar", "priority": 3, "timeout_ms": 3500}}, "Dms_PhoneSmoking": {"class_name": "oax4600_http_inference", "config": {"type": "inside", "http_url": "http://192.168.10.1:1180/Dms_PhoneSmoking", "model_url": "http://192.168.10.1:1180/tar", "priority": 4, "timeout_ms": 4500}}}, "fallback_services": {"FaceDetection": ["http://192.168.11.1:1180/FaceDetection", "http://192.168.12.1:1180/FaceDetection"], "FaceKeypoints": ["http://192.168.11.1:1180/FaceKeypoints", "http://192.168.13.1:1180/FaceKeypoints"]}, "result_processing": {"enforce_sequential_output": true, "max_out_of_order_buffer": 10, "result_cache_size": 100, "enable_result_validation": true, "validation_rules": {"FaceDetection": {"min_tensor_count": 1, "expected_shapes": [[1, 4], [1, 1]]}, "FaceKeypoints": {"min_tensor_count": 1, "expected_shapes": [[1, 68, 2]]}}}, "monitoring": {"enable_performance_logging": true, "log_level": "INFO", "metrics": {"track_latency": true, "track_throughput": true, "track_error_rate": true, "track_timeout_rate": true}, "alerts": {"high_timeout_rate_threshold": 0.1, "high_error_rate_threshold": 0.05, "high_latency_threshold_ms": 8000}}, "deployment_modes": {"production": {"description": "生产环境配置 - 所有处理在OAX4600上同步执行", "multi_model_inference": {"enabled": true, "enforce_sequential_output": true, "timeout_ms": 8000}, "recommended_settings": {"use_fallback_services": true, "enable_result_validation": true, "log_level": "WARN"}}, "testing": {"description": "测试环境配置 - 可以使用多个服务实例", "multi_model_inference": {"enabled": true, "enforce_sequential_output": true, "timeout_ms": 5000}, "recommended_settings": {"use_fallback_services": true, "enable_performance_logging": true, "log_level": "INFO"}}, "development": {"description": "开发环境配置 - 详细日志和调试信息", "multi_model_inference": {"enabled": true, "enforce_sequential_output": false, "timeout_ms": 10000}, "recommended_settings": {"use_fallback_services": false, "enable_performance_logging": true, "log_level": "DEBUG"}}}, "usage_examples": {"basic_usage": {"description": "基本的多模型推理使用方法", "code_example": "MultiModelInferenceManager manager; manager.initialize(config); uint64_t seq_id = manager.processImage(input); CombinedInferenceResult result; manager.getResult(result);"}, "with_callback": {"description": "使用回调函数处理结果", "code_example": "aggregator.setResultCallback([](const CombinedInferenceResult& result) { processResult(result); });"}, "timeout_handling": {"description": "处理超时和部分结果", "code_example": "if (result.has_timeout) { handlePartialResult(result); } else { handleCompleteResult(result); }"}}}